/**************************************************
 * Created by nany<PERSON>ingfeng on 2018/9/29 19:09.
 **************************************************/
const config = require('whispered-build/webpack.entry.config')
const path = require('path')

const monaco_editor_chunks = require('@ekuaibao/monaco-editor/monaco-dirname')

config.patch.files({
  'system-production.js': { path: 'node_modules/systemjs/dist/system-production.js' },

  '导入档案模板.xlsx': { path: 'src/file/导入档案模板.xlsx' },
  '合作企业开户授权委托书.docx': { path: 'src/file/合作企业开户授权委托书.docx' },
  '导入员工模板.xlsx': { path: 'src/file/导入员工模板.xlsx' },
  '导入员工模板原生.xlsx': { path: 'src/file/导入员工模板原生.xlsx' },
  '导入员工模板WX.xlsx': { path: 'src/file/导入员工模板WX.xlsx' },
  '导入员工模板平台.xlsx': { path: 'src/file/导入员工模板平台.xlsx' },
  '银企联流水权限批量设置模板.xlsx': { path: 'src/file/银企联流水权限批量设置模板.xlsx' },
  '支付结果校验.xlsx': { path: 'src/file/支付结果校验.xlsx' },

  // 钉钉试用版 入口跳板
  'dingtalkTrial.html': { path: 'node_modules/@ekuaibao/springboard/dingtalkTrialWeb.html' },
  springboardMedia: { path: 'node_modules/@ekuaibao/springboard/static/media/', to: 'static/media/' },

  // `企业微信` 入口跳板.
  'qyweixin.html': { path: 'node_modules/@ekuaibao/springboard/qyweixinforapp.html' },
  'qyweixin-message.html': { path: 'node_modules/@ekuaibao/springboard/qyweixin-message.html' },
  'weixinauth.html': { path: 'node_modules/@ekuaibao/springboard/weixinauth.html' },
  'initializing.html': { path: 'node_modules/@ekuaibao/ekuaibao_404/initializing.html' },
  'feishu-message.html': { path: 'node_modules/@ekuaibao/springboard/fs-message.html' },
  // 支付宝卡包授权页面
  'alipaycardAuth.html': { path: 'node_modules/@ekuaibao/springboard/alipaycardAuth.html' },
  // 工行e政务跳板页
  'icbcRedirect.html': { path: 'node_modules/@ekuaibao/springboard/icbcRedirect.html' },
  // 单据入口跳转
  'billentry.html': { path: 'node_modules/@ekuaibao/springboard/billentry.html' },
  // oem方式授权跳转：例如现在的航信授权
  'applicationAuth.html': { path: 'node_modules/@ekuaibao/springboard/desktopAuth.html' },
  'auth-check-lock.svg': { path: 'src/images/auth-check-lock.svg' },
  'logo_icbc.png': { path: 'src/file/logo_icbc.png' },
  'toIE.html': { path: 'src/file/toIE.html' },
  'main.png': { path: 'src/file/images/main.png' },
  'button.png': { path: 'src/file/images/button.png' },
  'hose_favicon.ico': { path: 'src/file/hose_favicon.ico' },
  'more-DT.html': { path: 'src/file/more-DT.html' },
  'learn-more-DT.png': { path: 'src/file/learn-more-DT.png' },
  'more-KdCloud.html': { path: 'src/file/more-KdCloud.html' },
  'home5-auth.html': { path: 'src/file/home5-auth.html' },
  'home5-create-loading.html': { path: 'src/file/home5-create-loading.html' },
  'home5-create-loading.svg': { path: 'src/file/home5-create-loading.svg' },
  'home5-auth.svg': { path: 'src/file/home5-auth.svg' },
  'learn-more-KdCloud.jpeg': { path: 'src/file/learn-more-KdCloud.jpeg' },
  'logout.html': { path: 'src/file/logout.html' },
  'template-bg.png': { path: 'src/file/images/template-bg.png' },
  'errorInfo.html': { path: 'src/file/error.html' }, //错误页面，错误信息取url上面的errorMessage信息
  'load-failed.svg': { path: 'src/file/load-failed.svg' },

  i18n: { path: 'src/i18n/locales', to: 'locales/' },
  'logo.png': { path: 'src/images/standalone/logo.png' },
  'new-logo.png': { path: 'src/images/standalone/newLogo.png' },
  'dingdingProbe.js': { path: 'src/hosting/dingtalk/dingdingProbe.js' },

  [monaco_editor_chunks]: {
    path: 'node_modules/@ekuaibao/monaco-editor/' + monaco_editor_chunks,
    to: monaco_editor_chunks
  },

  ['vendor-polyfill.1.0.3.min.js']: 'node_modules/@ekuaibao/vendor-polyfill/vendor-polyfill.1.0.3.min.js',
  'pdfWorker.js': { path: 'src/file/pdfWorker.js' }
})

config.patch.vendors(['*.css', ['@ekuaibao/vendor-antd']])

const embedUrl = { path: 'https://assets.surveyplus.cn/surveylite/static/embed.min.js', attributes: { async: true } }
const euiUrl = 'https://statics.ekuaibao.com/eui/eui-web/3.9.5/eui.js'
const feishuSdks = ['https://statics.ekuaibao.com/sdk/feishu/h5-js-sdk-1.5.44.js']
const dingdingSdks = { path: 'https://o.alicdn.com/dingding/bird/index.js', attributes: { defer: true } }

let sdks = [embedUrl, euiUrl]

if (!!process.env.IS_STANDALONE) {
  sdks.push('system-production.js')
} else if (process.env.NODE_ENV === 'production') {
  sdks = sdks.concat(['system-production.js'])
}

config.patch.sdks({ ['*']: sdks, feishu: feishuSdks, 'fs-message': feishuSdks, dingtalk: [dingdingSdks] })

config.patch.defines({
  // PREFIX_URL: process.env.PREFIX_URL || '""',
  HUAWEI_LOGIN: JSON.stringify(process.env.HUAWEI_LOGIN || 'http://hw.qhose.com.cn/api/huawei/oauth/v1/login'),
  IS_STANDALONE: process.env.IS_STANDALONE || '""',
  IS_NOEKB: process.env.IS_NOEKB || '""',
  __DEV__: JSON.stringify(false),
  IS_HSFK: JSON.stringify(!!process.env.HSFK), // 是否合思费控
  IS_CMBC: JSON.stringify(!!process.env.CMBC), // 是否在民生环境
  IS_SMG: JSON.stringify(!!process.env.SMG), //SMG
  IS_SZJL: JSON.stringify(!!process.env.IS_SZJL), //苏州金龙
  IS_ZJZY: JSON.stringify(!!process.env.ZJZY),
  IS_OPG: JSON.stringify(!!process.env.OPG), //OPG
  IS_ICBC: JSON.stringify(!!process.env.ICBC), // 工行e政务
  SYNC_TEMPLATE_ORIGIN: JSON.stringify(process.env.SYNC_TEMPLATE_ORIGIN) // 初始化同步模板接口复制域名
})

config.patch.htmls({ '*': { template: './index.hbs', favicon: './hose_favicon.ico' } })

if (!!process.env.IS_STANDALONE) {
  config.patch.htmls({ '*': { template: './standalone-index.hbs' } })
}

//如果有noekb tag。那么重置其ico标志路径。
if (!!process.env.IS_NOEKB) {
  config.patch.htmls({ '*': { favicon: './anfavicon.ico' } })
}

//如果有ICBC tag。那么重置其ico标志路径。
if (!!process.env.ICBC) {
  config.patch.htmls({ '*': { favicon: './icbc_favicon.ico' } })
}

/*config.patch.imports([
  {
    libraryName: 'antd',
    style: false,
    libraryDirectory: 'es'
  }
])*/

config.patch.externals({
  // dxDataGrid 是一个体积较大的第三方表格库, 本身采用异步加载的方式单独加载, 无需经过 webpack 处理
  // 'devextreme/ui/data_grid': 'DevExpress.ui.dxDataGrid',
  // 'devextreme/bundles/dx.all': 'DevExpress',
  // 'devextreme/events': 'DevExpress.events',
  // 'devextreme/core/utils/common': 'DevExpress.utils.common'
})

if (!!process.env.HSFK) {
  config.patch.files({
    'hose_favicon.ico': { path: path.join(__dirname, 'src/file/favicon.hsfk.ico') }
  })

  config.patch.htmls({ '*': { favicon: path.join(__dirname, 'src/file/favicon.hsfk.ico') } })
} else if (!!process.env.CMBC) {
  config.patch.files({
    'hose_favicon.ico': { path: path.join(__dirname, 'src/file/favicon.cmbc.ico') }
  })

  config.patch.htmls({ '*': { favicon: path.join(__dirname, 'src/file/favicon.cmbc.ico') } })
}

const { entryName } = config.getEntryNameAndPluginName()

config.useClusterMaster({
  masterId: entryName,
  injected: [[``, { entryName: name => !['payment', 'shareexpinfo'].includes(name) }]]
})

console.log('NODE_ENV====' + process.env.NODE_ENV)

module.exports = config
