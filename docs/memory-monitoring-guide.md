# Excel导入内存监控使用指南

## 🎯 目标
实时监控Excel导入过程中的内存使用情况，精确定位内存泄漏和性能瓶颈。

## 📦 文件说明

### 1. MemoryMonitor.js
基础内存监控工具，提供：
- 实时内存使用监控
- 定时采样记录
- 内存使用报告生成
- CSV数据导出

### 2. ExcelImportMemoryTracker.js
专门针对Excel导入的内存追踪器，提供：
- 导入各阶段的内存追踪
- 关键操作的内存变化记录
- 详细的导入过程分析

### 3. ImportStepViewMemoryPatch.js
快速集成补丁，提供：
- 无侵入式的监控集成
- 方法包装和监控
- 全局快捷操作

## 🚀 快速使用

### 方法1: 快速集成（推荐）

在ImportStepView组件中添加：

```javascript
import { enableMemoryMonitoring } from '../../../utils/ImportStepViewMemoryPatch'

export default class ImportStepView extends PureComponent {
  componentDidMount() {
    // 启用内存监控
    enableMemoryMonitoring(this)
  }
  
  // 其他代码保持不变...
}
```

### 方法2: 手动集成

```javascript
import excelImportTracker from '../../../utils/ExcelImportMemoryTracker'

// 在handleChange方法开始处
handleChange = info => {
  const file = info[0]
  
  // 开始监控
  if (file && file.status !== 'error') {
    excelImportTracker.startTracking({
      name: file.name,
      size: file.size,
      type: file.type
    })
  }
  
  // 原有逻辑...
  
  // 在关键位置添加监控点
  if (file.status === 'done') {
    excelImportTracker.trackUploadComplete(file.response)
    
    // ArrayBuffer转换前
    excelImportTracker.trackArrayBufferConversionStart(response)
    const uint8 = Array.from(new Uint8Array(response))...
    excelImportTracker.trackArrayBufferConversionEnd(uint8)
    
    // JSON解析前后
    excelImportTracker.trackJSONParseStart(errorText)
    const obj = JSON.parse(errorText)
    excelImportTracker.trackJSONParseEnd(obj)
  }
}
```

## 🔍 监控功能

### 自动监控的关键节点
1. **导入开始** - 文件信息记录
2. **文件上传完成** - 响应数据大小
3. **ArrayBuffer转换** - 转换前后内存变化
4. **JSON解析** - 解析前后内存变化
5. **数据处理** - 业务逻辑处理过程
6. **数组操作** - slice、concat等操作
7. **状态更新** - setState调用
8. **导入结束** - 完整流程总结

### 实时监控数据
- **内存使用量** (MB)
- **内存使用率** (%)
- **DOM节点数量**
- **相对时间戳**
- **操作标签**

## 📊 查看监控结果

### 控制台输出
监控过程中会在控制台实时输出：
```
📊 [1250ms] JSON解析完成: {内存使用: "45.2MB", 使用率: "67.8%", DOM节点: 1234}
```

### 生成报告
监控结束后自动生成详细报告：
```
📋 内存使用报告
总监控时长: 3450ms
起始内存: 23.5MB
结束内存: 28.7MB
内存变化: +5.2MB
峰值内存: 52.3MB
最高使用率: 78.5%
最大内存增长: +15.2MB
增长时间段: ArrayBuffer转换开始 → ArrayBuffer转换完成
```

## 🛠️ 调试工具

### 浏览器控制台命令

```javascript
// 查看当前监控状态
window.getExcelMonitoringStatus()

// 手动结束监控
window.endExcelMemoryMonitoring()

// 导出监控数据为CSV
window.exportExcelMonitoringData()

// 强制垃圾回收（需要特殊启动参数）
window.forceGC()

// 直接访问监控器
window.memoryMonitor.generateReport()
window.excelImportTracker.getTrackingStatus()
```

### Chrome启动参数（用于垃圾回收）
```bash
chrome --js-flags="--expose-gc" --enable-precise-memory-info
```

## 📈 数据分析

### CSV导出字段
- 时间戳
- 相对时间(ms)
- 标签
- 使用内存(MB)
- 总内存(MB)
- 内存限制(MB)
- 使用率(%)
- DOM节点数

### 关键指标分析
1. **内存增长速度** - 查看哪个阶段内存增长最快
2. **峰值内存** - 确定最大内存需求
3. **内存泄漏** - 对比开始和结束内存
4. **性能瓶颈** - 找出耗时最长的操作

## ⚠️ 注意事项

1. **性能影响** - 监控本身会消耗少量性能，生产环境建议关闭
2. **浏览器兼容性** - performance.memory API在某些浏览器中可能不可用
3. **内存精度** - 浏览器内存统计可能有延迟和不精确
4. **垃圾回收** - 浏览器的垃圾回收机制可能影响监控结果

## 🔧 自定义配置

### 调整监控频率
```javascript
// 默认50ms，可以调整
memoryMonitor.startMonitoring(100) // 100ms间隔
```

### 添加自定义监控点
```javascript
excelImportTracker.trackCustomOperation('自定义操作', {
  customData: 'value'
})
```

### 设置内存警告阈值
```javascript
// 在MemoryMonitor.js中修改
if (memoryInfo.usagePercent > 80) { // 默认80%
  console.warn(`⚠️ 内存使用率过高: ${memoryInfo.usagePercent}%`)
}
```

## 🎯 使用场景

1. **问题复现** - 使用相同的大文件重现崩溃问题
2. **性能优化** - 对比优化前后的内存使用情况
3. **压力测试** - 测试不同大小文件的内存表现
4. **代码审查** - 验证代码修改对内存的影响
