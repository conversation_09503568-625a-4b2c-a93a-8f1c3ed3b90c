const config = require('./webpack.basic')
const fs = require('fs')
const configExternals = require('./configs/externals')
const HtmlReplaceWebpackPlugin = require('html-replace-webpack-plugin')

let { PROXY_URL, ENTRY, ENV_CONFIG } = require('./configs/dev.local')

if (fs.existsSync(__dirname + '/webpack.local.js')) {
  const { LOCAL_PROXY, LOCAL_ENTRY, LOCAL_ENV_CONFIG } = require('./webpack.local')
  PROXY_URL = LOCAL_PROXY
  ENTRY = LOCAL_ENTRY
  ENV_CONFIG = {...ENV_CONFIG, ...LOCAL_ENV_CONFIG}
}
console.log('🚀 ~ PROXY_URL:', PROXY_URL)
console.log('🚀 ~ ENTRY:', ENTRY)

config.output.filename('[name].js').chunkFilename('chunks-[name].js')
config.devtool('#cheap-module-eval-source-map')

require('@ekuaibao/vendor-lodash/patch')(config)
require('@ekuaibao/vendor-common/patch')(config)
require('@ekuaibao/vendor-whispered/patch')(config)
require('@ekuaibao/vendor-antd/patch')(config)
config.patch.vendors([
  '*.dev.js',
  ['@ekuaibao/vendor-lodash', '@ekuaibao/vendor-common', '@ekuaibao/vendor-whispered', '@ekuaibao/vendor-antd']
])

configExternals(config)

config.patch.entry(ENTRY)
const configStr = JSON.stringify(ENV_CONFIG)
config.plugin('EnvConfigPlugin').use(new HtmlReplaceWebpackPlugin([
  {
    pattern: '<ENV_CONFIG_PLACEHOLDER>',
    replacement: configStr 
  },
]))

config.devServer
  .host('0.0.0.0')
  .port(9999)
  .disableHostCheck(true)
  .set('hot', true)
  .stats('minimal')
  .set('hotOnly', true)
  .proxy([
    {
      context: ['/debug/**', '/api/**', '/static/**', '/bi/**', '/report/**', '/sockjs/**'],
      changeOrigin: true,
      secure: false,
      ws: true,
      target: PROXY_URL
    },
    {
      '/elem-web': {
        target: 'http://localhost:9900',
        pathRewrite: { '^/elem-web': '' }
      },
      '/travelone-web': {
        target: 'http://localhost:9966',
        pathRewrite: { '^/travelone-web': '' }
      },
      '/fxiaoke-web': {
        target: 'http://localhost:9977',
        pathRewrite: { '^/fxiaoke-web': '' }
      }
    }
  ])
  .publicPath('/')

if (fs.existsSync(__dirname + '/webpack.config.local.js')) {
  require('./webpack.config.local')(config)
}

module.exports = config.toConfig()
