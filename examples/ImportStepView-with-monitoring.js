// 这是一个示例，展示如何在现有的ImportStepView中添加内存监控
// 只需要添加几行代码即可启用完整的内存监控功能

import React, { PureComponent, Fragment } from 'react'
import { Button, Spin, Alert, Icon, Checkbox, Tooltip } from 'antd'
import { FilesUploader } from '@ekuaibao/uploader'
import { getDownloadTemplate, getUploadUrl } from './util'
import { EnhanceConnect } from '@ekuaibao/store'

// 🔥 添加内存监控导入
import { enableMemoryMonitoring, endMemoryMonitoring } from '../../../utils/ImportStepViewMemoryPatch'
import excelImportTracker from '../../../utils/ExcelImportMemoryTracker'

@EnhanceConnect(state => ({
  HSBC: state['@common'].powers.HSBC,
  KA_FOREIGN_ACCOUNT: state['@common'].powers.KA_FOREIGN_ACCOUNT
}))
export default class ImportStepView extends PureComponent {
  state = {
    step: 1,
    details: [],
    successCount: 0,
    importResult: null,
    errorFile: undefined,
    errorType: '',
    errorText: '',
    expenseStandardData: '',
    autoFix: false
  }

  // 🔥 在组件挂载时启用内存监控
  componentDidMount() {
    // 启用内存监控 - 只需要这一行代码！
    enableMemoryMonitoring(this)
    
    console.log('💡 内存监控已启用，可以在控制台使用以下命令：')
    console.log('- window.getExcelMonitoringStatus() // 查看监控状态')
    console.log('- window.endExcelMemoryMonitoring() // 手动结束监控')
    console.log('- window.exportExcelMonitoringData() // 导出监控数据')
    console.log('- window.forceGC() // 强制垃圾回收（需要特殊启动参数）')
  }

  // 🔥 在组件卸载时清理监控
  componentWillUnmount() {
    if (excelImportTracker.isTracking) {
      endMemoryMonitoring()
    }
  }

  // 原有的handleChange方法保持不变
  // 监控功能会自动包装这个方法
  handleChange = info => {
    let file = info[0]
    this.setState({ step: 2 })
    
    if (file.xhr && file.xhr.status === 204) {
      if (this.props.type === 'dimensionMap') {
        this.setState({ step: 3, errorType: '' })
        return
      } else {
        this.props.onOk && this.props.onOk({ state: 'loading', name: file.name })
      }
    }
    
    if (file.status === 'done') {
      let { xhr } = file
      let type = xhr && xhr.getResponseHeader('content-type')
      const { response } = file
      
      if (type === 'application/json') {
        // 🔥 可选：添加手动监控点来获取更详细的信息
        excelImportTracker.trackArrayBufferConversionStart(response)
        
        const uint8 = Array.from(new Uint8Array(response))
          .map(item => String.fromCharCode(item))
          .join('')
          
        excelImportTracker.trackArrayBufferConversionEnd(uint8)
        
        const errorText = decodeURIComponent(escape(uint8))
        
        try {
          excelImportTracker.trackJSONParseStart(errorText)
          const obj = JSON.parse(errorText)
          excelImportTracker.trackJSONParseEnd(obj)
          
          // 导入出错时，兼容服务器报错
          if (obj.errorCode) {
            this.setState({ step: 3, errorType: 'other', errorText: obj.errorMessage })
          } else {
            this.fnHandleChange(obj)
          }
        } catch (err) {
          this.setState({ step: 3, errorType: 'status' })
        }
      } else {
        // 处理文件下载的情况...
        let headerStr = decodeURIComponent((xhr && xhr.getResponseHeader('content-disposition')) || '')
        let errorList = []
        try {
          let h = headerStr.substring(headerStr.search(/(\[(\d+),(\d+)\]\.xlsx")$/))
          let l = h.split('.')
          let s = l[0]
          errorList = JSON.parse(s)
        } catch (err) {}
        
        let blob = new Blob([response], { type: 'application/octet-stream' })
        // ... 其他逻辑
      }
    }

    if (file.status === 'error') {
      // 错误处理也会被自动监控
      let uint8 = Array.from(new Uint8Array(file.response.errorResonse))
        .map(item => String.fromCharCode(item))
        .join('')
      let errorText = decodeURIComponent(escape(uint8))
      this.setState({ step: 3, errorType: 'status', errorText })
    }
  }

  // 原有的fnHandleChange方法也保持不变
  // 监控功能会自动包装这个方法
  fnHandleChange = obj => {
    const { type, flag, bus } = this.props
    
    if (type === 'detail') {
      const newDetails = obj.details
      let details = this.state.details.slice()
      const f = newDetails[0]
      const {
        billSpecification: { type }
      } = flag
      
      if (f && f.specificationId && !~f.specificationId.indexOf(type)) {
        this.setState({ step: 3, errorType: 'type' })
        return
      }
      
      // 🔥 可选：手动监控数组操作
      excelImportTracker.trackArrayOperation('concat', details, details.concat(newDetails))
      
      details = details.concat(newDetails)
      this.setState({ step: 3, details, errorType: '' })
      return
    }
    
    // ... 其他类型的处理逻辑
  }

  // 🔥 添加一个手动触发监控报告的方法（可选）
  generateMemoryReport = () => {
    if (window.memoryMonitor) {
      const report = window.memoryMonitor.generateReport()
      console.log('📊 手动生成的内存报告:', report)
      return report
    }
  }

  // 🔥 添加一个检查当前内存状态的方法（可选）
  checkCurrentMemory = () => {
    const status = window.getExcelMonitoringStatus()
    console.log('📈 当前内存状态:', status)
    return status
  }

  // 其他原有方法保持不变...
  handleChangeStep = step => {
    this.setState({ step })
  }

  handleFinish = () => {
    const { result } = this.fnGetKindsTypeMap()
    this.props.onOk && this.props.onOk(result)
  }

  // ... 其他方法

  render() {
    // 原有的render逻辑保持不变
    const { step } = this.state
    
    return (
      <Fragment>
        {/* 🔥 可选：添加内存监控状态显示 */}
        {process.env.NODE_ENV === 'development' && (
          <div style={{ 
            position: 'fixed', 
            top: 10, 
            right: 10, 
            background: 'rgba(0,0,0,0.8)', 
            color: 'white', 
            padding: '5px 10px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: 9999
          }}>
            <div>内存监控: {excelImportTracker.isTracking ? '🟢 运行中' : '🔴 未启动'}</div>
            <div>
              <button onClick={this.checkCurrentMemory} style={{marginRight: '5px'}}>
                检查内存
              </button>
              <button onClick={this.generateMemoryReport}>
                生成报告
              </button>
            </div>
          </div>
        )}
        
        {/* 原有的UI组件 */}
        <div className="invoice-step-wrapper">
          {step === 2 && (
            <div className="load">
              <Spin tip="Excel校验并导入中，内存监控运行中..." />
            </div>
          )}
          {/* ... 其他UI */}
        </div>
      </Fragment>
    )
  }
}

/*
🎯 使用说明：

1. 只需要添加几行导入和componentDidMount中的一行代码即可启用完整监控
2. 所有关键操作都会被自动监控，无需修改现有业务逻辑
3. 可以通过浏览器控制台查看实时监控数据
4. 开发环境下会显示监控状态指示器

🔍 监控的内容：
- 文件上传完成时的内存状态
- ArrayBuffer转换前后的内存变化
- JSON解析前后的内存变化
- 数据处理过程中的内存变化
- 数组操作的内存影响
- setState调用的内存影响

📊 查看监控结果：
- 控制台会实时输出内存使用情况
- 导入完成后自动生成详细报告
- 可以导出CSV文件进行进一步分析

⚠️ 注意：
- 生产环境建议移除监控代码或添加环境判断
- 监控本身会消耗少量性能
- 某些浏览器可能不支持performance.memory API
*/
