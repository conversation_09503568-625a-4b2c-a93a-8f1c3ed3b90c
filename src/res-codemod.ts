/*****************************************
 * 更新于 Wed Jul 08 2020 18:56:29 GMT+0800 (GMT+08:00)
 ******************************************/
import loadable from '@loadable/component'
export default [
  {
    resource: '@elements',
    value: {
      ['data-grid-v2/StaffFilter']: loadable(() => import('./elements/data-grid/StaffFilter')),
      ['data-grid-v2/DepartmentFilter']: loadable(() => import('./elements/data-grid/DepartmentFilter')),
      ['ekbIcon']: loadable(() => import('./elements/ekbIcon')),
      ['ekbIconPark']: loadable(() => import('./elements/ekbIconPark')),
      ['puppet/personnelList/NewPersonnelList']: loadable(() =>
        import('./elements/puppet/personnelList/NewPersonnelList')
      ),
      ['logo/logo-for-login']: loadable(() => import('./elements/logo/logo-for-login')),
      ['feeType-tree-select']: loadable(() => import('./elements/feeType-tree-select')),
      ['feeType-tree-select-eui']: loadable(() => import('./elements/feeType-tree-select-eui')),
      ['feetype-tree/feetype-tree-search-modal']: loadable(() =>
        import('./elements/feetype-tree/feetype-tree-search-modal')
      ),
      ['modal/sync-staffs-modal']: loadable(() => import('./elements/modal/sync-staffs-modal')),
      ['export-excel/export-excel-modal']: loadable(() => import('./elements/export-excel/export-excel-modal')),
      ['async-export-modal']: loadable(() => import('./elements/async-export-modal')),
      ['city/SelectCityModal']: loadable(() => import('./elements/city/SelectCityModal')),
      ['transfer/TransferModal']: loadable(() => import('./elements/transfer/TransferModal')),
      ['del-confirm-modal']: loadable(() => import('./elements/del-confirm-modal')),
      ['staff-selector/staff-selector-modal']: loadable(() => import('./elements/staff-selector/staff-selector-modal')),
      ['staff-selector-v2/staff-selector-modal']: loadable(() =>
        import('./elements/staff-selector-v2/staff-selector-modal')
      ),
      ['select-member-modal']: loadable(() => import('./elements/select-member-modal')),
      ['staff-selector-field']: loadable(() => import('./elements/custom-fields/StaffSelectorField')),
      ['select-tree-modal']: loadable(() => import('./elements/select-tree-modal')),
      ['SelectTreeModalV2']: loadable(() => import('./elements/SelectTreeModalV2')),
      ['LoadableSelectTreeModal']: loadable(() => import('./elements/LoadableSelectTreeModal')),
      ['CarouselInvoiceReviewer/CarouselInvoiceReviewer']: loadable(() =>
        import('./elements/CarouselInvoiceReviewer/CarouselInvoiceReviewer')
      ),
      ['budget-detail/budget-detail']: () => import('./elements/budget-detail/budget-detail'),
      ['payee-account/account-change-log']: loadable(() => import('./elements/payee-account/account-change-log')),
      ['print-modal']: loadable(() => import('./elements/print-modal')),
      ['dept-select-modal']: loadable(() => import('./elements/dept-select-modal')),
      ['phone-verification/verification-phone']: loadable(() =>
        import('./elements/phone-verification/verification-phone')
      ),
      ['flow-allow-modal']: loadable(() => import('./elements/flow-allow-modal')),
      ['AddCreditNote']: loadable(() => import('./elements/addCreditNote')),
      ['payPlan/table/EditRow']: loadable(() => import('./elements/payPlan/table/EditRow')),
      ['payPlan/table/EditCell']: loadable(() => import('./elements/payPlan/table/EditCell')),
      ['puppet/Money']: loadable(() => import('./elements/puppet/Money')),
      ['puppet/Currency']: loadable(() => import('./elements/puppet/Currency')),
      ['puppet/details/DetailItemExpandFields']: loadable(() =>
        import('./elements/puppet/details/DetailItemExpandFields')
      ),
      ['puppet/Guide/OnceItemTip']: loadable(() => import('./elements/puppet/Guide/OnceItemTip')),
      ['puppet/Guide/OncePopoverTip']: loadable(() => import('./elements/puppet/Guide/OncePopoverTip')),
      ['batch-components/BatchDimensionSelectComponent']: loadable(() =>
        import('./elements/batch-components/BatchDimensionSelectComponent')
      ),
      ['tag-selector']: loadable(() => import('./elements/tag-selector')),
      ['puppet/view-item/SelectItem']: loadable(() => import('./elements/puppet/view-item/SelectItem')),
      ['SummaryComponent']: loadable(() => import('./elements/SummaryComponent')),
      ['data-grid/DataGridWrapper']: loadable(() => import('./elements/data-grid/DataGridWrapper')),
      ['data-grid-v2/HeaderWrapper']: loadable(() => import('./elements/data-grid-v2/HeaderWrapper')),
      ['data-grid-v2/LoaderWithLegacyData']: require('./elements/data-grid-v2/LoaderWithLegacyData').default,
      ['data-grid-v2/CustomSearch']: loadable(() => import('./elements/data-grid-v2/element/CustomSearch')),
      ['data-grid-v2/CreateColumn']: require('./elements/data-grid-v2/CreateColumn'),
      ['ETabs']: require('./elements/ETabs').default,
      ['ETabsDrop']: loadable(() => import('./elements/ETabsDrop')),
      ['EKBContainer.Tab']: loadable(() => import('./elements/ekb-container/Topbar/Topbar')),
      ['Topbar.title']: loadable(() => import('./elements/ekb-container/Topbar/Title')),
      ['EKBContainer']: loadable(() => import('./elements/ekb-container/index')),
      ['attachment-component/AttachmentComponent']: loadable(() =>
        import('./elements/attachment-component/AttachmentComponent')
      ),
      ['MoneyView/MoneyView']: loadable(() => import('./elements/MoneyView/MoneyView')),
      ['signature/SignaturePanel']: loadable(() => import('./elements/signature/SignaturePanel')),
      ['signature/ApproveSignature']: loadable(() => import('./elements/signature/ApproveSignature')),
      ['Animation/Animation']: loadable(() => import('./elements/Animation/Animation')),
      ['payment-form-field/select-payment-method']: loadable(() =>
        import('./elements/payment-form-field/select-payment-method')
      ),
      ['expense-export']: loadable(() => import('./elements/expense-export')),
      ['search-input']: loadable(() => import('./elements/search-input')),
      ['puppet/attachment']: loadable(() => import('./elements/puppet/attachment')),
      ['puppet/details/Details']: loadable(() => import('./elements/puppet/details/Details')),
      ['payPlan/PayPlayTableWrapper']: loadable(() => import('./elements/payPlan/PayPlayTableWrapper')),
      ['payee-account/account-list-item']: loadable(() => import('./elements/payee-account/account-list-item')),
      ['bank-card/BankCard']: loadable(() => import('./elements/bank-card/BankCard')),
      ['PayAccountCard']: loadable(() => import('./elements/payee-account/PayAccountCard')),
      ['payee-account/AccountNoInput']: loadable(() => import('./elements/payee-account/AccountNoInput')),
      ['data-grid-v2/LineClamp']: loadable(() => import('./elements/data-grid-v2/LineClamp')),
      ['data-grid-v2/SimpleTableWithLoader']: loadable(() => import('./elements/data-grid-v2/SimpleTableWithLoader')),
      ['icon']: loadable(() => import('./elements/icon')),
      ['EkbHighLighter']: loadable(() => import('./elements/EkbHighLighter')),
      ['logo/logo']: loadable(() => import('./elements/logo/logo')),
      ['puppet/KeelSingleViewHeader']: loadable(() => import('./elements/puppet/KeelSingleViewHeader')),
      ['puppet/KeelViewBody']: loadable(() => import('./elements/puppet/KeelViewBody')),
      ['puppet/KeelBasic']: require('./elements/puppet/KeelBasic').default,
      ['InvoiceCard/OpenBillInfoCard']: loadable(() => import('./elements/InvoiceCard/OpenBillInfoCard')),
      ['ImmersiveToolBar']: loadable(() => import('./elements/ImmersiveToolBar')),
      ['DataLinkTable/DataLinkTableWrapper']: loadable(() => import('./elements/DataLinkTable/DataLinkTable')),
      ['invoice-form/RefInvoice']: loadable(() => import('./elements/invoice-form/RefInvoice')),
      ['InvoiceCard/InvoiceItem']: loadable(() => import('./elements/InvoiceCard/InvoiceItem')),
      ['feetype-icon']: loadable(() => import('./elements/feetype-icon')),
      ['data-grid/EKBDataGridWrapper']: loadable(() => import('./elements/data-grid/EKBDataGridWrapper')),
      ['data-grid-v2/LightingMode']: loadable(() => import('./elements/data-grid-v2/LightingMode')),
      ['data-grid-v2/NewLightingMode']: loadable(() => import('./elements/data-grid-v2/NewLightingMode')),
      ['input-selector']: loadable(() => import('./elements/input-selector')),
      ['transfer-tree']: loadable(() => import('./elements/transfer-tree')),
      ['puppet/tree-menu/tree-menu']: loadable(() => import('./elements/puppet/tree-menu/tree-menu')),
      ['ImmersiveToolBar/index']: loadable(() => import('./elements/ImmersiveToolBar/index')),
      ['SearchBarForTitle/SearchBarForTitle']: loadable(() => import('./elements/SearchBarForTitle/SearchBarForTitle')),
      ['SearchBarForTitle/NoResultViewForSearch']: loadable(() =>
        import('./elements/SearchBarForTitle/NoResultViewForSearch')
      ),
      ['member-edit/member-edit-view']: loadable(() => import('./elements/member-edit/member-edit-view')),
      ['puppet/TreeSelectSingle']: loadable(() => import('./elements/puppet/TreeSelectSingle')),
      ['currency/currency-dropdown']: loadable(() => import('./elements/currency/currency-dropdown')),
      ['input-tags']: loadable(() => import('./elements/input-tags')),
      ['feetype-tree-menu']: loadable(() => import('./elements/feetype-tree-menu')),
      ['select/ComplexSelect']: loadable(() => import('./elements/select/ComplexSelect')),
      ['select/ComplexSelectV2']: loadable(() => import('./elements/select/ComplexSelectV2')),
      ['select/ComplexSelectMultiple']: loadable(() => import('./elements/select/ComplexSelectMultiple')),
      ['puppet/Ref']: loadable(() => import('./elements/puppet/Ref')),
      ['puppet/IntelligentApprovalTags']: loadable(() => import('./elements/puppet/IntelligentApprovalTags')),
      ['ekbc-basic/breadcrumb']: loadable(() => import('./elements/ekbc-basic/breadcrumb')),
      ['payment-form-field/select-bank-field']: loadable(() =>
        import('./elements/payment-form-field/select-bank-field')
      ),
      ['payment-form-field/select-area-field']: loadable(() =>
        import('./elements/payment-form-field/select-area-field')
      ),
      ['payment-form-field/select-branch-field']: loadable(() =>
        import('./elements/payment-form-field/select-branch-field')
      ),
      ['puppet/RefEnum']: loadable(() => import('./elements/puppet/RefEnum')),
      ['ekbc-basic/active-wrapper/active-wrapper']: loadable(() =>
        import('./elements/ekbc-basic/active-wrapper/active-wrapper')
      ),
      ['FilterPopover']: loadable(() => import('./elements/FilterPopover')),
      ['edit-table-elements/StandardDateSelectDrodown/DateSelectDrodown']: loadable(() =>
        import('./elements/edit-table-elements/StandardDateSelectDrodown/DateSelectDrodown')
      ),
      ['city/CityComponent']: loadable(() => import('./elements/city/CityComponent')),
      ['select-member-role-department']: loadable(() => import('./elements/select-member-role-department')),
      ['MarketADBanner/MarketADBanner']: require('./elements/MarketADBanner/MarketADBanner').default,
      ['tag-selector-edit']: loadable(() => import('./elements/tag-selector-edit')),
      ['immersiveBtn/ImmersiveBtn']: require('./elements/immersiveBtn/ImmersiveBtn').default,
      ['immersiveBtnNew/ImmersiveBtn']: require('./elements/immersiveBtnNew/ImmersiveBtn').default,
      ['dataGrid/DataGrid']: loadable(() => import('./elements/dataGrid/DataGrid')),
      ['ScreeningDropDown/ScreeningDropDown']: loadable(() => import('./elements/ScreeningDropDown/ScreeningDropDown')),
      ['print-view']: loadable(() => import('./elements/print-view')),
      ['namePopover/PopoverWrapper']: loadable(() => import('./elements/namePopover/PopoverWrapper')),
      ['EKBPagination']: loadable(() => import('./elements/EKBPagination')),
      ['circle-chart']: loadable(() => import('./elements/circle-chart')),
      ['puppet/data-interconnection-config/DataConfig']: loadable(() =>
        import('./elements/puppet/data-interconnection-config/DataConfig')
      ),
      ['transfer/SortableTransferTable']: loadable(() => import('./elements/transfer/SortableTransferTable')),
      ['transfer/SortableTransfer']: loadable(() => import('./elements/transfer/SortableTransfer')),
      ['search-dropdown']: loadable(() => import('./elements/search-dropdown')),
      ['puppet/entity-detail/entity-detail-fieldview']: loadable(() =>
        import('./elements/puppet/entity-detail/entity-detail-fieldview')
      ),
      ['member-edit/DeptEditView']: loadable(() => import('./elements/member-edit/DeptEditView')),
      ['radio-group/EKBRadioGroup']: loadable(() => import('./elements/radio-group/EKBRadioGroup')),
      ['warningPopup/WarningPopup']: loadable(() => import('./elements/warningPopup/WarningPopup')),
      ['ConditionalEditComponent/ConditionalEditWrapper']: loadable(() =>
        import('./elements/ConditionalEditComponent/ConditionalEditWrapper')
      ),
      ['ekbc-business/bills/expense-flow-config']: loadable(() =>
        import('./elements/ekbc-business/bills/expense-flow-config')
      ),
      ['invoice-card']: loadable(() => import('./elements/invoice-card')),
      ['StaffsTagSelect']: loadable(() => import('./elements/StaffsTagSelect')),
      ['Skeleton']: loadable(() => import('./elements/Skeleton/Skeleton')),
      ['PayerInfoList']: loadable(() => import('./elements/payerInfo')),
      ['ColorPicker']: loadable(() => import('./elements/ColorPicker')),
      ['puppet/FakeInput']: loadable(() => import('./elements/puppet/FakeInput')),
      ['CurrencyMoney']: loadable(() => import('./elements/currency/currency-money')),
      ['FilterDateRange']: loadable(() => import('./elements/filter-date-range/FilterDateRange')),
      ['travel/card']: loadable(() => import('./elements/new-travel-plan-item')),
      ['StaffCredential']: loadable(() => import('./components/dynamic/StaffCredential')),
      ['PersonnelList']: loadable(() => import('./elements//puppet/personnelList/PersonnelList')),
      ['Brow']: loadable(() => import('./elements/brow/Brow')),
      ['BrowModal']: loadable(() => import('./elements/brow/BrowModal')),
      ['list-menu']: loadable(() => import('./elements/ListMenu/list-menu')),
      ['table-wrapper']: loadable(() => import('./elements/data-grid-v2/TableWrapper')),
      ['staff-selector-async/SelectStaffDeptAsyncModal']: loadable(() =>
        import('./elements/staff-selector-async/SelectStaffDeptAsyncModal')
      ),
      ['UploadButton']: loadable(() => import('./elements/ekbc-business/upload-button')),
      ['NullView']: loadable(() => import('./elements/NullView')),
      ['ApprovalAmountConfig']: loadable(() => import('./elements/ApprovalAmountConfig')),
      ['specification-tree-select']: loadable(() => import('./elements/specification-tree-select')),
      ['HoseTable']: loadable(() => import('./elements/HoseTable/index')),
      ['HoseTable/toolBar']: loadable(() => import('./elements/HoseTable/toolBar/index')),
      ['HoseTable/TableResize']: loadable(() => import('./elements/HoseTable/TableResize')),
      ['IntelligentFillingModal']: loadable(() => import('./elements/payee-account/IntelligentFillingModal')),
      ['image-cropper']: loadable(() => import('./elements/image-cropper')),
      ['user-badge']: loadable(() => import('./elements/profile-card/user-badge/index')),
    }
  },
  {
    resource: '@components',
    value: {
      ['layout/ExceedStandardRiskForField']: loadable(() => import('./components/layout/ExceedStandardRiskForField')),
      ['dynamic/CityPickerComponent']: loadable(() => import('./components/dynamic/CityPickerComponent')),
      ['dynamic/CityPickerComponentNew']: loadable(() => import('./components/dynamic/city-picker-new')),
      ['dynamic/RefDepartmentCP']: loadable(() => import('./components/dynamic/RefDepartmentCP')),
      ['dynamic/RefCP']: loadable(() => import('./components/dynamic/RefCP')),
      ['dynamic/EditTableWrapper']: loadable(() =>
        import('./components/dynamic/dataLinkEdit/DataLinkEditTable/EditTableContainer')
      ),
      ['mc-authority/button']: loadable(() => import('./components/mc_authority_button')),
      ['mc-authority/enum']: require('./components/mc_authority_button/enum').default
    }
  },
  {
    resource: '@ekb-components',
    value: {
      ['business/breadcrumb']: loadable(() => import('./ekb-components/business/breadcrumb')),
      ['business/breadcrumb/index']: loadable(() => import('./ekb-components/business/breadcrumb/index')),
      ['business/breadcrumb/Title']: loadable(() => import('./ekb-components/business/breadcrumb/Title')),
      ['business/breadcrumb/Breadcrumb']: loadable(() => import('./ekb-components/business/breadcrumb/Breadcrumb')),
      ['base/puppet/EKBSelect']: loadable(() => import('./ekb-components/base/puppet/EKBSelect')),
      ['base/form/form-item']: loadable(() => import('./ekb-components/base/form/form-item')),
      ['base/puppet/EKBTreeSelect']: loadable(() => import('./ekb-components/base/puppet/EKBTreeSelect')),
      ['business/ekbsteps']: loadable(() => import('./ekb-components/business/ekbsteps')),
      ['business/role-selected/tags']: loadable(() => import('./ekb-components/business/role-selected/tags')),
      ['base/puppet/EKBCheckBox']: loadable(() => import('./ekb-components/base/puppet/EKBCheckBox')),
      ['base/puppet/EKBInput']: loadable(() => import('./ekb-components/base/puppet/EKBInput')),
      ['business/card/data-interconnection']: loadable(() =>
        import('./ekb-components/business/card/data-interconnection')
      ),
      ['base/form/form-text']: loadable(() => import('./ekb-components/base/form/form-text'))
    }
  },
  {
    resource: '@elements',
    value: {
      ['specificationIcon']: require('./components/internal/SpecificationIcon').default,
      ['enhance/enhance-form-create']: require('./elements/enhance/enhance-form-create').default,
      ['feetype-icons']: require('./elements/feetype-icons').default,
      ['payPlan/table/table.store']: require('./elements/payPlan/table/table.store'),
      ['payPlan/types']: require('./elements/payPlan/types'),
      ['payPlan/helper/formatTableData']: require('./elements/payPlan/helper/formatTableData'),
      ['payPlan/table/ColumnFormat']: require('./elements/payPlan/table/ColumnFormat'),
      ['payPlan/helper/tableHelper']: require('./elements/payPlan/helper/tableHelper'),
      ['data-grid-v2/withLoader']: require('./elements/data-grid-v2/withLoader').default,
      ['data-grid-v2/LocaleProvider']: require('./elements/data-grid-v2/LocaleProvider'),
      ['ekbc-basic/layout/Box']: require('./elements/ekbc-basic/layout/Box'),
      ['data-grid/columnsUtil']: require('./elements/data-grid/columnsUtil'),
      ['data-grid-v2/DataGrid.module.less']: require('./elements/data-grid-v2/DataGrid.module.less'),
      ['puppet/MoneyNzh']: require('./elements/puppet/MoneyNzh').default,
      ['InvoiceCard/FormatInvoiceData']: require('./elements/InvoiceCard/FormatInvoiceData'),
      ['data-grid/columnOthers']: require('./elements/data-grid/columnOthers'),
      ['data-grid/fetchFixer']: require('./elements/data-grid/fetchFixer'),
      ['DataLinkTable/tableUtil']: require('./elements/DataLinkTable/tableUtil'),
      ['payPlan/helper/fnInitalValue']: require('./elements/payPlan/helper/fnInitalValue'),
      ['payPlan/table/credit.store']: require('./elements/payPlan/table/credit.store'),
      ['edit-table-elements/StandardDateSelectDrodown/utils/types']: require('./elements/edit-table-elements/StandardDateSelectDrodown/utils/types'),
      ['edit-table-elements/TableSelectItemView']: require('./elements/edit-table-elements/TableSelectItemView'),
      ['edit-table-elements/fnTreeFilterAvailable']: require('./elements/edit-table-elements/fnTreeFilterAvailable'),
      ['payee-account/utils']: require('./elements/payee-account/utils'),
      ['configure/config']: require('./elements/configure/config'),
      ['configure/bill-drawer']: require('./elements/configure/bill-drawer'),
      ['feeDetailViewList/Related']: require('./elements/feeDetailViewList/Related'),
      ['transfer/images/empty.svg']: require('./elements/transfer/images/empty.svg'),
      ['payee-account/account-list-consts']: require('./elements/payee-account/account-list-consts'),
      ['member-edit/util']: require('./elements/member-edit/util'),
      ['ConditionalEditComponent/Utils']: require('./elements/ConditionalEditComponent/Utils'),
      ['puppet/details/FormatDateUtils']: require('./elements/puppet/details/FormatDateUtils'),
      ['edit-table-elements/CurrencySetting']: require('./elements/edit-table-elements/CurrencySetting'),
      ['UniversalComponent']: require('./elements/universal/StandardVersionComponent'),
      ['StandardVersionComponent']: require('./elements/universal/StandardVersionComponent'),
      ['conditionComponents']: require('./elements/ekb-container/condition-components'),
      ['Types/ContextUtils']: require('./components/dynamic/types'),
      ['staffs/staffShowFn']: require('./elements/staffs/staffShowFn').default,
      ['elements/utilFn']: require('./elements/utilFn'),
      ['EKBContainer.TitleWrapper']: require('./elements/ekb-container/TitleWrapper'),
      ['HoseTable/filter']: require('./elements/HoseTable/filter/index').default,
      ['profile-card']: require('./elements/profile-card').default,
      ['LoadingError']: require('./elements/LoadingError'),
      ['name-cell']: require('./elements/name-cell'),
    }
  },
  {
    resource: '@components',
    value: {
      ['reports']: require('./components/reports').default,
      ['index.editable']: require('./components/index.editable'),
      ['index.entitydetail']: require('./components/index.entitydetail'),
      ['index.interconnectal']: require('./components/index.interconnectal'),
      ['index.internal']: require('./components/index.internal'),
      ['index.readonly']: require('./components/index.readonly'),
      ['index.trips.readonly']: require('./components/index.trips.readonly'),
      ['layout/FormWrapper']: require('./components/layout/FormWrapper'),
      ['consts']: require('./components/consts'),
      ['utils/fnCurrencyObj']: require('./components/utils/fnCurrencyObj'),
      ['utils/fnPredefine4Date']: require('./components/utils/fnPredefine4Date'),
      ['utils/fnThousandBitSeparator']: require('./components/utils/fnThousandBitSeparator'),
      ['utils/fnFilterPaymentChannel']: require('./components/utils/fnFilterPaymentChannel'),
      ['validator/validator']: require('./components/validator/validator'),
      ['interconnectal/InterconInput']: require('./components/interconnectal/InterconInput'),
      ['utils/fnCheckPayerInfo']: require('./components/utils/fnCheckPayerInfo'),
      ['utils/fnFormartDatalinkData']: require('./components/utils/fnFormartDatalinkData'),
      ['utils/getInitialValue']: require('./components/utils/getInitialValue').default,
      ['utils/fnEntityDataParse']: require('./components/utils/fnEntityDataParse'),
      ['utils/utilFunctionsParams']: require('./components/utils/utilFunctionsParams'),
      ['utils/questionnaireConfig']: require('./components/utils/questionnaireConfig'),
      ['interconnectal/checkboxLabel']: require('./components/interconnectal/checkboxLabel'),
      ['interconnectal/CustomDimensionFilter.isValidCustomDimensions']: require('./components/interconnectal/CustomDimensionFilter.isValidCustomDimensions'),
      ['index.budget']: require('./components/index.budget'),
      ['index.supplier']: require('./components/index.supplier'),
      ['skeleton']: require('./elements/skeletonList/index').default,
      ['tooltip/ellipsis']: require('./components/TooltipEllipsis/index').default,
      ['select-staff-simple']: require('./elements/puppet/staff-select-simple/index').default,
      ['select-staff-heavy/single-select']: require('./elements/puppet/staff-select-heavy/staff-select-single').default,
      ['tag-show']: require('./elements/puppet/tag-show').default,
      ['payee-account/PayeeAccountCard']: require('./elements/payee-account/payee-account-card').default,
      ['select-staff-heavy/staff-select-range']: require('./elements/puppet/staff-select-heavy/index').default,
      ['comment/NCommentComponent']: require('./plugins/bills/layers/comment/NCommentComponent').default
    }
  },
  {
    resource: '@images',
    value: {
      ['add.svg']: require('./images/add.svg'),
      ['delete.svg']: require('./images/delete.svg'),
      ['brand']: require('./images/brand'),
      ['changjie-logo.png']: require('./images/changjie-logo.png'),
      ['other-logo.svg']: require('./images/other-logo.svg'),
      ['auth-check-user.svg']: require('./images/auth-check-user.svg'),
      ['auth-check-user-lock.svg']: require('./images/auth-check-user-lock.svg'),
      ['auth-check-invalid.svg']: require('./images/auth-check-invalid.svg'),
      ['auth-manage-buy.svg']: require('./images/auth-manage-buy.svg'),
      ['refresh.svg']: require('./images/refresh.svg'),
      ['budget-tip.svg']: require('./images/budget-tip.svg'),
      ['budget_success.svg']: require('./images/budget_success.svg'),
      ['budget_draft_or_edit.svg']: require('./images/budget_draft_or_edit.svg'),
      ['home-arrow-right.svg']: require('./images/home-arrow-right.svg'),
      ['custom-plan-tip.svg']: require('./images/custom-plan-tip.svg'),
      ['external.svg']: require('./images/external.svg'),
      ['avatar.svg']: require('./images/avatar.svg'),
      ['no-select-exp.png']: require('./images/no-select-exp.png'),
      ['home-loan.png']: require('./images/home-loan.png'),
      ['home-requsition.png']: require('./images/home-requsition.png'),
      ['template-expense.png']: require('./images/template-expense.png'),
      ['template-loan.png']: require('./images/template-loan.png'),
      ['template-travel-requsition.png']: require('./images/template-travel-requsition.png'),
      ['template-travel.png']: require('./images/template-travel.png'),
      ['mine-corporation-info-placeHolderImg.svg']: require('./images/mine-corporation-info-placeHolderImg.svg'),
      ['empty-item.svg']: require('./images/empty-item.svg'),
      ['feetype_root.png']: require('./images/feetype_root.png'),
      ['add-condition.svg']: require('./images/add-condition.svg'),
      ['custom-flow-close.svg']: require('./images/custom-flow-close.svg'),
      ['custom-flow-del.svg']: require('./images/custom-flow-del.svg'),
      ['taxi.svg']: require('./images/taxi.svg'),
      ['eleme.svg']: require('./images/eleme.svg'),
      ['icon-move-up.svg']: require('./images/icon-move-up.svg'),
      ['icon-move-up-disabled.svg']: require('./images/icon-move-up-disabled.svg'),
      ['icon-move-down.svg']: require('./images/icon-move-down.svg'),
      ['icon-move-down-disabled.svg']: require('./images/icon-move-down-disabled.svg'),
      ['empty-detail.svg']: require('./images/empty-detail.svg'),
      ['bell.svg']: require('./images/bell.svg'),
      ['no-user-avator-square.svg']: require('./images/no-user-avator-square.svg'),
      ['icon-edit.svg']: require('./images/icon-edit.svg'),
      ['icon-delete.svg']: require('./images/icon-delete.svg'),
      ['newpageguide.svg']: require('./images/newpageguide.svg'),
      ['icon-asc.svg']: require('./images/icon-asc.svg'),
      ['icon-dsc.svg']: require('./images/icon-dsc.svg'),
      ['search-empty.svg']: require('./images/search-empty.svg'),
      ['roleAvatar.svg']: require('./images/roleAvatar.svg'),
      ['empty.svg']: require('./images/empty.svg'),
      ['no-user-avator.svg']: require('./images/no-user-avator.svg'),
      ['platform-default-icon.png']: require('./images/platform-default-icon.png'),
      ['role.svg']: require('./images/role.svg'),
      ['department.svg']: require('./images/department.svg'),
      ['tongyong.svg']: require('./images/tongyong.svg'),
      ['clyh.svg']: require('./images/clyh.svg'),
      ['xc-travel.png']: require('./images/xc-travel.png'),
      ['record-left.png']: require('./images/record-left.png'),
      ['record-right.png']: require('./images/record-right.png'),
      ['invoice-left.png']: require('./images/invoice-left.png'),
      ['invoice-right.png']: require('./images/invoice-right.png'),
      ['no_travel.svg']: require('./images/no_travel.svg'),
      ['home_record.svg']: require('./images/home_record.svg'),
      ['home_invoice.svg']: require('./images/home_invoice.svg'),
      ['order-top.svg']: require('./images/order-top.svg'),
      ['star.svg']: require('./images/star.svg'),
      ['ai-approval.svg']: require('./images/ai-approval.svg'),
      ['apportion_example.png']: require('./images/apportion_example.png')
    }
  },
  {
    resource: '@hosting',
    value: {
      ['initializeTitle']: require('./hosting/initializeTitle')
    }
  }
]
