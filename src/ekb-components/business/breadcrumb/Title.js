/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/17
 * */
import React, { PureComponent } from 'react'
import './Title.less'
import { isString, isFunction } from '@ekuaibao/helpers'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'
import { app } from '@ekuaibao/whispered'
import { Button, Modal } from '@hose/eui'
import guidePic from '../../../images/order-management-guide.png'
import { getBoolVariation } from '../../../lib/featbit'

export default class Title extends PureComponent {
  state = { chargeTitle: undefined, tipText: '' }
  componentDidMount() {
    app
      .invokeService('@itinerary-manage:get:travelManagementConfig', { type: 'travelOneOrderConfirmConfig' })
      .then(({ value }) => {
        const contextDetail = value?.contextDetail
        const remindMessage = contextDetail?.remindMessage
        this.setState({ tipText: remindMessage || '' })
      })
  }

  componentWillReceiveProps(nextPorps) {
    if (nextPorps.changeTitleEffect !== this.props.changeTitleEffect) {
      this.setState({ chargeTitle: undefined })
    }
  }

  handleChangeTitle = chargeTitle => {
    this.setState({ chargeTitle })
  }

  handleCheckOrderSyncRole = () => {
    Modal.info({
      title: i18n.get('订单数据获取规则说明'),
      icon: null,
      width: 800,
      height: 800,
      centered: true,
      closable: true,
      content: (
        <div className="guide_popover_content" style={{ fontSize: '14px' }}>
          <div className="content-item-layer">
            <div className="content-item">
              <p className="item-title">
                <span className="dot">●</span>
                <span className="item-title-text">
                  {i18n.get(
                    '合思商旅：实时同步订单数据'
                  )}
                </span>
              </p>
              <p className="item-title">
                <span className="dot">●</span>
                <span className="item-title-text">
                {i18n.get(
                    getBoolVariation('fkrd-5385-pull_high_frequency')
                      ? '携程商旅：每5分钟同步过去70分钟的订单数据'
                      : '其他第三方 TMC 平台：每日凌晨1:00-6:00定时同步近7天的订单数据（含：携程商旅、阿里商旅、滴滴企业版、大唐商旅、差旅壹号、吉利商旅）'
                  )}
                </span>
              </p>
              <p className="item-title">
                <span className="dot">●</span>
                <span className="item-title-text">
                  {i18n.get(
                    getBoolVariation('fkrd-5385-pull_high_frequency')
                      ? '阿里商旅：每5分钟同步过去10分钟的订单数据'
                      : '例如：1月10日凌晨1:00同步1月3日-1月9日的订单数据'
                  )}
                </span>
              </p>
              <p className="item-title">
                <span className="dot">●</span>
                <span className="item-title-text">
                  {i18n.get(
                    '同程商旅：每5分钟同步过去10分钟的订单数据'
                  )}
                </span>
              </p>
              <p className="item-title">
                <span className="dot">●</span>
                <span className="item-title-text">
                  {i18n.get(
                    '您也可以点击页面的【刷新】按钮，手动同步订单'
                  )}
                </span>
              </p>
            </div>
            <img src={guidePic} className='order-management-guide-pic'/>
          </div>
        </div>
      )
    })
  }

  renderTitle = () => {
    let { changeColor, TitleContainer, title, changeTitleEffect, tooltip, titleType = 'single', ...others } = this.props
    let { tipText } = this.state
    let classNameInner =
      'navigation-bar-title-content ' +
      (changeColor ? 'change-color ' : '') +
      (window.isNewHome ? ' navigation-bar-title-' + titleType : '')
    const { chargeTitle } = this.state
    let finalTitle = changeTitleEffect ? chargeTitle || title : title
    finalTitle = isString(finalTitle) ? i18n.get(finalTitle) : finalTitle
    return TitleContainer ? (
      <TitleContainer {...others} changeTitle={this.handleChangeTitle}>
        <div className={classNameInner}>{finalTitle}</div>
        {tooltip && tooltip()}
      </TitleContainer>
    ) : (
      <>
        {finalTitle === '订单确认' && (
          <div className={classNameInner + ' tips'}>
            <span>{finalTitle}</span>
            {tipText && (
              <Tooltip title={tipText}>
                <QuestionCircleOutlined className="icon-tip" />
              </Tooltip>
            )}
          </div>
        )}
        {(finalTitle === '订单管理' || finalTitle === 'Order Management') && (
          <div className='order-management-title'>
            <div className={classNameInner}>{finalTitle}</div>
            <Button category="secondary" onClick={this.handleCheckOrderSyncRole}>{i18n.get('查看订单同步规则')}</Button>
          </div>
        )}
        {finalTitle !== '订单确认' && finalTitle !== 'Order Management' && finalTitle !== '订单管理' && <div className={classNameInner}>{finalTitle}</div>}
        {tooltip && tooltip()}
      </>
    )
  }

  render() {
    let { statusText, otherView, RightView, bus, className = '', immersiveable, nodeLength, ...others } = this.props
    const { otherViewReplaceTitle, create } = others
    let classNameWrapper = 'navigation-bar-title-wrapper ' + className
    let renderTitleVisible = true
    let otherViewVisible = !!otherView
    if (otherViewReplaceTitle) {
      renderTitleVisible = nodeLength > 1
      otherViewVisible = nodeLength <= 1
    }
    if (otherViewReplaceTitle === false) {
      otherViewVisible = false
    }
    return (
      <div className={classNameWrapper}>
        {renderTitleVisible && this.renderTitle()}
        {otherViewVisible && (isFunction(otherView) ? otherView(create) : otherView)}
        {statusText && <div className="status-tag">{statusText}</div>}
        {RightView && <RightView {...others} changeTitle={this.handleChangeTitle} TitleBus={bus} />}
      </div>
    )
  }
}
