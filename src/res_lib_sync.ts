import loadable from '@loadable/component'

export default {
  resource: '@lib',
  value: {
    ['attachment-fetch']: require('./lib/attachment-fetch'),
    ['entity.join']: require('./lib/entity.join'),
    ['expenseMangerUtils']: require('./lib/expenseMangerUtils'),
    ['export-excel-service']: require('./lib/export-excel-service'),
    ['lib-util']: require('./lib/lib-util'),
    ['logs']: require('./lib/logs'),
    ['misc']: require('./lib/misc'),
    ['featbit']: require('./lib/featbit'),
    ['filtersFixer']: require('./lib/filtersFixer').default,
    ['mutil-staff-fetch']: require('./lib/mutil-staff-fetch').default,
    ['dragSort']: loadable(() => import('./lib/dragSort')),
    ['sourceMap']: require('./lib/souceMap').default,
    ['fee-util']: require('./lib/fee-util'),
    ['corp-util']: require('./lib/corp-util'),
    ['token-data']: require('./lib/token-data').default,
    ['datalink-table-column-parse']: require('./elements/DataLinkTable/tableUtil'),
    ['columns-util']: require('./elements/data-grid/columnsUtil'),
    ['entity-select-join']: require('./lib/entity.join'),
    ['requisition-relate']: require('./elements/feeDetailViewList/Related'),
    ['element-util']: require('./elements/util'),
    ['staff-show-util']: require('./elements/utilFn'),
    ['charge-util']: require('./lib/charge-util'),
    ['staff-tag-util']: require('./elements/puppet/staff-select-heavy/utile'),
    ['data-grid-v2/CustomSearchUtil']: require('./elements/data-grid-v2/element/util'),
    ['i18n']: require('./lib/i18n'),
    ['flowPerformanceStatistics']: require('./lib/flowPerformanceStatistics'),
  }
}
