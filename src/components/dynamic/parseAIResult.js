import moment from 'moment'

/**
 * 解析各种格式的日期为时间戳
 * @param {*} dateValue - 日期值(字符串、Date对象、时间戳等)
 * @returns {number|undefined} - 返回时间戳或undefined
 */
const parseDateToTimestamp = (dateValue) => {
  if (!dateValue) return undefined

  // 如果是Date对象
  if (dateValue instanceof Date) {
    return isNaN(dateValue.getTime()) ? undefined : dateValue.getTime();
  }

  // 如果已经是有效时间戳
  if (typeof dateValue === 'number' && !isNaN(dateValue)) {
    return dateValue;
  }

  // 字符串处理
  if (typeof dateValue === 'string' && dateValue.trim()) {
    const trimmedValue = dateValue.trim();

    // 处理"YYYY年MM月DD日"格式
    if (/^\d{4}年\d{1,2}月\d{1,2}日$/.test(trimmedValue)) {
      const parts = trimmedValue.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
      if (parts) {
        const date = new Date(parseInt(parts[1]), parseInt(parts[2]) - 1, parseInt(parts[3]));
        return isNaN(date.getTime()) ? undefined : date.getTime();
      }
    }

    // 尝试标准解析
    let date = new Date(trimmedValue);
    if (!isNaN(date.getTime())) {
      return date.getTime();
    }

    // 尝试解析"YYYY-MM-DD"、"YYYY/MM/DD"等格式
    const dateRegex = /(\d{4})[-/年](\d{1,2})[-/月](\d{1,2})[日]?/;
    const match = trimmedValue.match(dateRegex);
    if (match) {
      date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
      return isNaN(date.getTime()) ? undefined : date.getTime();
    }
  }

  return undefined;
};


/**
 * 解析日期范围对象
 * @param {*} rangeValue - 日期范围值
 * @returns {{start: number|undefined, end: number|undefined}|null} - 返回日期范围或null
 */
const parseDateRange = (rangeValue) => {
  if (!rangeValue || typeof rangeValue !== 'object') {
    return null;
  }

  let start, end;

  // 处理start日期
  if ('start' in rangeValue) {
    start = parseDateToTimestamp(rangeValue.start);
  }

  // 处理end日期
  if ('end' in rangeValue) {
    end = parseDateToTimestamp(rangeValue.end);
  }

  // 至少需要一个有效日期
  if (start === undefined && end === undefined) {
    return null;
  }

  return { start, end };
};

/**
 * 解析数字值
 * @param {*} numValue - 数字值
 * @returns {number|undefined} - 返回数字或undefined
 */
const parseNumberValue = (numValue) => {
  if (numValue === null || numValue === undefined) {
    return undefined;
  }

  if (typeof numValue === 'number' && !isNaN(numValue)) {
    return numValue;
  }

  if (typeof numValue === 'string' && numValue.trim()) {
    // 移除逗号、空格等非数字字符（保留小数点和负号）
    const cleanedStr = numValue.replace(/[^\d.-]/g, '');
    const num = parseFloat(cleanedStr);
    return isNaN(num) ? undefined : num;
  }

  return undefined;
};

/**
 * 格式化对象为字符串
 * @param {Object} obj - 要格式化的对象
 * @returns {string} - 格式化后的字符串
 */
const formatObjectToString = (obj) => {
  if (!obj || typeof obj !== 'object') return String(obj);

  try {
    const parts = Object.entries(obj).map(([key, val]) => {
      const displayVal = typeof val === 'string' ? `'${val}'` : val;
      return `${key}:${displayVal}`;
    });
    return parts.join(',');
  } catch (e) {
    console.error('解析对象失败:', e);
    return String(obj);
  }
};

/**
 * 格式化数组为字符串
 * @param {Array} arr - 要格式化的数组
 * @returns {string} - 格式化后的字符串
 */
const formatArrayToString = (arr) => {
  if (!Array.isArray(arr)) return String(arr);

  try {
    // 处理对象数组
    if (arr.length > 0 && typeof arr[0] === 'object' && arr[0] !== null) {
      const flatPairs = arr.flatMap(obj => {
        if (obj && typeof obj === 'object') {
          return Object.entries(obj).map(([key, val]) => {
            const displayVal = typeof val === 'string' ? `'${val}'` : val;
            return `${key}:${displayVal}`;
          });
        }
        return [];
      });
      return flatPairs.join('，');
    }

    // 处理普通数组
    return arr.join('，');
  } catch (e) {
    console.error('解析数组失败:', e);
    return String(arr);
  }
};

/**
 * 获取基础货币对象
 * @returns {Object} - 返回标准货币对象
 */
const getBaseMoney = () => ({
  standardUnit: '元',
  standardScale: 2,
  standardSymbol: '¥',
  standardNumCode: '156',
  standardStrCode: 'CNY'
});

/**
 * 解析AI结果值为指定类型
 * @param {*} value - 要解析的值
 * @param {string} type - 目标类型
 * @returns {*} - 解析后的值
 */
export const parseAIResultValue = (value, type) => {
  try {
    // 日期类型
    if (type === 'date') {
      // 处理日期
      const timestamp = parseDateToTimestamp(value);

      // 如果是个日期范围，则显示开始值
      if (typeof value === 'object' && value !== null && 'start' in value && 'end' in value) {
        return parseDateToTimestamp(value.start);
      }

      return timestamp;
    }

    // 日期范围类型
    if (type === 'dateRange') {
      return parseDateRange(value);
    }

    // 数字类型
    if (type === 'number') {
      return parseNumberValue(value);
    }

    // 金额类型
    if (type === 'money') {
      const baseMoney = getBaseMoney();

      // 如果已经是money对象格式
      if (typeof value === 'object' && value !== null && 'standard' in value) {
        return value;
      }

      // 解析为数字
      const numValue = parseNumberValue(value);
      if (numValue === null || numValue === undefined) return undefined;

      return {
        ...baseMoney,
        standard: typeof numValue === 'string' ? numValue : numValue.toString()
      };
    }

    // 文本类型
    if (type === 'text') {
      // null或undefined
      if (value === null || value === undefined) {
        return '';
      }

      // 字符串直接返回
      if (typeof value === 'string') {
        return value;
      }

      // 对象格式
      if (typeof value === 'object' && !Array.isArray(value) && value !== null) {
        return formatObjectToString(value);
      }

      // 数组格式
      if (Array.isArray(value)) {
        return formatArrayToString(value);
      }

      // 其他类型
      return String(value);
    }

    // 空对象
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      // 处理空对象
      if (Object.values(value).filter(Boolean).length === 0) return undefined
      return value
    }

    // 空数组
    if (Array.isArray(value)) {
      value = value.filter(item => !(item && typeof item === 'object' && Object.values(item).filter(Boolean).length === 0))?.filter(item => Object.values(item)?.filter(Boolean)?.length > 0)?.filter(Boolean)
      if (value.length === 0) return [];
      return value
    }

    if (type === 'list:dataLinkEdits') {
      return value
    }

    // 默认情况
    return value;
  } catch (error) {
    console.error('解析AI结果值失败:', error, { value, type });
    return null;
  }
};

const cleanNull = (str) => typeof str === 'string' ? str.replace(/\s*null$/, '') : '';

/**
 * 提取拼接输入内容的名称
 * @param {*} input 
 * @returns 
 */
export const extractNames = (input) => {
  let data = input;

  if (typeof input === 'string') {
    try {
      data = JSON.parse(input);
    } catch {
      return '';
    }
  }

  if (!data) return '';

  if (Array.isArray(data)) {
    return data
      .map(({ name, label }) => cleanNull(name ?? label ?? ''))
      .filter(Boolean)
      .join('、');
  }

  if (typeof data === 'object') {
    return cleanNull(data.name ?? data.label ?? '');
  }

  return '';
}

// 将格式化后的数据转为字符串，用于显示识别结果
export const formatAIResultToString = (result, key, typeMap) => {
  const value = result[key]
  const type = typeMap[key]
  const parsedValue = parseAIResultValue(value, type)

  const getDateValue = (dateValue) => {
    return dateValue ? moment(dateValue).format('YYYY-MM-DD') : ''
  }

  if (type === 'date') {
    return getDateValue(parsedValue)
  }
  if (type === 'dateRange') {
    const start = getDateValue(parsedValue?.start)
    const end = getDateValue(parsedValue?.end)
    if (!start || !end) {
      return ''
    }
    return start + ' - ' + end
  }
  if (type === 'money') {
    return parsedValue ? parsedValue?.standard : ''
  }
  if (
    type?.startsWith('datalink.DataLinkEntity') ||
    type?.startsWith('basedata.Dimensio') ||
    type === 'organization.Staff' ||
    type?.startsWith('basedata.Enum') ||
    type === 'organization.Department' ||
    type === 'basedata.city' ||
    type === 'payeeInfo' ||
    type === 'pay.PayeeInfo'
  ) {
    return extractNames(parsedValue)
  }

  if (typeof parsedValue === 'object') return ''

  return parsedValue ? String(parsedValue) : ''
}

// 将格式化后的数据转为dom，用于显示识别结果, 比如业务对象多选
export const formatAIResultToDom = (result, key, typeMap) => {
  const value = result[key]
  const type = typeMap[key]
  const parsedValue = parseAIResultValue(value, type)

  // 业务对象多选
  if (type === 'list:dataLinkEdits') {
    return parsedValue ? String(parsedValue) : ''
  }

  return parsedValue ? String(parsedValue) : ''
}

/**
 * 多选城市数据处理
 * @param {*} data 
 * @returns 
 */
export const extractIdsFromCityArray = (data) => {
  if (!data) return undefined;
  let cityData = data;
  if (typeof data === 'string') {
    try {
      cityData = JSON.parse(data);
    } catch {
      return undefined;
    }
  }

  if (!Array.isArray(cityData) && typeof cityData === 'object' && Object.values(cityData)?.filter(Boolean)?.length > 0) {
    cityData = [cityData]
  }

  const valueJSON = cityData?.filter?.(({ key }) => key)
  return valueJSON.length > 0 ? JSON.stringify(valueJSON) : undefined
}

/**
 * 提取数组中的id
 * @param {*} data 
 * @returns 
 */
export const extractIdsFromArray = (data) => {
  if (!data) return undefined;
  let idsData = data;
  if (typeof data === 'string') {
    try {
      idsData = JSON.parse(data);
    } catch {
      return undefined;
    }
  }

  if (!Array.isArray(idsData) && typeof idsData === 'object' && Object.values(idsData)?.filter(Boolean)?.length > 0) {
    idsData = [idsData]
  }

  const ids = idsData?.map?.(({ id }) => id).filter(Boolean)
  return ids.length > 0 ? ids : undefined
}


/**
 * 单选城市数据处理
 * @param {*} data 
 * @returns 
 */
export const extractIdFromCityData = (data) => {
  if (!data) return undefined;
  let cityData = data;
  if (typeof data === 'string') {
    try {
      cityData = JSON.parse(data);
    } catch {
      return undefined;
    }
  }

  if (!Array.isArray(cityData) && typeof cityData === 'object' && Object.values(cityData)?.filter(Boolean)?.length > 0) {
    cityData = [cityData]
  }

  const valueJSON = cityData?.filter?.(({ key }) => key).filter((_, index) => index === 0)
  return valueJSON.length > 0 ? JSON.stringify(valueJSON) : undefined
}

// 预处理函数：移除空对象字段
export function removeEmptyObjects(obj) {
  if (Array.isArray(obj)) {
    return obj.map(removeEmptyObjects);
  }

  if (typeof obj === 'object' && obj !== null) {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value) && Object.values(value).filter(Boolean).length === 0) {
        // 跳过空对象
        continue;
      }
      if (removeEmptyObjects(value)) {
        result[key] = removeEmptyObjects(value);
      }
    }
    return result;
  }

  return obj;
}