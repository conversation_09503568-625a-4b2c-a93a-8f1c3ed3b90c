import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import MessageCenter from '@ekuaibao/messagecenter'
import { cloneDeep } from 'lodash'
import { wrapper } from '../../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import TripCard from './TripCard'
import styles from './TripDataLink.module.less'
import TravelClosedLoop from "../TravelClosedLoop"
import { filterTripTemplate } from '../utils'
import TripTool from './TripTool'
import moment from 'moment'

@EnhanceField({
  descriptor: {
    test(field) {
      let { type, referenceData } = field
      return type === 'dataLinkEdits' && referenceData.type === 'TRIP'
    }
  },
  wrapper: wrapper(true)
})
export default class TripDataLink extends PureComponent {
  constructor(props) {
    super(props)
    this.bus = new MessageCenter()
    let { field } = this.props
    field.fieldBus = this.bus
    field.fieldBus.onChange = props.onChange
    this.tripTool = new TripTool(this.props)
    this.state = {
      tripsList: [],
      field: field,
      template: [], tripsTemplate: []
    }
  }
  componentDidMount = async () => {
    const data = await api.invokeService('@tpp-v2:get:getTravelManagement')
    await this.getTripsTemplate()
    this.tripTool.setConfig(data?.items || [])
    await this.getTemplate()
  }
  getTripsTemplate = () => {
    return api.invokeService('@bills:get:getTripsTemplate').then(res => {
      const tripsTemplate = res.items
      this.setState({
        tripsTemplate
      })
    })
  }
  getTemplate = () => {
    let {
      field: { behaviour = 'INSERT' }
    } = this.props
    const { field } = this.state
    return api
      .invokeService('@bills:get:getDataLinkEditTemplate', {
        id: field.referenceData.id,
        type: behaviour === 'INSERT'
      })
      .then(res => {
        const template = filterTripTemplate(res.items, field)
        this.tripTool.setTemplate(res.items)
        this.setState({
          template,
          tripsList: this.formatValue(this.props.value, template)
        })
      })
  }
  formatValue = (value) => {
    let u = this.tripTool.getValues(value)
    return u
  }
  handleEdit = index => {
    const tripsList = cloneDeep(this.state.tripsList)
    const value = tripsList[index]
    api.open('@bills:AddTripModal', {
      bus: this.props.bus,
      isSingleDataLink: false,
      template: this.tripTool.getCurrentTemp(value),
      editable: false,
      value,
      isSingle: true
    })
  }
  renderList() {
    const { tripsList } = this.state
    const groupKeys = [...new Set(tripsList?.map(t => t?.startTime))]?.sort();
    const firstGroupKey = groupKeys[0];
    const lastGroupKey = groupKeys[groupKeys.length - 1];
    const isSingleGroup = tripsList?.length === 1 || tripsList?.every(item => item?.startTime === tripsList[0]?.startTime)

    // 获取对象包含key中得某个字符串，得value
    const reconsitutionValue = (value, property) => {
      return value?.[Reflect?.ownKeys(value)?.find((e) => e?.indexOf(property) > 0)]
    }

    return (
      <div className="trips-list-wrapper">
        {tripsList.map((trip, index) => {
          const referenceStartDate = reconsitutionValue(trip?.dataLinkForm, '行程开始日期')
          return (
          <div className="trips-item" key={index} onClick={() => this.handleEdit(index)}>
            <div className="trip-card">
              <TripCard
                trip={trip}
                readonly={true}
                tripsTemplate={this.state?.tripsTemplate || []}
                isNewGroup={trip?.startTime !== tripsList?.[index - 1]?.startTime}
                isFirstGroup={trip?.startTime === firstGroupKey}
                isLastGroup={trip?.startTime === lastGroupKey}
                isSingleGroup={isSingleGroup}
                isNewVersion
                onlyOne={tripsList?.length === 1}
                hiddleLeft={this.props?.billState === 'approving' ? false : true}
                referenceStartDateSimple={referenceStartDate ? moment(referenceStartDate)?.format('MM.DD dddd') : ''}
                referenceStartTime={moment(reconsitutionValue(trip?.dataLinkForm, '行程开始日期'))?.format('MM月DD日')}
                referenceEndTime={moment(reconsitutionValue(trip?.dataLinkForm, '行程结束日期'))?.format('MM月DD日')}
              />
            </div>
          </div>
        )})}
      </div>
    )
  }

  render() {
    const { value } = this.props
    return (
      <div className={styles['trip-details-wrapper']}>
        <TravelClosedLoop value={value} />
        {this.renderList()}
      </div>
    )
  }
}
