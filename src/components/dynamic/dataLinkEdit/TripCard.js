import React, { PureComponent } from 'react'
import moment from 'moment'
import { get } from 'lodash'
import TripItem from './TripItem'
import TravelItem from '../../../elements/new-travel-plan-item'
import { getWeek } from '../../../lib/lib-util'

const TYPE_MAP = {
  TAXI: '用车',
  FLIGHT: '飞机',
  TRAIN: '火车',
  HOTEL: '酒店',
  FOOD: '餐饮',
  COMMON: '通用'
}

export default class TripCard extends PureComponent {
  constructor(props) {
    super(props)
  }
  formatData = data => {
    const { trip: { tripType = '' } } = this.props
    let tripFeeType = {}
    if (tripType === 'COMMON') {
      tripFeeType = {
        icon:
          'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABUCAYAAAAcaxDBAAAAAXNSR0IArs4c6QAAA29JREFUeAHtmr9rFEEUx+9Eg4iFohZaGBH8QTpbFSSNjZUWKljYiKCgjQhiaxNJYyGilf4DinYWASMYUURExcJCNIiFGkURYyIm5+fpHSx3u0d29+ZN9vgOvNvd2Zn35vu5NzvL7tZqKiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgApUl0EgvxyorKPbA03k25qg/FHtslYyfAdSqf2P7Kimqy6DrXc715BTQtjYdLWH7FFuRcDzL/mTiuNe7DRxOYWPYSL1en+l1gKj+gHsPi1VGPMRb1niWh57B2mINth0HOfQGOhFExcKcXl9Ys3Ktgl9Dk8Njrq/m+AvmGpd4t7l+7k+OJdS+t7AaUF8hZiiUoBS/thANAfRtyrmeV3lPeRPgPe1HvWCauH4H+h6NLqu7wbQSA6jnSn+G7Jz+L9Xn1/0aarK4jn5isy6wxHFgDgeO0eE+RobaIEJn6RwxTneodaiIBTT0wnSF7HzpwG9xhGDK78RClc84XhVLaawMtYck9mAkRDlPdn4L4XhR+ySLJgKk6BN8xkqSf7xjBu/1dfQdig6QnfMxM2lpxODtK/1XxnK/wHgMoPm6BsyfBfr3Rxem5o62KX+qP5RFUgHMiwmgk+wPRBpK9cMCbxn2MQFUb0HL/K2APJiA+Yb9mNfyMlI6+sYScjwxErvTuAzURFXp3R8sUGdLeyngwP3hCODWM84PWMjYtvLvAeqDAkxKdYlxH2oxQ8I0IBbjsO14F3egZI1lZ+gXZva06YY3TIsXOlMyNTH1t3ByO5b88CGzfc4TM/xxd3L2qX5zoA5gg1jpPxYfK7EN1adSQAHC7T50FJvFrExhRwu4sqf/a7GbmH2AZuU1truIr8r2QfAFU55S9uYVhY+7KX6+U7cxr6/KtkesZWRauZVHFA42pzlp1p3L46tXbd1XecQuZ/D2BUlasXvUPKVb+yjXU3egrL72JcfzDGqPM+qzql9w4lfGyUcZ9f1XTZbuwqabU7O1sSdOa/Kqpc8JbL7lpLkdY+ueLDb20rcreQG02iN4G/snsU3YM+wS2VvoXRC+hul/BLOXc+PYVXz9YasiAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAiIgAjEJ/AUaCw//2RpkeAAAAABJRU5ErkJggg==',
        color: 'rgb(38, 198, 218)'
      }
    } else if (tripType === 'FOOD') {
      tripFeeType = {
        icon:
          'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABUCAYAAAAcaxDBAAAAAXNSR0IArs4c6QAAAqFJREFUeAHtmjtIHFEUhl3RIoYUhmCwFgQJYiVB1mCRBNJYCYKFYCGWWgiWWiWViOms0oS06SRVYMuQELBK0osEbCyEILEY/4ujDOM87ixndtbZ78LP3nte4/k8Kuvevj4WBCAAAQhAAAIQgAAEIAABCEAAAhCAAAQgAAEIQAACEIAABCAAAQhAAAK1IxAEwU/pnWtMr6PSibQQnl9r/1caz2pc/sfSgXQqHUtvpQdZObX1qXG3voQAn18fg53wvBGe36QBkP+p9CuMi760dBhKyyvb3l/2A8qoL2AN1f0kTSTUn5NtP8HeEdO9BCoy89LLDEKrgv4sw1+aq2qgbtLaWVs5Sa5uXkxOifbcVQMt/FVr8qaU1PRIXFLssEecaUi3AC0yqdOeBAYV5+B3dA109Gl3HzarKdqU+VXoWtb5TPvVu6G3lsnbXf7Gxbbyw+wiqgb6UK3sRtoZ0/595Jy0/Z9kTLFdpNhLM3fLj3yRBr8XCP5RINYktEqggUcHSTGflffHI/ew0WgcecTVI0S/K91bz6x1KedoUreyj0vurWnaOpJjJCm3tjY1/EK6SCMi+3ZW8/KPSB9j+ec670iPsnJr61PjTembFF3unyRrPk0rbiaaqP1Xn7xaxwjCYgzKB9+Gldd1QKv8o+TL7V7FAdT42wVQgBoTMC7HhALUmIBxOSYUoMYEjMsxoQA1JmBcjgkFqDEB43JMKECNCRiXY0IBakzAuBwTClBjAsblmNAaAo1/9h4/G7dcbrlumFB3GeEy0maRmyH/InluGz/H3D1y1KeX7tLYnrQied/EU+yg9Fu6WSs9gqy8NkXyibQupd7HL+/pVIYABCAAAQhAAAIQgAAEIAABCEAAAhCAAAQgAAEIQAACEIAABCBQNwJXz52e211TEDIAAAAASUVORK5CYII=',
        color: '#F48FB1'
      }
    } else {
      tripFeeType = this.props?.tripsTemplate.find(i => i.id.split(':')[1] === tripType.toLowerCase())
    }
    if (!tripFeeType) {
      tripFeeType = {
        icon:
          'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABUCAYAAAAcaxDBAAAAAXNSR0IArs4c6QAAAe9JREFUeAHt2ktKA0EUheG0iDMdOxAci3MhC3AJjnQxcS/OXIIrcAHiAhR07CwitOdKB6SJ5lySrsrjL7jk0advVX9WkoE9GjEQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAIENEWhqrbNt2yPNfak6U+2vaB1f6vOsemia5mNFPde/jTDHqhfVUCN6j2tIFN+hutDYmU+qk4Ev+FX9z0vv1L2BL2pe+/iYD40Z88YcMVfRUQM0vjNLjZJz/VxTDdBV/QA5f5SSc1UDdSA2NlNjh24slrNwQB2lRAbQBJYTBdRRSmQATWA5UUAdpUQG0ASWEwXUUUpkAE1gOVFAHaVEBtAElhMF1FFKZABNYDlRQB2lRAbQBJYTBdRRSmQATWA5UUAdpUQG0ASWEwXUUUpkAE1gOVFAHaVEpvj/rRNrm0U/9eS+e3Glx4PZgXV8XOcd+i6wW9Wp7k+6iYrn3XtxjBECullssuCWu0cdv1b9uRPjWJeJ7H9jsvXquvp5oFO9f6e6yALEOd250aM/dg70TQIBfJyF7OejR9cres7GzoAu/Fj3wdzXkvz9dbAToIcuzrI54Raba9m1cj4CCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAwFYKfAMaK1aiqimvmgAAAABJRU5ErkJggg==',
        color: 'rgb(38, 198, 218)'
      }
    }
    let { startTime, endTime, startCity, endCity } = data
    let type = data.tripType
    let isCityRange = get(data, 'entity.isCityRange')
    let isDateRange = get(data, 'entity.isDateRange')
    let weekDay = ''
    if (['CUSTOM', 'TRAIN', 'FLIGHT'].includes(type)) {
      if (isCityRange === false) {
        endCity = null
      }
      if (isDateRange === false) {
        endTime = null
      }
    }
    if (!endTime) {
      weekDay = getWeek(startTime)
    }
    startTime = (startTime && moment(startTime).format(i18n.get('MM月DD日'))) || ''
    endTime = (endTime && moment(endTime).format(i18n.get('MM月DD日'))) || ''
    let st = (startCity && JSON.parse(startCity)) || []
    let et = (endCity && JSON.parse(endCity)) || []
    const pathKey = i18n.currentLocal === 'en-US' ? 'enLabel' : 'label'
    startCity = startCity ? (JSON.parse(startCity)?.[0]?.[pathKey] || JSON.parse(startCity)?.[0]?.label) : ''
    let tooltipStr = ''
    if (st.length > 1) {
      startCity += `(+${st.length - 1})`
      tooltipStr = st?.map(v => v[pathKey] || v.label)?.join('、')
    }
    endCity = endCity ? (JSON.parse(endCity)?.[0]?.[pathKey] || JSON.parse(endCity)?.[0]?.label) : ''
    if (et.length > 1) {
      endCity += `(+${et.length - 1})`
    }
    return { startTime, endTime, startCity, endCity, tripFeeType, weekDay, tooltipStr }
  }


  handleOpen = async e => {
    let { trip, index } = this.props
    this.props?.handleOpen(e, trip, index)
  }

  handleDeleteTrip = (e) => {
    const { handleDelete } = this.props
    handleDelete && handleDelete(e)
  }

  render() {
    const { trip, showButton = false, readonly = false, exposureSensorTrack, isNewGroup, onlyOne, isNewVersion, isFirstGroup, isLastGroup, isSingleGroup, hiddleLeft, sceneTypeField, referenceStartTime, referenceEndTime, referenceStartDateSimple } = this.props
    if (!trip) return null
    const { startCity, endCity, tripFeeType = {}, startTime, endTime, weekDay, tooltipStr } = this.formatData(trip)
    let dateString = ''
    if (startTime && endTime) {
      const daysNumber = moment(trip.endTime).diff(trip.startTime, 'days')
      dateString = `${startTime} ~ ${endTime} ${i18n.get('共')}${daysNumber + 1}${i18n.get('天')}`
      if (trip.tripType === 'HOTEL') {
        dateString += `${daysNumber}${i18n.get('晚')}`
      }
    } else {
      dateString = startTime ? `${startTime} ${weekDay}` : `${endTime} ${endTime}`
    }
    if (showButton) {
      exposureSensorTrack && exposureSensorTrack(trip?.tripType)
    }
    const cityStr = `${startCity} ${startCity && endCity && '-'} ${endCity}`

    if (isNewVersion) {
      const formatedData = {
        type: TYPE_MAP[trip?.tripType] || trip?.entity?.configName,
        travelToCity: trip?.endCity,
        travelFromCity: trip?.startCity,
        startDate: trip?.startTime,
        endDate: trip?.endTime,
        referencePrice: trip?.money,
        scene: undefined,
        sceneList: trip.sceneList
      }

      return <TravelItem
        isNewGroup={isNewGroup}
        isFirstGroup={isFirstGroup}
        isLastGroup={isLastGroup}
        isSingleGroup={isSingleGroup}
        isOriginalTravelPlainning={true} // 2.0
        onlyOne={onlyOne}
        line={formatedData}
        onOpen={(e) => this.handleOpen(e)}
        onDeleteTrip={e => this.handleDeleteTrip(e)}
        showButton={showButton}
        editable={trip?.editable}
        readOnly={readonly}
        isOldVersion
        hiddleLeft={hiddleLeft}
        sceneTypeField={sceneTypeField}
        referenceStartTime={referenceStartTime}
        referenceEndTime={referenceEndTime}
        referenceStartDateSimple={referenceStartDateSimple}
      />
    }


    return (
      <TripItem
        tripType={tripFeeType}
        cityStr={cityStr}
        readonly={readonly}
        showButton={showButton}
        dateString={dateString}
        editable={trip?.editable}
        money={trip?.money}
        type={trip?.tripType}
        onOpen={(e) => this.handleOpen(e)}
        onDeleteTrip={e => this.handleDeleteTrip(e)}
        tooltipStr={tooltipStr}
        startCity={trip?.startCity}
        endCity={trip?.endCity}
        originStartDate={trip?.startTime}
        originEndDate={trip?.endTime}
        referenceStartTime={referenceStartTime}
        referenceEndTime={referenceEndTime}
      />
    )
  }
}
