/**
 * Created by <PERSON><PERSON><PERSON> on 2017/9/6.
 */

import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Col, Tooltip, Select, Popover } from 'antd'
import { Switch } from '@hose/eui'
import { fnHideFieldsNote, fnF<PERSON>HideFields, isHiddenFieldsInclude } from "../utils/fnHideFields";
import { isAllowModifyFiled } from '../utils/fnDisableComponent'
import FormItemLabelWrap from '../layout/FormItemLabelWrap/FormItemLabelWrap'
import SVG_AUTOCALCULATE from '../../images/auto-calculate.svg'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import classNames from 'classnames'
import styles from './Switcher.module.less'
import { OutlinedTipsInfo } from '@hose/eui-icons'
const Option = Select.Option

// 装饰器
@EnhanceField({
  descriptor: {
    type: 'switcher'
  },
  initialValue(props) {
    let { field, initialValue } = props
    let { defaultValue, editable } = field
    if (initialValue !== undefined) {
      return initialValue
    } else if (defaultValue && defaultValue.type === 'constant') {
      return defaultValue.value
    } else if (defaultValue && defaultValue.type === 'boolean') {
      return defaultValue.value
    } else if (editable && defaultValue && defaultValue.type === 'formula') {
      return undefined
    }
    return false
  }
})
export default class CheckBox extends PureComponent {
  onChange = (checked) => {
    const { onChange } = this.props
    onChange && onChange(checked)
  }

  onSelectChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  render() {
    let {
      hiddenFields,
      field,
      value,
      currentNode,
      isModify,
      form,
      validateError = [],
      external,
      isForbid,
      flowId,
      isDetail,
      detailId,
      dataSource
    } = this.props
    let { name, hide, editable, defaultValue, configs, attributeHide = false, componentPosition = 'right', popoverContent } = field
    const label = fnGetFieldLabel(field)
    let hasAuto = configs === null ? false : configs?.find(r => r?.property === 'hide') === undefined ? false : true

    const isFormula = defaultValue && defaultValue.type === 'formula'
    let disabled = (isModify && !isAllowModifyFiled(currentNode, name)) || !editable
    let errors = form.getFieldError(name)

    if (
      !hide ||
      (hide && !hasAuto) ||
      (hide && !isHiddenFieldsInclude(hiddenFields, field)) ||
      (hide && isHiddenFieldsInclude(hiddenFields, field) && !hasAuto)
    ) {
      let isShow = (hide && !isHiddenFieldsInclude(hiddenFields, field)) || !hide || errors || validateError.includes(name)
      if (!isShow) return null
    } else if (hide && hasAuto && isHiddenFieldsInclude(hiddenFields, field)) {
      let isShow = (hide && !attributeHide) || errors || validateError.includes(name)
      if (!isShow) return null
    }
    //审批流是否设置了字段隐藏
    const flowHiddenFields = fnFlowHideFields(dataSource?.plan)
    if (flowHiddenFields.includes(name)) {
      return null
    }

    if (field?.isSimple) {
      return (
        <Select disabled={disabled} onChange={this.onSelectChange} value={value} style={{ width: '100px' }}>
          <Option value={true}>是</Option>
          <Option value={false}>否</Option>
        </Select>
      )
    }

    return (
      <Col style={{ paddingBottom: '24px' }}>
        <label className={classNames(styles['switch-edit-wrap'])}>
          {componentPosition === 'left' && <Switch className='mr-8' data-testid={`field-switcher-${name}`} onChange={this.onChange} disabled={disabled} checked={value} size='small' />}
          {label}
          {isFormula && !editable && (
            <Tooltip title={i18n.get('系统自动计算，不可修改，无需再次审核')}>
              <img className="img-wrapper ml-5" src={SVG_AUTOCALCULATE} />
            </Tooltip>
          )}
          <FormItemLabelWrap
            riskData={external}
            isForbid={isForbid}
            field={field}
            flowId={flowId}
            isDetail={isDetail}
            detailId={detailId}
            isEdit={true}
          />
          {popoverContent && <Popover
            content={popoverContent}
            trigger="hover"
          >
            <OutlinedTipsInfo className='ml-4' fontSize={12} />
          </Popover>}
          {componentPosition === 'right' && <Switch className='ml-8' data-testid={`field-switcher-${name}`} onChange={this.onChange} disabled={disabled} checked={value} size='small' />}
        </label>
      </Col>
    )
  }
}
