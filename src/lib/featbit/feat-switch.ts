import { getBoolVariation } from './init'

/**
 * 是否启用新版城市选择器
 */
export const enableNewCityPicker = (defaultValue?: boolean) => {
  return getBoolVariation('city-picker-2.0', defaultValue)
}

/**
 * 过滤单据中被隐藏的字段
 */
export const getIfHiddenFields = (defaultValue: boolean = false) => {
  return getBoolVariation('cyxq-68436-filter-hidden-fields', defaultValue)
}

/** 根据表格设置的列字段请求表格数据
 * @link https://jira.hosecloud.com/browse/MFRD-3435
 */
export const getIfFetchDataBySettingOfTableColumn = () => {
  return getBoolVariation('mfrd-3435-get-table-data-by-scene', false)
}

/**
 * 明细保存时增加费用金额<=发票总金额校验
 */
export const getIfCheckInvoicesMoney = () => {
  return getBoolVariation('mfrd-3469-invoices-total-less-than-amount-validate', false)
}

/**
 * 单据编辑页是否展示”本位币“标识
 */
export const isDismissLocalCurrencyLabel = () => {
  return getBoolVariation('mfrd-2995-dismiss-local-currency-label', false)
}

export const newVersionOPaymenAccount = () => {
  return getBoolVariation('new_version_of_payment_account', false)
}
/**
 * 1、是否隐藏本位币  2、隐藏海外本位币币种
 */
export const onlyOneCurrencyHideSeaLocalCurrrency = () => {
  return getBoolVariation('only-one-hide-local-currrency', false)
}

export const asyncOCRinvoice = () => {
  return  getBoolVariation('async-ocr-invoice', false)
}
/*
 * 隐藏新费用选择器内的类型编码
 * @link https://jira.hosecloud.com/browse/CYXQ-70919
 */
export const enableHidingFeeTypeCode = () => {
  return getBoolVariation('cyxq-70919-hide-feeType-code', true)
}

/**
 * 报销金额使用接口数据进行展示
 */
export const useApiExpenseMoney = () => {
  return getBoolVariation('cyxq-70702-use-api-expense-money', false)
}

export const enableNewBillOptimization = () => {
  return getBoolVariation('web-new-bill-optimization', false)
}

/**
 * 部门可见性
 */
export const useDepartmentVisible = () => {
  return getBoolVariation('mfrd-3133-department-visible', false)
}

/**
 * 当点击单据详情中的body时，是否关闭抽屉，默认关闭
 */
export const ifCloseWhenClickBody = () => {
  return getBoolVariation('mfrd-2935-if-close-when-click-body', false)
}

/**
 * 自定义档案加载优化
 */
export const enableRecordOptimization = () => {
  return getBoolVariation('mfrd-3830-record-optimization', false)
}


/**
 * 是否启用员工API切换
 * @link https://jira.hosecloud.com/browse/MFRD-4278
 */
export const enableStaffApiSwitch = () => {
  return getBoolVariation('mfrd-4278-staff-api-switch', false)
}


export const enableOtherInvoiceByDimension = () => {
  return getBoolVariation('fird-4844-invoice-by-dimension', false)
}
/**
 * 【无需测试】【在线客服】web端客户入口和移动端顶部导航栏客服入口对接天润客服离线消息
 * @link https://jira.hosecloud.com/browse/MFRD-3923
 */
export const enableSupportOfflineMessage = () => {
  return getBoolVariation('mfrd-3923-support-offline', false)
}

export const enableMoneyOptimization = () => {
  return getBoolVariation('mfrd-3678-money-optimization', false)
}
/**
 * 【单据焕新】【二期】【查看态】「我的单据」去除已完成操作
 * @link https://jira.hosecloud.com/browse/MFRD-3156
 */
export const enableHidingFinishedBills = () => {
  return getBoolVariation('mfrd-3156-get-rid-of-finished-bills', false)
}


/**
 * 【单据焕新】切换单据且保持外部加载，先关闭单据—>加载—>打开新单据
 * 因为依赖于自定义按钮的逻辑，所以公用一个featbit
 * @link https://jira.hosecloud.com/browse/MFRD-4163
*/
export const supportBillDetailsSwitchingInDrawer = () => {
  return getBoolVariation('custom-extend-button', false)
}

/**
 * 单据审批意见输入框增大
 */
export const enableCommentOptimization = () => {
  return getBoolVariation('mfrd-4689', false)
}

export const enableFlowOptimization = () => {
  return getBoolVariation('cyxq-77804-flow-optimization', false)
}

/**
 * 【赢家】更新单据模板收款信息可见性清空校验
 * @link https://jira.hosecloud.com/browse/MFRD-4595
*/
export const enableCheckingVisibilityBeforeClearingAccounts = () => {
  return getBoolVariation('mfrd-4595', false)
}

/**
 * 自定义按钮
*/
export const enableCustomExtendButton = () => {
  return getBoolVariation('custom-extend-button', false)
}

export const enableRecordSearchOptimization = () => {
  return getBoolVariation('cyxq-78696-record-search', false)
}
