/*****************************************
 * AUTHOR : nanyuantingfeng
 * DATE : 3/23/16
 * TIME : 12:03
 ****************************************/
@import '~@ekuaibao/web-theme-variables/styles/default';
@import 'antd-cover';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';
@import '~@ekuaibao/theme-variables/styles/global.less'; // 全局字体颜色类

////keel组件的title样式控制
////单栏
//.navigation-bar-title-single {
//  color: #00a854 !important;
//}
//
////多栏左侧
//.navigation-bar-title-main{
//  color: palevioletred !important;
//}
//
////多栏右侧
//.navigation-bar-title-sub{
//  color: #ed9634 !important;
//}

#top-progress-bar-style .bar {
  background: @primary-6 !important;
}

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

//设置一行超出字数后显示 ...
.word-break() {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

html {
  width: 100%;
  height: 100%;
  min-width: 1000px;
  min-height: 400px;
  overflow: auto;
  -webkit-font-smoothing: antialiased;
}

a {
  text-decoration: none;
}

ul,
li,
ol {
  list-style: none;
}

ul {
  padding-left: 0;
}

//body {
//  font-family : SF UI Display, PingFang SC, Neue Haas Grotesk Text Pro, Arial Nova, Segoe UI, Microsoft YaHei, Microsoft JhengHei, Helvetica, Hiragino Sans GB, Helvetica Neue, Source Han Sans SC, Noto Sans CJK SC, Source Han Sans CN, Noto Sans SC, Source Han Sans TC, Noto Sans CJK TC, sans-serif !important;
//}

textarea {
  resize: none;
}

.nav-line {
  margin: 16px 0;
  width: 100%;
  height: 1px;
  background: #4e5e7a;
}

body {
  font-family: @font-family;
  background-color: @body-background;
}

.body-gray {
  -webkit-filter: grayscale(100%);
  /* webkit */
  -moz-filter: grayscale(100%);
  /*firefox*/
  -ms-filter: grayscale(100%);
  /*ie9*/
  -o-filter: grayscale(100%);
  /*opera*/
  filter: grayscale(100%);
  filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
  filter: gray;
  /*ie9- */
}

.option-line {
  display: flex;
  align-items: center;
}

.data-link-table-text-style-new {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

.overflow-ellipsis-line-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.flex {
  flex-grow: 1;
}

.flex-110 {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0;
}

.grow {
  flex: 1;
}

.hidden {
  display: none;
}

.select-disable {
  user-select: none;
}

.unselectable {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

.stand-icon {
  border-radius: @radius-2;
  padding: @space-3;
  width: 32px !important;
  height: 32px !important;

  &:hover {
    background-color: @color-bg-2;
  }
}

.stand-16-icon {
  width: 16px !important;
  height: 16px !important;
}

.stand-20-icon {
  width: 20px;
  height: 20px;
}

.stand-24-icon {
  width: 24px;
  height: 24px;
}

.stand-28-icon {
  width: 28px;
  height: 28px;
}

.stand-44-icon {
  width: 44px;
  height: 44px;
}

.valid-error {
  .ant-select-selection {
    border: 1px solid @error-color;
  }

  .valid {
    border: 1px solid @error-color;
  }
}

.valid-error-input {
  border: 1px solid @error-color;
}

.valid-success {
  span.valid {
    border: none;
  }
}

.input-placeholder-color {
  color: @input-placeholder-color !important;
}

.vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.horizontal {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.center {
  .vertical;
  justify-content: center;
}

.center-horizontal {
  .horizontal;
  justify-content: center;
}

.scrollable {
  overflow-x: hidden;
  overflow-y: auto;
}

.flip-box {
  position: relative;

  .flip {
    position: absolute;
    width: 0;
    height: 0;
    overflow: hidden;
  }

  &:hover .flip {
    width: auto;
    height: auto;
  }
}

/* ==================== 布局 ==================== */
.dis-b {
  display: block;
}

.dis-f {
  display: flex;
}

.dis-i {
  display: inline;
}

.dis-ib {
  display: inline-block;
}

.dis-if {
  display: inline-flex;
}

.dis-none {
  display: none;
}

.dis-t {
  display: table;
}

.pos-r {
  position: relative;
}

.pos-a {
  position: absolute;
}

.pos-f {
  position: fixed;
}

.pos-s {
  position: static;
}

.fl-l {
  float: left;
}

.fl-r {
  float: right;
}

.fl-n {
  float: none;
}

.flex-1 {
  flex: 1;
}

.fd-c {
  flex-direction: column;
}

.flex-s-0 {
  flex-shrink: 0;
}

.fd-r {
  flex-direction: row;
}

.jc-c {
  justify-content: center;
}

.jc-s {
  justify-content: flex-start;
}

.jc-e {
  justify-content: flex-end;
}

.jc-sa {
  justify-content: space-around;
}

.jc-sb {
  justify-content: space-between;
}

.ai-c {
  align-items: center;
}

.ai-b {
  align-items: baseline;
}

.ai-fs {
  align-items: flex-start;
}

.ai-fe {
  align-items: flex-end;
}

.ai-s {
  align-items: stretch;
}

.ovr-h {
  overflow: hidden;
}

.ovr-a {
  overflow: auto;
}

.ovr-s {
  overflow: scroll;
}

.ovr-x-h {
  overflow-x: hidden;
}

.ovr-x-a {
  overflow-x: auto;
}

.ovr-y-h {
  overflow-y: hidden;
}

.ovr-y-a {
  overflow-y: auto;
}

/* ==================== 文字排版 ==================== */
.ta-l {
  text-align: left;
}

.ta-r {
  text-align: right;
}

.ta-c {
  text-align: center;
}

.wb-bw {
  word-break: break-all;
}

.wb-ka {
  word-break: keep-all;
}

.wb-n {
  word-break: normal;
}

.fs-0 {
  font-size: 0;
}

.fs-10 {
  font-size: 10px;
}

.fs-12 {
  font-size: 12px;
}

.fs-13 {
  font-size: 13px;
}

.fs-14 {
  font-size: 14px;
}

.fs-15 {
  font-size: 15px;
}

.fs-16 {
  font-size: 16px;
}

.fs-17 {
  font-size: 17px;
}

.fs-18 {
  font-size: 18px;
}

.fs-20 {
  font-size: 20px;
}

.fs-24 {
  font-size: 24px;
}

.fw-b {
  font-weight: bold;
}

.fw-n {
  font-weight: normal;
}

.fw-500 {
  font-weight: 500;
}

.fw-600 {
  font-weight: 600;
}

.ti-2 {
  text-indent: 2em;
}

.td-u {
  text-decoration: underline;
}

.td-n {
  text-decoration: none;
}

.ti-999 {
  text-indent: -9999em;
}

/* ==================== 鼠标指针 ==================== */
.cur-d {
  cursor: default;
}

.cur-p {
  cursor: pointer;
}

.cur-m {
  cursor: move;
}

.cur-t {
  cursor: text;
}

.cur-w {
  cursor: wait;
}

.cur-c {
  cursor: crosshair;
}

.cur-h {
  cursor: help;
}

.cur-n {
  cursor: not-allowed;
}

.rounded-8px {
  border-radius: 8px;
}

/* ==================== 字体颜色 ==================== */
.color-white,
a.color-white,
.color-white a {
  color: #ffffff;
}

.color-black,
a.color-black,
.color-black a {
  color: @text-color;
}

.color-gray,
a.color-gray,
.color-gray a {
  color: @color-black-3 !important;
}

.color-black-1 {
  color: @color-black-1;
}

.color-gray-8c,
a.color-gray-8c,
.color-gray-8c a {
  color: #8c8c8c;
}

.color-gray-9c,
a.color-gray-9c,
.color-gray-9c a {
  color: #9c9c9c;
}

.color-gray-9e,
a.color-gray-9e,
.color-gray-9e a {
  color: #9e9e9e;
}

.color-blue,
a.color-blue,
.color-blue a {
  color: var(--brand-base);
}

.color-red,
a.color-red,
.color-red a {
  color: #f17b7b;
}

.color-gray-c {
  color: #9c9c9c;
}

.color-orange,
a.color-orange,
.color-orange a {
  color: #ed9634;
}

.color-gray-7 {
  color: @gray-7;
}

.color-gray-8 {
  color: @gray-8;
}

.color-gray-9 {
  color: @gray-9;
}

.color-blue2 {
  color: var(--brand-base);
}

.color-blue3 {
  color: #197cd9;
}

/* ==================== 背景 ==================== */
.bg-gray {
  background: #f5f5f5;
}

/* ==================== 宽度栅格化 ==================== */
.w-10p {
  width: 10%;
}

.w-20p {
  width: 20%;
}

.w-30p {
  width: 30%;
}

.w-40p {
  width: 40%;
}

.w-50p {
  width: 50%;
}

.w-60p {
  width: 60%;
}

.w-70p {
  width: 70%;
}

.w-80p {
  width: 80%;
}

.w-90b {
  width: 90%;
}

.w-100b {
  width: 100%;
}

/* ==================== 高度栅格化 ==================== */
.h-10b {
  height: 10%;
}

.h-20b {
  height: 20%;
}

.h-30b {
  height: 30%;
}

.h-40b {
  height: 40%;
}

.h-50b {
  height: 50%;
}

.h-60b {
  height: 60%;
}

.h-70b {
  height: 70%;
}

.h-80b {
  height: 80%;
}

.h-85b {
  height: 85%;
}

.h-90b {
  height: 90%;
}

.h-100b {
  height: 100%;
}

.min-h-670 {
  min-height: 670px;
}

/* ==================== 宽度 ==================== */
.w-0 {
  width: 0;
}

.w-10 {
  width: 10px;
}

.w-18 {
  width: 18px;
}

.w-20 {
  width: 20px;
}

.w-24 {
  width: 24px;
}

.w-30 {
  width: 30px;
}

.w-40 {
  width: 40px;
}

.w-50 {
  width: 50px;
}

.w-60 {
  width: 60px;
}

.w-70 {
  width: 70px;
}

.w-80 {
  width: 80px;
}

.w-90 {
  width: 90px;
}

.w-100 {
  width: 100px;
}

.w-110 {
  width: 110px;
}

.w-120 {
  width: 120px;
}

.w-130 {
  width: 130px;
}

.w-140 {
  width: 140px;
}

.w-150 {
  width: 150px;
}

.w-160 {
  width: 160px;
}

.w-170 {
  width: 170px;
}

.w-180 {
  width: 180px;
}

.w-190 {
  width: 190px;
}

.w-200 {
  width: 200px;
}

.w-210 {
  width: 210px;
}

.w-220 {
  width: 220px;
}

.w-230 {
  width: 230px;
}

.w-240 {
  width: 240px;
}

.w-250 {
  width: 250px;
}

.w-260 {
  width: 260px;
}

.w-270 {
  width: 270px;
}

.w-280 {
  width: 280px;
}

.w-290 {
  width: 290px;
}

.w-300 {
  width: 300px;
}

.w-360 {
  width: 360px;
}

.w-600 {
  width: 600px;
}

.mw-150 {
  max-width: 150px;
}

.mw-400 {
  max-width: 400px;
}

.mw-560 {
  max-width: 560px;
}

.mw-45-percent {
  max-width: 45%;
}

/* ==================== 高度 ==================== */
.h-0 {
  height: 0;
}

.h-10 {
  height: 10px;
}

.h-18 {
  height: 18px;
}

.h-20 {
  height: 20px;
}

.h-24 {
  height: 24px;
}

.h-30 {
  height: 30px;
}

.h-32 {
  height: 32px;
}

.h-40 {
  height: 40px;
}

.h-50 {
  height: 50px;
}

.h-60 {
  height: 60px;
}

.h-70 {
  height: 70px;
}

.h-80 {
  height: 80px;
}

.h-90 {
  height: 90px;
}

.h-100 {
  height: 100px;
}

.h-110 {
  height: 110px;
}

.h-120 {
  height: 120px;
}

.h-130 {
  height: 130px;
}

.h-140 {
  height: 140px;
}

.h-150 {
  height: 150px;
}

.h-160 {
  height: 160px;
}

.h-170 {
  height: 170px;
}

.h-180 {
  height: 180px;
}

.h-190 {
  height: 190px;
}

.h-200 {
  height: 200px;
}

.h-300 {
  height: 200px;
}

.h-600 {
  height: 600px;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ==================== ant-design ==================== */

.ant-tabs-tab-btn-disabled {
  display: none;
}

/* ==================== position ==================== */
.t-50 {
  top: 50px;
}

/* ==================== margin ==================== */
.mt-0 {
  margin-top: 0;
}

.mt-2 {
  margin-top: 2px;
}

.mt-3 {
  margin-top: 3px;
}

.mt-4 {
  margin-top: 4px;
}

.mt-5 {
  margin-top: 5px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-24 {
  margin-top: 24px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-90 {
  margin-top: 90px;
}

.mr-0 {
  margin-right: 0;
}

.mr-2 {
  margin-right: 2px;
}

.mr-4 {
  margin-right: 4px;
}

.mr-5 {
  margin-right: 5px;
}

.mr-8 {
  margin-right: 8px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-12 {
  margin-right: 12px;
}

.mr-16 {
  margin-right: 16px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-30 {
  margin-right: 30px;
}

.mr-40 {
  margin-right: 40px;
}

.mr-50 {
  margin-right: 50px;
}

.mr-360 {
  margin-right: 360px;
}

.mr-400 {
  margin-right: 400px;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-5 {
  margin-bottom: 5px;
}

.mb-4 {
  margin-bottom: 4px;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.ml-0 {
  margin-left: 0;
}

.ml-2 {
  margin-left: 2px;
}

.ml-4 {
  margin-left: 4px;
}

.ml-5 {
  margin-left: 5px;
}

.ml-6 {
  margin-left: 6px;
}

.ml-8 {
  margin-left: 8px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-12 {
  margin-left: 12px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-16 {
  margin-left: 16px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-24 {
  margin-left: 24px;
}

.ml-25 {
  margin-left: 25px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-40 {
  margin-left: 40px;
}

.ml-50 {
  margin-left: 50px;
}

/* ==================== padding ==================== */
.pt-0 {
  padding-top: 0;
}

.pt-5 {
  padding-top: 5px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-24 {
  padding-top: 24px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pr-0 {
  padding-right: 0;
}

.pr-5 {
  padding-right: 5px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-16 {
  padding-right: 16px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-24 {
  padding-right: 24px;
}

.pr-30 {
  padding-right: 30px;
}

.pr-40 {
  padding-right: 40px;
}

.pr-50 {
  padding-right: 50px;
}

.pb-0 {
  padding-bottom: 0;
}

.pb-5 {
  padding-bottom: 5px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pl-0 {
  padding-left: 0;
}

.pl-5 {
  padding-left: 5px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-12 {
  padding-left: 12px;
}

.pl-16 {
  padding-left: 16px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-24 {
  padding-left: 24px;
}

.pl-30 {
  padding-left: 30px;
}

.pl-32 {
  padding-left: 32px;
}

.pl-40 {
  padding-left: 40px;
}

.pl-50 {
  padding-left: 50px;
}

.p-0 {
  padding: 0;
}

.p-lr-8 {
  padding: 0 8px !important;
}

.p-2 {
  padding: 2px;
}

.p-4 {
  padding: 4px;
}

.p-10 {
  padding: 10px;
}

.p-16 {
  padding: 16px;
}

.p-20 {
  padding: 20px;
}

.p-24 {
  padding: 24px;
}

.p-30 {
  padding: 30px;
}

.p-40 {
  padding: 40px;
}

.p-50 {
  padding: 50px;
}

.m-0 {
  margin: 0;
}

.m-10 {
  margin: 10px;
}

.m-20 {
  margin: 20px;
}

.m-30 {
  margin: 30px;
}

.m-40 {
  margin: 40px;
}

.m-50 {
  margin: 50px;
}

//right
.r-25 {
  right: 25px !important;
}

.r-28 {
  right: 28px !important;
}

.f-fd {
  font-family: DIN Alternate;
}

.base-bg {
  background-color: var(--eui-bg-base);
}

//antd custom style

.modal-no-padding {
  .ant-modal-body,
  .eui-modal-body {
    padding: 0;
  }
}

.modal-fix-top10 {
  .ant-modal-body {
    & > div {
      margin-top: 10px;
    }
  }
}

.modal-no-footer {
  .ant-modal-footer,
  .eui-modal-footer {
    display: none;
  }
}

.modal-custom-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: rgba(29, 43, 61, 1);
  border-bottom: none;

  .cross-icon {
    font-size: 16px;
    cursor: pointer;
  }
}

.modal-custom-footer-btn {
  font-size: 14px;
  border-radius: 4px;
  border: none;
  font-weight: 400;
  background-color: rgba(29, 43, 61, 0.06);
  color: #1d2b3d;

  &:active,
  &:hover {
    color: var(--brand-base);
  }

  &[disabled] {
    color: #cbcbcb;
    background-color: #f7f7f7;
  }
}

.modal-custom-footer-btn-primary {
  background-color: var(--brand-base);
  color: rgba(255, 255, 255, 1);

  &:active,
  &:hover {
    color: rgba(255, 255, 255, 1);
    background-color: var(--brand-fadeout-70);
  }

  &[disabled] {
    color: #cbcbcb;
    background-color: #f7f7f7;
  }
}

.modal-custom-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 56px;
  padding: 0 16px;
  box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2);
  justify-content: flex-end;
  border-top: none;
  background-color: #fff;

  .btn-ml {
    margin-left: 8px;
  }

  .ant-btn {
    .modal-custom-footer-btn;
  }

  .ant-btn-primary:not(.ant-btn-danger) {
    .modal-custom-footer-btn-primary;
  }
}

.white-popup-close {
  .ant-drawer-content-wrapper {
    .ant-drawer-wrapper-body {
      .ant-drawer-close {
        color: @color-white-1;
      }

      .ant-drawer-body {
        overflow: hidden;
      }
    }
  }
}

.travel-assistant-drawer {
  .ant-drawer-content-wrapper {
    min-width: 473px !important;
  }
}

.min400-ant-drawer-content-wrapper {
  .ant-drawer-content-wrapper {
    min-width: 400px !important;
  }
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  &.high-z-index-modal {
    z-index: 1031; // 解决问题：popover点出的弹窗被popover盖住
  }

  .ant-modal,
  .eui-modal {
    top: 0;
  }

  .ant-modal-content,
  .eui-modal-content {
    flex: 1;
    overflow: hidden;
    border-radius: 8px;

    .ant-modal-close-x,
    .eui-modal-close-x {
      // font-size: 20px;
      font-size: 16px;
    }

    .ant-modal-header,
    .eui-modal-header {
      border-top-left-radius: inherit;
      border-top-right-radius: inherit;
      border-bottom: none;

      .ant-modal-title,
      .eui-modal-title {
        font: var(--eui-font-head-b1);
        color: var(--eui-text-title);
      }
    }

    .ant-modal-footer {
      box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2);
      border-top: none;

      .ant-btn:not(.ant-btn-danger) {
        .modal-custom-footer-btn;
      }

      .ant-btn-primary:not(.ant-btn-danger) {
        .modal-custom-footer-btn-primary;
      }
    }
  }
}

.ekb-custom-modal {
  position: relative;
  color: @text-color;
  overflow-x: hidden;
  background: #ffffff;
  padding: 0;
  border-radius: 8px;

  .ant-modal-content,
  .eui-modal-content {
    .ant-modal-close,
    .eui-modal-close {
      display: none;
    }
  }

  .modal-no-padding;
  .modal-no-footer;

  .modal-content {
    overflow-x: hidden;
    overflow-y: auto;
  }

  .modal-header {
    height: 50px;
    padding: 0 24px;
    .modal-custom-header;
  }

  .modal-footer {
    .modal-custom-footer;
    flex-shrink: 0;
  }

  .modal-footer-button {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    padding: 12px 16px;

    .btn-ml {
      margin-right: 16px;
    }
  }
}

.hidden-cancel-button {
  .ant-modal-footer {
    display: none;
  }
}

.respond-modal-layer {
  position: absolute;
  top: 40px !important;
  right: 20px;
  bottom: 40px;
  left: 20px;
  color: @text-color;
  overflow: hidden;
  background: #ffffff;
  padding: 0;
  border-radius: 8px !important;

  .ant-modal-content,
  .eui-modal-content {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    border-radius: 8px;

    .ant-modal-close,
    .eui-modal-close {
      display: none;
    }
  }

  .ant-modal-footer,
  .eui-modal-footer {
    display: none;
  }

  .ant-modal-body,
  .eui-modal-body {
    padding: 0;
    height: 100%;
  }

  .modal-header {
    padding: 16px;
    background: rgba(255, 255, 255, 1);
    border-top-right-radius: 12px;
    border-top-left-radius: 12px;
    //z-index: 99;
    position: relative;
    .modal-custom-header;
  }

  .modal-content {
    overflow-x: hidden;
    overflow-y: auto;
  }

  .modal-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;

    .modal-custom-footer;
  }

  .drawer {
    transition: all 0.5s ease;
  }
}

.fullScreenDrawer {
  .ant-drawer-content-wrapper {
    width: 100% !important;
  }

  .eui-drawer-content-wrapper {
    width: 100% !important;
  }
}

.respond-modal-layer-fullScreen {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  color: @text-color;
  overflow: hidden;
  background: #ffffff;
  padding: 0;

  .ant-modal-content {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    border-radius: 12px;

    .ant-modal-close {
      display: none;
    }
  }

  .ant-modal-footer {
    display: none;
  }

  .ant-modal-body {
    padding: 0;
    height: 100%;
  }

  .modal-header {
    padding: 16px;
    background: rgba(255, 255, 255, 1);
    border-top-right-radius: 12px;
    border-top-left-radius: 12px;
    //z-index: 99;
    position: relative;
    .modal-custom-header;
  }

  .modal-content {
    overflow-x: hidden;
    overflow-y: auto;
  }

  .modal-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;

    .modal-custom-footer;
  }

  .drawer {
    transition: all 0.5s ease;
  }
}

.custom-modal-layer-no-footer {
  position: relative;
  color: @text-color;
  overflow: hidden;
  background: #ffffff;
  padding: 0;
  border-radius: 12px;

  .ant-modal-content,
  .eui-modal-content {
    overflow: inherit;
    border-radius: inherit;

    .ant-modal-close,
    .eui-modal-close {
      display: none;
    }
  }

  .ant-modal-footer,
  .eui-modal-footer {
    display: none;
  }

  .ant-modal-body,
  .eui-modal-body {
    padding: 0;
  }

  .modal-header {
    padding: 16px;
    background: rgba(255, 255, 255, 1);
    .modal-custom-header;
  }

  .modal-header-v2 {
    padding: 16px;
    background: rgba(255, 255, 255, 1);
    .modal-custom-header;
  }

  .modal-content {
    overflow-x: hidden;
    overflow-y: auto;
  }

  .modal-footer {
    .modal-custom-footer;
  }

  .modal-footer-v2 {
    .modal-custom-footer;
  }

  .modal-footer-start {
    justify-content: flex-start;
  }

  .drawer {
    transition: all 0.5s ease;
  }
}

.eui-modal.custom-modal-layer {
  // EUI 规范
  border-radius: 8px;
}

.custom-modal-layer {
  .custom-modal-layer-no-footer;

  .ant-modal-content {
    .ant-modal-close {
      display: none;
    }
  }
  &.eui-close{
    .eui-modal-close{
      display: block!important;
    }
  }

  &.custom-modal-layer-for-select-modal {
    overflow: visible;
  }
  // 使用eui 那一套规范
  &.eui-modal-layer{
    .modal-header{
      padding: 16px;
      font-size: 16px;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
      font:var(--eui-font-head-b1);
    }
    .eui-icon-OutlinedTipsClose{
      cursor: pointer;
      color: var(--eui-icon-n2);
      font-size: 16px;
    }
  }
}


.new-custom-modal-layer {
  .custom-modal-layer-no-footer;
  box-shadow: 0px 8px 24px rgba(20, 34, 52, 0.24);
  border-radius: 4px;

  .ant-modal-content {
    .ant-modal-close {
      display: none;
    }
  }

  &.custom-modal-layer-for-select-modal {
    overflow: visible;
  }
}

mark {
  background: none;

  &.highlight {
    color: #f17b7b;
  }
}

.ekb-custom-content {
  .ant-modal-body {
    padding: 0;
  }
}

.ant-form-item {
  color: var(--eui-text-title);

  label {
    font-size: 14px;
  }
}

.text-nowrap-ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.text-nowrap-ellipsis-3-lines {
  display: -webkit-box !important;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ws-nowrap {
  white-space: nowrap !important;
}

.clear_borderhover {
  &:hover {
    box-shadow: 0 0 0;
  }
}

.respond-modal-layer-closable {
  .respond-modal-layer;

  .ant-modal-content {
    .ant-modal-close {
      display: block;
    }
  }
}

.respond-modal-layer-closable-body {
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
  display: flex;
}

//复制弹窗的样式
.confirmCopyModal-wrapper {
  .ant-confirm-title {
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .ant-confirm-content {
    font-size: 12px;
    line-height: 20px;
  }
}

// IE10 上传 input 光标闪烁
.ekb-files-uploader-wrapper {
  .input-wrapper {
    .ekb-files-input {
      font-size: 0 !important;
      width: 95px;
    }
  }
}

.data_link_number_red_color {
  color: #f5222d;
}

#intercom-container {
  z-index: 103 !important;
}

#intercom-container .intercom-launcher-discovery-frame,
#intercom-container .intercom-launcher-frame {
  visibility: hidden;
  display: none;
}

#intercom-container .intercom-gradient {
  visibility: hidden;
  display: none;
}

#intercom-container .intercom-launcher-badge-frame {
  visibility: hidden;
  display: none;
}

#intercom-container .intercom-messenger-frame,
#intercom-container .intercom-note {
  right: 20px !important;
}

#intercom-container .intercom-notifications-frame,
#intercom-container .intercom-borderless-frame {
  right: 10px !important;
}

.gpyModel {
  .custom-modal-layer {
    background: #292929 !important;
  }
}

.immersive-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  .font-size-5;
  .font-weight-3;
}

.dropdown-filter-menu {
  padding: @space-4 0 !important;

  .ant-dropdown-menu-item-selected,
  .ant-dropdown-menu-item-active {
    background-color: @color-bg-2;
  }

  .menu-item {
    color: @color-brand-2;
    display: flex;
    justify-content: space-between;
    .font-size-2;
    align-items: center;
  }
}

.disable-btn-class {
  color: @color-black-4;
}

.print-modal-wrapper {
  .anticon-info-circle {
    color: rgba(24, 144, 255, 1) !important;
  }

  .ant-confirm-title {
    color: @color-black-1;
    .font-size-4;
    .font-weight-3;
  }

  .ant-confirm-content {
    margin-top: @space-4;
    .font-size-2;
    .font-weight-2;
    color: @color-black-3;
  }
}

.confirm-modal-wapper {
  .ant-modal-content {
    border-radius: @space-5;

    .ant-confirm-body {
      .ant-confirm-title {
        color: @color-black-1;
        .font-size-4;
        .font-weight-3;
      }

      .ant-confirm-content {
        margin-top: @space-4;
        .font-size-2;
        .font-weight-2;
        color: @color-black-3;
      }
    }

    .ant-confirm-btns {
      .ant-btn {
        border-radius: @radius-2;
      }

      .ant-btn:not(.ant-btn-danger):first-child:not(:last-child) {
        background: rgba(29, 43, 61, 0.06);
      }
    }
  }
}

.ant-confirm-confirm.confirmDelModal-wrapper {
  .ant-modal-content {
    box-shadow: 0px 8px 32px 0px rgba(29, 43, 61, 0.2);
    border-radius: 12px;

    .ant-confirm-btns {
      .ant-btn {
        .modal-custom-footer-btn;
      }

      .ant-btn-primary {
        color: rgba(244, 82, 107, 1);
        background: rgba(244, 82, 107, 0.1);

        &:active,
        &:hover {
          color: rgba(244, 82, 107, 1);
          background: rgba(244, 82, 107, 0.2);
        }

        &[disabled] {
          color: #cbcbcb;
          background-color: #f7f7f7;
        }
      }
    }
  }
}

.reconciliation-modal-info {
  .ant-confirm-body .ant-confirm-content {
    font-size: 14px;
    color: var(--brand-base);
  }

  .ant-confirm-btns {
    display: none;
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:global {
  .message-time-hour {
    .ant-time-picker-panel-inner {
      overflow: initial;
    }
  }

  .ant-select-dropdown-menu-item.ant-select-dropdown-menu-item-selected {
    .sort-invoices-item {
      color: var(--brand-base);
    }
  }

  .batch-show-agree-modal {
    box-sizing: border-box;
    //min-height: 60px;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 120px;
    padding: 8px 16px;
    border-radius: 4px;
    background-color: rgba(29, 43, 61, 0.03);

    .batch-item {
      font-size: 14px;
      line-height: 22px;
      color: rgba(29, 43, 61, 0.5);
      margin: 4px 0;

      .mark {
        width: 0;
        height: 0;
        display: inline-block;
        vertical-align: middle;
        border: 2px solid rgba(29, 43, 61, 0.5);
      }
    }
  }

  .trip-card-box {
    max-width: 400px;

    .ant-tooltip-inner {
      max-height: 450px;
    }
  }

  ::placeholder {
    color: var(--font-color-secondary);
  }

  .anticon-check {
    color: var(--brand-base);
  }

  /* 审批中修改单据，可修改字段需要高亮显示 https://hose2019.feishu.cn/wiki/wikcnSiV2s46oKobp7ip35WSmCd */
  #modify-bill-wrapper #bill-info-editable-container.highlight-modifiable-field,
  #FeeDetailView .highlight-modifiable-field[class*='fee-detail-edit-wrapper'] {
    .ant-form-item-control-wrapper {
      & .ant-input:not([class*=disabled]),
      & .eui-input:not([class*=disabled]),
      & .ant-input-number:not([class*=disabled]) .ant-input-number-input,
      & .eui-input-number:not([class*=disabled]) .eui-input-number-input,
      & .ant-select:not([class*=disabled]) .ant-select-selection,
      & .eui-select:not([class*=disabled]) .eui-select-selection,
      & .rc-select:not([class*=disabled]) .rc-select-selection,
      & .ant-calendar-picker:not([class*=disabled]) .ant-calendar-picker-input,
      & .eui-calendar-picker:not([class*=disabled]) .eui-calendar-picker-input,
      /* 业务对象 */
      & div[class*=ledger-card] .card-body:not(:only-child),
      /* 人员多选 */
      & .mutil-staff:not([class*=disabled]),
      /* 附件 */
      & .attachment:not([class*=disabled]) .attachment-item div[class*=file-item],
      /* 业务对象 */
      & div[class*=data-link-input-wrapper] .link-card-box,
      /* 关联申请 */
      & div[class*=expenselink-wrapper]:not([class*=disabled]) .import-select,
      /* 发票 */
      & div[class*=invoice-select-wrapper]:not([class*=disabled]) div[class*=invoice-item-wrapper],
      /* 员工、收款信息 */
      & div[class*=fake_input_wrapper]:not([class*=disabled]) {
        // background: var(--eui-illustration-brand-f);
        background: rgba(232, 241, 255, 1);
        border-color: transparent;
      }

      & .ant-input-number:not([class*=disabled]),
      & .eui-input-number:not([class*=disabled]),
      /* 关联申请 */
      & div[class*=expenselink-wrapper]:not([class*=disabled]) .import-select .info-item-wrapper {
        border-color: transparent;
      }

      & .ant-input:not([class*=disabled]),
      & .eui-input:not([class*=disabled]),
      & .ant-input-number:not([class*=disabled]) .ant-input-number-input,
      & .eui-input-number:not([class*=disabled]) .eui-input-number-input,
      & .ant-select:not([class*=disabled]) .ant-select-selection,
      & .eui-select:not([class*=disabled]) .eui-select-selection,
      /* 员工、收款信息 */
      & div[class*=fake_input_wrapper]:not([class*=disabled]),
      /* 人员多选 */
      & .mutil-staff:not([class*=disabled]),
      /* 业务对象 */
      & div[class*=data-link-input-wrapper] .link-card-box,
      /* 附件 */
      & .attachment:not([class*=disabled]) .attachment-item div[class*=file-item] {
        &:hover {
          border: 1px solid var(--brand-base);
        }
      }

      & .ant-input:not([class*='disabled']),
      & .eui-input:not([class*='disabled']),
      & .ant-input-number:not([class*='disabled']) .ant-input-number-input {
        &:focus {
          background-color: transparent;
          border: 1px solid var(--brand-base);
        }
      }
      & .eui-input-number:not([class*='disabled']) .eui-input-number-input {
        &:focus {
          background-color: transparent;
          border: 1px solid var(--brand-base);
        }
      }

      /* 附件 */
      & .attachment:not([class*='disabled']) div[class*='attachment-list'] {
        margin-top: 12px;
      }

      /* 业务对象 */

      & div[class*='ledger-card'] .card-body:not(:only-child) {
        border-radius: 2px;
      }

      /* 业务对象 */
      & div[class*='data-link-input-wrapper'] .link-card-box {
        margin-bottom: 6px;
        border-radius: 2px;
      }
    }

    /* 复选框 */
    label[class*=switch-edit-wrap]:not([class*=disabled]),
    /* 核销 */
    & div[class*=WrittenOff-part]:not([class*=disabled]) .writtenoff-wrapper,
    /* 费用明细 */
    #details-container div[class*=details-wrapper] {
      // background: var(--eui-illustration-brand-f);
      background: rgba(232, 241, 255, 1);
      border-radius: 2px;
    }

    /* 复选框 */
    label[class*='switch-edit-wrap']:not([class*='disabled']) {
      display: inline-flex;
      padding: 6px 8px;
      box-sizing: border-box;
    }
  }
}

.billfill-modal-wrap {
  top: -100px;

  .billfill-modal {
    .ant-modal-content {
      box-shadow: 0 4px 24px 0 rgb(20 34 52 / 16%);

      .ant-modal-body {
        height: 312px;

        .ant-btn {
          border-radius: 4px;
          border-width: 0;

          &.primary {
            color: #fff;
            background: rgba(20, 34, 52, 0.88);
          }

          &.default {
            color: rgba(20, 34, 52, 0.96);
            background: rgba(20, 34, 52, 0.06);
          }
        }
      }

      .ant-modal-close {
        display: none;
      }
    }
  }
}

.adjustingBillsDrawer-modal {
  .ant-drawer-content-wrapper {
    width: 1200px !important;
  }
}

:global {
  /**
   * 由于 webpack 打包的问题，通过 api 调用之后渲染的组件存在样式无法调用的情况。
   * 所以先直接写入 web 主包中保证正常显示。
   * 这段样式属于子包 @ekuaibao/plugin-web-print ，异步打印提示弹窗
   */
  .async-print-task-notification {
    padding: 0;
    border-radius: 8px;

    .ant-notification-notice-close,
    .ant-notification-notice-message {
      display: none;
    }

    .async-print-task-item {
      padding: 4px;

      &-wrapper {
        padding: 12px 16px;
        display: flex;
        width: 100%;
        align-items: center;
        border-radius: 8px;

        &:hover {
          background: var(--eui-fill-hover);
        }
      }

      &-status {
        padding-top: 2px;
      }

      &-content {
        flex: 1;
      }

      &-icon {
        font-size: 24px;
        padding-right: 12px;
        line-height: initial;

        .eui-icon {
          position: relative;
          top: -2px;
        }
      }
    }

    .async-print-task-item-title {
      color: var(--eui-text-title);
      font: var(--eui-font-body-b1);
    }

    .async-print-task-progress-message {
      font: var(--eui-font-note-r2);
      color: var(--eui-text-caption);

      .eui-icon {
        margin-right: 4px;
        color: var(--eui-primary-pri-500);

        &.success {
          color: var(--eui-function-success-500);
        }

        &.error {
          color: var(--eui-function-danger-500);
        }
      }
    }

    .collapse-layout {
      &-header {
        padding: 20px;
        border: 1px solid var(--eui-line-divider-default);
        display: flex;
      }

      &-title {
        font: var(--eui-font-head-b1);
        flex: 1;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      &-actions {
        padding-left: 12px;

        * + * {
          margin-left: 12px;
        }
      }

      &-content {
        transition: max-height 0.3s;
        max-height: 0px;
        overflow: hidden;
      }

      &-arrow {
        transform: rotate(180deg);
        transition: 0.3s;
      }

      &-open {
        &.collapse-layout-opening {
          .collapse-layout-content {
            overflow: hidden;
          }
        }

        .collapse-layout-arrow {
          transform: none;
        }

        .collapse-layout-content {
          max-height: 360px;
          height: auto;
          overflow: auto;
        }
      }
    }

    .async-print-task-item-actions > * + * {
      margin-left: 12px;
    }
  }

  .dropping {
    background: #fefefe;
    transition: all 0.3s;
  }

  .async-print-layout5-tooltip {
    height: 320px;
    width: 240px;

    .ant-tooltip-inner {
      max-height: none;
    }

    &-image {
      margin-top: 12px;
    }
  }
}


.ant-drawer.ant-drawer-open:not(.is-dragging) {
  // 保持与EUI的动画一致
  transition: width 0.3s;
}

.ant-drawer.ant-drawer-open {
  .ant-drawer-content-wrapper {
    min-width: initial;
  }
}

.ekb-custom-modal-with-height {
  .eui-modal-content {
    height: 100%;
  }

  .eui-modal-body {
    height: 100%;
  }

  .modal-footer {
    height: 64px;
  }
}
