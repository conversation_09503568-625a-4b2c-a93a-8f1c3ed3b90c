import React from 'react'
import { Button } from 'antd'
import styles from './TripPlanningItem.module.less'
import moment from 'moment'
import { getWeek } from '../../../../../../../lib/lib-util'
import { useEffect, useState } from 'react'
import { Resource } from '@ekuaibao/fetch'
import { Tooltip, Tag } from '@hose/eui'
import { getBoolVariation } from '../../../../../../../lib/featbit'
const cityFetch = new Resource('/api/v2/basedata/city/')
const cityIanaFetch = new Resource('/api/v2/basedata/city/iana/')
const TripPlanningItem = ({
  item,
  index,
  loading,
  handleClickOrder,
  handleCtripToOrder,
  formatTripButtonText,
  checkInDateTravelList
}) => {
  // 获取对象包含key中得某个字符串，得value
  const reconsitutionValue = (value, property) => {
    return value?.[Reflect?.ownKeys(value)?.find((e) => e?.indexOf(property) > 0)]
  }

  const [startCityEnhance, setStartCityEnhance] = useState([])
  const [endCityEnhance, setEndCityEnhance] = useState([])

  const [startCityStr, setStartCityStr] = useState([])
  const [endCityStr, setEndCityStr] = useState([])

  // 时区相关状态
  const [localUTC, setLocalUTC] = useState('')
  const [tripDateUTCs, setTripDateUTCs] = useState<string[]>([])
  const [allCityWithTimezone, setAllCityWithTimezone] = useState('')
  const [dontNeedShowUTC, setDontNeedShowUTC] = useState(false)

    

  const formatCity = cityArray => {
    if (!Array.isArray(cityArray)) return cityArray
    return cityArray.map(city => city.label || city.name || '').join(', ')
  }

  const formatCityOrigin = cityArray => {
    if (!Array.isArray(cityArray)) return cityArray

    if (cityArray.length === 1) {
      return cityArray[0].label || cityArray[0].name || ''
    }

    if (cityArray.length > 1) {
      const firstCity = cityArray[0].label || cityArray[0].name || ''
      return `${firstCity}(+${cityArray.length - 1})`
    }

    return ''
  }

  // 计算UTC偏移量的函数
  const getUtcOffset = (timestamp, timeZone) => {
    if (!timeZone) {
      return ''
    }
    try {
      const dtf = new Intl.DateTimeFormat('en-US', {
        timeZone,
        timeZoneName: 'shortOffset'
      })
      const parts = dtf.formatToParts(new Date(timestamp))
      const offsetPart = parts.find(p => p.type === 'timeZoneName')
      return offsetPart ? offsetPart.value.replace('GMT', 'UTC') : null
    } catch (error) {
      console.warn('该浏览器不支持设备时区详细信息获取，可能导致UTC显示异常，建议更换浏览器使用', error)
      return ''
    }
  }

  // 计算时区集合的函数
  const calculateTimeZoneOffsets = (cities, timestamp) => {
    if (!Array.isArray(cities) || cities.length === 0) return []

    const cityOffsets = cities.map(city => {
        if (city?.iana) {
          const utcOffset = getUtcOffset(timestamp, city.iana)
          return utcOffset
        }
        return null
      })
      .filter(Boolean)

    if (cityOffsets.length > 0 && cityOffsets.every(utc => utc.includes(localUTC))) {
      setDontNeedShowUTC(true)
      return null
    } else {
      return cityOffsets
    }
  }

  // 为城市增加时区后缀
  const generateCityWithTimezone = (cities, timestamp) => {
    if (!Array.isArray(cities) || cities.length === 0) {
      return ''
    }

    const cityLabels = cities.map(city => {
        let cityLabel = city?.label || city?.name || ''

        if (city?.iana && timestamp) {
          const utcOffset = getUtcOffset(timestamp, city.iana)

          if (utcOffset) {
            cityLabel = `${cityLabel}<${utcOffset}>`
          }
        }

        if(city.type === 'cityGroup' && !dontNeedShowUTC) {
          cityLabel = `${cityLabel}<UTC+8>`
        }

        return cityLabel
      })
    
    if (cityLabels.length > 0 && cityLabels.every(utc => utc.includes(localUTC))) {
      return null
    } else {
      return cityLabels.join('、')
    }
  }

  // 初始化本地UTC
  useEffect(() => {
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const localUTC = getUtcOffset(new Date().getTime(), timeZone)
    setLocalUTC(localUTC || 'UTC+8')
  }, [])

  useEffect(() => {
    if (!getBoolVariation('fkrd-5600_trip_UTC')) return
    const startCity = reconsitutionValue(item?.dataLinkForm, '出发地')
    const endCity = reconsitutionValue(item?.dataLinkForm, '目的地')
    const stayCity = reconsitutionValue(item?.dataLinkForm, '住宿地')

    if (startCity) {
      const fetchData = async () => {
        const { enhanceFromCityData, enhanceToCityData } = await fetchAndProcessCityDetails(startCity, endCity)

        const {
          enhanceFromCityData: ianaFromCityData,
          enhanceToCityData: ianaToCityData
        } = await fetchAndProcessCityIana(startCity, endCity)

        const mergeAllData = (cityData, ianaData) => {
          if (!Array.isArray(cityData) || !Array.isArray(ianaData)) {
            return cityData
          }

          return cityData.map(city => {
            const ianaCity = ianaData.find(iana => iana.key === city.key)
            return {
              ...city,
              ...(ianaCity?.iana && { iana: ianaCity.iana })
            }
          })
        }

        const finalFromCityData = mergeAllData(enhanceFromCityData, ianaFromCityData)
        const finalToCityData = mergeAllData(enhanceToCityData, ianaToCityData)

        setStartCityStr(formatCityOrigin(finalFromCityData))
        setEndCityStr(formatCityOrigin(finalToCityData))

        setStartCityEnhance(formatCity(finalFromCityData))
        setEndCityEnhance(formatCity(finalToCityData))

        const tripDate = getValue(item, 'DATE')
        if (tripDate) {
          const tripTimestamp = new Date(tripDate).getTime()

          // 计算所有城市的时区偏移量
          const allCities = finalFromCityData.concat(finalToCityData)
          const utcOffsets = calculateTimeZoneOffsets(finalFromCityData, tripTimestamp)
          let uniqueUTCs = Array.from(new Set(utcOffsets))
          if(finalFromCityData?.length > 0 && finalFromCityData?.[0]?.type === 'cityGroup' && localUTC !== 'UTC+8') {
            uniqueUTCs = ["UTC+8"]
          }
          setTripDateUTCs(uniqueUTCs)

          const allCityWithTimezone = generateCityWithTimezone(allCities, tripTimestamp)
          setAllCityWithTimezone(allCityWithTimezone)
        }
      }
      fetchData()
    } else if (stayCity) {
      const fetchData = async () => {
        const { enhanceFromCityData } = await fetchAndProcessCityDetails(stayCity, [])

        const { enhanceFromCityData: ianaFromCityData } = await fetchAndProcessCityIana(stayCity, [])

        const finalFromCityData = enhanceFromCityData.map(city => {
          const ianaCity = ianaFromCityData.find(iana => iana.key === city.key)
          return {
            ...city,
            ...(ianaCity?.iana && { iana: ianaCity.iana })
          }
        })

        setStartCityStr(formatCityOrigin(finalFromCityData))
        setStartCityEnhance(formatCity(finalFromCityData))

        const tripDate = getValue(item, 'DATE')
        if (tripDate) {
          const tripTimestamp = new Date(tripDate).getTime()

          const utcOffsets = calculateTimeZoneOffsets(finalFromCityData, tripTimestamp)
          const uniqueUTCs = Array.from(new Set(utcOffsets))
          setTripDateUTCs(uniqueUTCs)

          const allCityWithTimezone = generateCityWithTimezone(finalFromCityData, tripTimestamp)
          setAllCityWithTimezone(allCityWithTimezone)
        }
      }
      fetchData()
    }
  }, [item, localUTC])

  const fetchAndProcessCityDetails = async (travelFromCity, travelToCity) => {
    const fromCityData = travelFromCity?.length > 0 ? (travelFromCity && JSON.parse(travelFromCity)) || [] : []
    const toCityData = travelToCity?.length > 0 ? (travelToCity && JSON.parse(travelToCity)) || [] : []

    const foreignCityKeys = []
    if (fromCityData && fromCityData.length > 0) {
      for (const city of fromCityData) {
        foreignCityKeys.push(city.key)
      }
    }
    if (toCityData && toCityData.length > 0) {
      for (const city of toCityData) {
        foreignCityKeys.push(city.key)
      }
    }

    if (foreignCityKeys.length > 0) {
      try {
        const res = await cityFetch.GET('[ids]', { ids: foreignCityKeys.join(',') })

        if (res && res.items && Array.isArray(res.items)) {
          const processCityDetails = (cityData, cityDetails) => {
            if (!cityData || !cityDetails || !Array.isArray(cityData) || !Array.isArray(cityDetails)) {
              return cityData
            }

            const newCityData = cityData.map(city => ({ ...city }))

            cityDetails.forEach(cityDetail => {
              if (cityDetail.fullName && cityDetail.countryCode !== 'CN') {
                const countryName = cityDetail.fullName.split(',')[0]
                newCityData.forEach(city => {
                  if (city.key === cityDetail.id && !city.label.includes(`(${countryName})`)) {
                    city.label = `${city.label}(${countryName})`
                  }
                })
              }
            })

            return newCityData
          }

          const enhanceFromCityData = processCityDetails(fromCityData, res.items)
          const enhanceToCityData = processCityDetails(toCityData, res.items)

          return { enhanceFromCityData, enhanceToCityData }
        }
      } catch (error) {
        console.error('请求城市详情失败:', error)
      }
    }

    return { enhanceFromCityData: fromCityData, enhanceToCityData: toCityData }
  }

  // 获取IANA时区信息
  const fetchAndProcessCityIana = async (travelFromCity, travelToCity) => {
    const fromCityData = travelFromCity?.length > 0 ? (travelFromCity && JSON.parse(travelFromCity)) || [] : []
    const toCityData = travelToCity?.length > 0 ? (travelToCity && JSON.parse(travelToCity)) || [] : []

    // 收集所有城市的 ID
    const allCityKeys = []
    if (fromCityData && fromCityData.length > 0) {
      for (const city of fromCityData) {
        if (city?.key) {
          allCityKeys.push(city.key)
        }
      }
    }
    if (toCityData && toCityData.length > 0) {
      for (const city of toCityData) {
        if (city?.key) {
          allCityKeys.push(city.key)
        }
      }
    }

    if (allCityKeys.length > 0) {
      try {
        const res = await cityIanaFetch.POST('', { ids: allCityKeys })

        if (res && res.items && Array.isArray(res.items)) {
          const processCityIana = (cityData, ianaDetails) => {
            if (!cityData || !ianaDetails || !Array.isArray(cityData) || !Array.isArray(ianaDetails)) {
              return cityData
            }

            const newCityData = cityData.map(city => ({ ...city }))

            ianaDetails.forEach(ianaDetail => {
              if (ianaDetail.iana) {
                newCityData.forEach(city => {
                  if (city.key === ianaDetail.id) {
                    city.iana = ianaDetail.iana
                  }
                })
              }
            })

            return newCityData
          }

          const enhanceFromCityData = processCityIana(fromCityData, res.items)
          const enhanceToCityData = processCityIana(toCityData, res.items)

          return { enhanceFromCityData, enhanceToCityData }
        }
      } catch (error) {
        console.error('请求城市IANA信息失败:', error)
      }
    }

    return { enhanceFromCityData: fromCityData, enhanceToCityData: toCityData }
  }

  // 获取日期
  const formatTripDate = item => {
    const format = i18n.currentLocale === 'en-US' ? 'MM.DD' : 'MM月DD日'
    let date = getValue(item, 'DATE')
    const referenceStartTime = moment(reconsitutionValue(item?.dataLinkForm, '行程开始日期'))?.format('MM月DD日')
    if (getBoolVariation('fkrd-5600_trip_UTC')) {
      let dateStr = ''
      if(referenceStartTime) {
        dateStr = referenceStartTime + ' ' + getWeek(date)
      }else{
        dateStr = moment(date).format(format) + ' ' + getWeek(date)
      }
      if (tripDateUTCs?.length > 0) {
        const timezoneInfo = tripDateUTCs.length > 1 ? i18n.get('多时区') : tripDateUTCs[0]
        return (
          <>
            {dateStr}
            <Tag className="ml-4" size="small" color="neu">
              {timezoneInfo}
            </Tag>
          </>
        )
      }
      return dateStr
    } else {
      let dateStr = moment(getValue(item, 'DATE')).format(format) + ' ' + getWeek(date)
      return dateStr
    }
  }
  // 获取地址
  const formatTripAddress = item => getValue(item)

  /**
   * 获取不同类型旅程 对应字段值
   * @param value 每条数据item
   * @param matchType 匹配日期还是地址
   * @returns
   */
  const getValue = (item, matchType?: string) => {
    let result = ''
    const TYPE = item?.addTypeWithIcon?.type
    const value = item?.dataLinkForm
    const inCheckInDateTravelList = checkInDateTravelList.includes(TYPE)

    if (matchType === 'DATE') {
      if (inCheckInDateTravelList) {
        result = reconsitutionValue(value, '入住日期')
      } else {
        result = reconsitutionValue(value, '行程日期')
      }
    } else {
      if (getBoolVariation('fkrd-5582-travel-planning-show-foreign-country')) {
        return (
          <>
            <Tooltip title={allCityWithTimezone || startCityEnhance || ''}>
              <span className="city">{startCityStr}</span>
            </Tooltip>
            {endCityStr?.length > 0 && (
              <Tooltip title={allCityWithTimezone || endCityEnhance || ''}>
                <span className="city">&nbsp;-&nbsp;{endCityStr}</span>
              </Tooltip>
            )}
          </>
        )
      } else {
        result = reconsitutionValue(value, 'name')
      }
    }
    return result
  }
  // 获取对象包含key中得某个字符串，得value
  const reconsitutionValue = (value, property) =>
    value[Reflect.ownKeys(value)?.find((e: string) => e.indexOf(property) > 0)]

  // render 前往商城button
  const rendereButton = item => {
    const buttonText = item.hasOrder ? i18n.get('查看订单') : i18n.get(formatTripButtonText?.(item))
    const handleClick = item.hasOrder
      ? () => handleClickOrder(item.addTypeWithIcon.type)
      : () => handleCtripToOrder?.(item.addTypeWithIcon.type)
    return (
      <div className={`trip-item-button ${loading ? '' : 'ekb-skeleton-normal ekb-skeletons-text'}`}>
        {loading && (
          <Button type="primary" onClick={handleClick} {...({} as any)}>
            {buttonText}
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className={styles['trip-item-wrap']}>
      <div className={'trip-item'} key={index}>
        <div className={`trip-item-left`}>
          <div
            className={`trip-item-left-icon ${loading ? '' : 'ekb-skeleton-normal'}`}
            style={{ backgroundColor: item.addTypeWithIcon.color }}
          >
            {loading && <img src={item.addTypeWithIcon.icon} />}
          </div>
          <div className={`trip-item-left-head`}>
            <span className={`trip-item-left-head-title ${loading ? '' : 'ekb-skeleton-normal ekb-skeletons-text'}`}>
              {formatTripAddress?.(item)}
            </span>
            <span className={`trip-item-left-head-des ${loading ? '' : 'ekb-skeleton-normal ekb-skeletons-text'}`}>
              {formatTripDate?.(item)}
            </span>
          </div>
        </div>
        {rendereButton(item)}
      </div>
    </div>
  )
}

export default TripPlanningItem
