import React, { PureComponent } from 'react'
import { Icon, Collapse, Input } from 'antd'
import classNames from 'classnames'
import { debounce } from 'lodash'
import Highlighter from 'react-highlight-words'
import { isOpenTemplateModal } from '../bills.action'
import { app as api } from '@ekuaibao/whispered'
import { session } from '@ekuaibao/session-info'
import { Fetch } from '@ekuaibao/fetch'
const { Panel } = Collapse
const { Search } = Input
import { FilledTipsClose, IllustrationSmallNoSearch, OutlinedEditDeleteTrash, OutlinedTipsClose } from '@hose/eui-icons'
import { fnFilterSpecification, getSpecificationName } from '../util/billUtils'
import { Tag } from '@hose/eui'
import { OverflowText } from './OverflowText'
import { SpecificationGroup } from '@ekuaibao/ekuaibao_types'
import { SkeletonModal } from './SkeletonModal'
import AIChatEntry from '../../../components/AIChatEntry'
const styles = require('./NewChangeTemplateModal.module.less')
const { EnhanceModal } = require('@ekuaibao/enhance-layer-manager')
const getSpecificationIconByName = api.require<any>('@elements/specificationIcon')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

export interface Props {
  layer: any
  specificationGroups: any[]
  currentSpecification?: any
  overrideGetResult: any
  reloadSpecification?: boolean // 是否需要重新加载单据模版
}

export interface State {
  searchedSpecificationGroups: SpecificationGroup[]
  searchKey: string
  activeKeys: string[]
  searchHistory: string[]
  isLoading: boolean
  emptyTipMessage: string
  dataSource: SpecificationGroup[],
  newColors: {}
}

@EnhanceModal({
  footer: [],
  className: `respond-modal-layer ${styles['respond-modal-layer']}`
})
export default class NewChangeTemplateModal extends PureComponent<Props, State> {
  result: any
  constructor(props: Props) {
    super(props)
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult(this.getResult)
    const { reloadSpecification, specificationGroups = [] } = props
    this.result = props.currentSpecification
    let searchedSpecificationGroups = []
    let activeKeys = []
    if (!reloadSpecification) {
      searchedSpecificationGroups = this.fnFilterSpecification(specificationGroups)
      activeKeys = this.getSpecificationGroupIds(specificationGroups)
    }
    //UI老师替换了原来的颜色，但是无法修改后端传过来的颜色，所以做了映射
    const colors = {
      '#f9a825': '#CB272D',
      '#ff6f00': '#F53F3F',
      '#ef9a9a': '#F76560',
      '#ef5350': '#D25F00',
      '#e53935': '#FF7D00',
      '#c62828': '#FF9A2E',
      '#f48fb1': '#CFAF0F',
      '#ec407a': '#FADC19',
      '#d81b60': '#FBE842',
      '#ad1457': '#008026',
      '#b39ddb': '#009A29',
      '#7e57c2': '#00B42A',
      '#5e35b1': '#23C343',
      '#4527a0': '#206CCF',
      '#90caf9': '#3491FA',
      '#42a5f5': '#57A9FB',
      '#1e88e5': '#1739D2',
      '#1565c0': '#2555FF',
      '#80deea': '#4C79FF',
      '#26c6da': '#739BFF',
      '#00acc1': '#07828B',
      '#00838f': '#0DA5AA',
      '#a5d6a7': '#14C9C9',
      '#66bb6a': '#37D4CF',
      '#43a047': '#551DB0',
      '#2e7d32': '#722ED1',
      '#dce775': '#8D4EDA',
      '#cddc39': '#A871E3',
      '#afb42b': '#CB1E83',
      '#827717': '#F5319D',
      '#bcaaa4': '#F754A8',
      '#8d6e63': '#202A56',
      '#6d4c41': '#2C3659',
      '#4e342e': '#47537A',
      '#b0bec5': '#12952D',
      '#78909c': '#22BA3C',
      '#546e7a': '#37DE4C',
      '#37474f': '#57E565',
    }

    this.state = {
      searchedSpecificationGroups,
      searchKey: '',
      activeKeys,
      searchHistory: [],
      isLoading: false,
      emptyTipMessage: i18n.get('没有找到相关内容'),
      dataSource: specificationGroups,
      newColors: colors
    }
  }

  componentDidMount() {
    const { reloadSpecification } = this.props
    if (reloadSpecification) {
      this.setState({ isLoading: true })
      api
        .invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned', undefined, {
          hiddenLoading: true
        })
        .then(({ items }) => {
          const hasSpecificationRange = this.fnCheckSpecificationRange(items)
          if (!hasSpecificationRange) {
            this.setState({
              emptyTipMessage: i18n.get('没有可用的单据模板，请联系系统管理员'),
              searchedSpecificationGroups: [],
              activeKeys: [],
              isLoading: false,
              dataSource: []
            })
            return
          }
          const searchedSpecificationGroups = this.fnFilterSpecification(items)
          const activeKeys = this.getSpecificationGroupIds(items)
          this.setState({
            searchedSpecificationGroups,
            activeKeys,
            isLoading: false,
            dataSource: items
          })
        })
        .catch(e => {
          console.log(e)
          this.setState({ isLoading: false })
        })
    }
    this.getLocalSearchHistory()
  }

  componentWillUnmount() {
    api.dispatch(isOpenTemplateModal({ isOpen: false }))
  }

  fnGetDataSource = () => {
    const { reloadSpecification, specificationGroups } = this.props
    const { dataSource } = this.state
    return reloadSpecification ? dataSource : specificationGroups
  }

  fnFilterSpecification = (specificationGroups: SpecificationGroup[] = []) => {
    const filterSpecificationGroup = specificationGroups
      .filter(group => {
        group.specifications = group.specifications.filter(v => {
          const isActive = v => v.type !== 'settlement' && v.type !== 'reconciliation'
          if (!this.result || !this.result?.id?.includes('system:对账单')) {
            return isActive && !v.id.includes('system:对账单')
          }
          return isActive
        })
        .filter(v => {
          const NotAllowCreateApplication = api.getState()['@common'].powers?.powersList?.find(power => power.powerCode === '110415')?.state === 'using'
          if (!NotAllowCreateApplication ) return true
          return v.type !== 'requisition'
        })
        return group
      })
      .filter(v => v.specifications.length)
    return [...fnFilterSpecification(filterSpecificationGroup)]
  }

  fnCheckSpecificationRange = (specificationGroups: SpecificationGroup[] = []) => {
    let hasTemplates = false
    if (specificationGroups.length) {
      for (const item of specificationGroups) {
        if (item.specifications.length > 0) {
          hasTemplates = true
          break
        }
      }
    }
    return hasTemplates
  }

  getSpecificationGroupIds = (specificationGroups: SpecificationGroup[] = []) => {
    const groupIds: string[] = []
    specificationGroups.forEach(group => {
      groupIds.push(group.id)
    })
    return groupIds
  }

  getResult() {
    return this.result
  }

  handleModalClose() {
    this.props.layer.emitCancel()
  }

  handleSelect = (item: any) => {
    this.result = item
    startOpenFlowPerformanceStatistics()
    this.props.layer.emitOk()
  }

  handleSearch = (value: string) => {
    if (!value.trim()) return
    const { reloadSpecification, specificationGroups = [] } = this.props
    const { dataSource = [] } = this.state
    const searchOrgData = reloadSpecification ? dataSource : specificationGroups
    const searchedSpecificationGroups = fnFilterSpecification(searchOrgData)
    // 搜索到的结果
    const searchedRes = [] as any
    // 精确匹配到的模版 id
    const searchedSpecificationIds: string[] = []
    // 过滤出需要展示的模版分组
    const filterGroup: string[] = []
    searchedSpecificationGroups.forEach(group => {
      if (group.name.includes(value)) {
        searchedRes.push(group)
        filterGroup.push(group.id)
      } else {
        group.specifications.forEach((item: any) => {
          const name = getSpecificationName(item)
          if (name.includes(value)) {
            filterGroup.push(group.id)
            searchedSpecificationIds.push(item.id)
          }
        })
      }
    })
    if (searchedSpecificationIds.length) {
      const specificationGroupsList = searchOrgData
      const notSearchedResList = specificationGroupsList.filter(
        item => !searchedRes.some((searchedResItem: any) => searchedResItem === item)
      )
      const SearchedResList = notSearchedResList.map((group: any) => {
        return {
          ...group,
          specifications: group.specifications.filter((item: any) => {
            const name = getSpecificationName(item)
            return name.includes(value)
          })
        }
      })
      this.setState({
        searchedSpecificationGroups: [...SearchedResList, ...searchedRes],
        activeKeys: filterGroup
      })
    } else {
      this.setState({ searchedSpecificationGroups: searchedRes, activeKeys: filterGroup })
    }
    this.onSearch(value)
  }

  handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchKey = e.currentTarget.value
    this.setState({ searchKey })
    if (searchKey) {
      debounce((searchKey: string) => this.handleSearch(searchKey), 300)
    } else {
      this.setState({
        searchedSpecificationGroups: [...this.fnGetDataSource()],
        activeKeys: this.getSpecificationGroupIds(this.fnGetDataSource())
      })
    }
  }

  handleClear = () => {
    this.setState({
      searchKey: '',
      searchedSpecificationGroups: [...this.fnGetDataSource()],
      activeKeys: this.getSpecificationGroupIds(this.fnGetDataSource())
    })
  }

  renderHighLighter = (name: string) => (
    <Highlighter
      highlightStyle={{ color: 'var(--brand-base)', background: 'none', padding: '2.8px 0' }}
      searchWords={[this.state.searchKey]}
      textToHighlight={name}
      autoEscape={true}
    />
  )

  switchPanel = (key: string[]) => {
    this.setState({ activeKeys: key })
  }

  handleSearchHistoryItemCLick = (value: string): void => {
    this.setState({ searchKey: value })
    this.handleSearch(value)
  }

  getLocalSearchHistory = (): void => {
    const dataSource = window.isWx ? session.get(Fetch.corpId) : localStorage.getItem(Fetch.corpId)
    this.setState({
      searchHistory: dataSource ? JSON.parse(dataSource) : []
    })
  }

  onSearch = (value: string): void => {
    const { searchHistory } = this.state
    const isExist = searchHistory.includes(value)
    if (!isExist) {
      if (searchHistory.length >= 10) {
        searchHistory.pop()
      }
      this.setState({
        searchHistory: [value, ...searchHistory]
      })
      this.setLocalSearchHistory([value, ...searchHistory])
    }
  }

  hanleClearSearchHistory = () => {
    this.setState({ searchHistory: [] })
    this.setLocalSearchHistory([])
  }

  setLocalSearchHistory = (arr: string[]): void => {
    if (window.isWx) {
      session.set(Fetch.corpId, JSON.stringify(arr))
    } else {
      localStorage.setItem(Fetch.corpId, JSON.stringify(arr))
    }
  }

  renderGroup(item: any, index: number) {
    const { specifications = [] } = item
    const { currentSpecification } = this.props
    const { newColors } = this.state

    if (specifications.length === 0) {
      return null
    }
    const groupName = i18n.currentLocale === 'en-US' && item.enName ? item.enName : item.name

    return (
      <div key={index} className="collapse-wrapper">
        <Collapse defaultActiveKey={[...item.id]} activeKey={this.state.activeKeys} onChange={this.switchPanel}>
          <Panel header={this.renderHighLighter(groupName)} key={item.id}>
            {specifications.map((o: any, index: number) => {
              const IconCompo = getSpecificationIconByName(o.icon)
              const name = getSpecificationName(o)
              let color = newColors[o.color] || o.color
              return (
                <div
                  className={`children-parent ${
                    currentSpecification && o.id === currentSpecification.id ? 'children-parent-active' : ''
                  }`}
                  data-testid="bill-template-item"
                  key={index}
                  onClick={() => {
                    this.handleSelect(o)
                  }}
                >
                  <div className="icon-background" style={{ backgroundColor: color }}>
                    <IconCompo className="icon icon-my" style={{ color: color }} fontSize={16} />
                  </div>

                  <div className="children">
                    {this.state.searchKey ? (
                      <Highlighter
                        highlightStyle={{ color: 'var(--brand-base)', background: 'none', padding: '2.8px 0' }}
                        searchWords={[this.state.searchKey]}
                        textToHighlight={name}
                        autoEscape={true}
                      />
                    ) : (
                      <OverflowText text={name} />
                    )}
                  </div>
                </div>
              )
            })}
          </Panel>
        </Collapse>
      </div>
    )
  }

  renderContent = () => {
    const { currentSpecification } = this.props
    const { searchedSpecificationGroups = [], searchKey, searchHistory, isLoading, emptyTipMessage } = this.state
    let hasTemp = false
    if (isLoading) {
      return <SkeletonModal />
    }
    if (!searchedSpecificationGroups.length && !searchKey) {
      return (
        <div className="modal-content-new">
          <div className="empty">
            <IllustrationSmallNoSearch className="empty-icon"></IllustrationSmallNoSearch>
            <div className="empty-tips">{emptyTipMessage}</div>
          </div>
        </div>
      )
    }
    return (
      <div className="modal-content-new">
        {!currentSpecification && searchHistory?.length > 0 && (
          <div className={`search-history ${!searchedSpecificationGroups.length ? '' : 'show-line'}`}>
            <div className="search-history__title">
              <div>{i18n.get('历史搜索')}</div>
              <OutlinedEditDeleteTrash onClick={this.hanleClearSearchHistory}></OutlinedEditDeleteTrash>
            </div>
            <div className="search-history__content">
              {searchHistory.map(item => (
                <Tag className="search-history-item" key={item} onClick={() => this.handleSearchHistoryItemCLick(item)}>
                  {item}
                </Tag>
              ))}
            </div>
          </div>
        )}
        {searchedSpecificationGroups.map((item: any, index: number) => {
          const view = this.renderGroup(item, index)
          if (view && !hasTemp) {
            hasTemp = true
          }
          return view
        })}
        {!hasTemp && (
          <div className="empty">
            <IllustrationSmallNoSearch></IllustrationSmallNoSearch>
            <div className="empty-tips">{emptyTipMessage}</div>
          </div>
        )}
      </div>
    )
  }

  render() {
    const { searchKey } = this.state
    return (
      <div id={'changeTemplateModal'} className={styles['new-change-template-modal-style']}>
        <div className="modal-header-new">
          <div className="title flex-1">{i18n.get('选择单据')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className="search-template-wrapper">
          <Search
            placeholder={i18n.get('搜索')}
            value={searchKey}
            onSearch={this.handleSearch}
            onChange={this.handleChange}
            data-testid="bill-template-search"
          />
          <div className={classNames('clear', { hidden: !searchKey })} onClick={this.handleClear}>
            <FilledTipsClose></FilledTipsClose>
          </div>
        </div>
        <AIChatEntry hasRight={true} style={{margin: '0 8px 12px'}}/>
        {this.renderContent()}
      </div>
    )
  }
}
