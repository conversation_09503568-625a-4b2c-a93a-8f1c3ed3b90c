/**
 * <AUTHOR> <<EMAIL>>
 * @date 2018-02-07
 * @description 行程编辑卡片
 */

import styles from './Trips.module.less'
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { Col, Row, Icon, Select, Form } from 'antd'
import { Dynamic } from '@ekuaibao/template'
import { editable } from '../../../../components'
import Loading from '@ekuaibao/loading'
import { parseAsMeta } from '../../util/parse'
import { getAutoCalculationResult } from '../../bills.action'
import { app as api } from '@ekuaibao/whispered'
import { getValidRiskWarningData } from '../../riskWarning/formatRiskWarningData'
import { getFormFieldi18n } from '@ekuaibao/lib/lib/lib-util'
import { needUpdateResult } from '../../util/parse'
import { debounce, cloneDeep, isEqual } from 'lodash'
import { updateAutoCalResult } from '../../util/autoCalculate'
import { getV } from '@ekuaibao/lib/lib/help'
import { getBoolVariation } from '../../../../lib/featbit'

function create(T) {
  return Form.create({
    onValuesChange(props, changedValues) {
      setTimeout(() => {
        props.bus.emit('dynamic:value:changed', changedValues)
      }, 0)
    }
  })(T)
}
@EnhanceConnect(
  state => {
    return {
      baseDataProperties: state['@common'].globalFields.data,
      lastChoice: state['@common'].lastChoice.choiceValue,
      baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap
    }
  },
  { getAutoCalculationResult }
)
export default class TripsEditCard extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      value: {},
      template: undefined,
      templates: [],
      externalData: {}, //分配到编辑消费详情页面中的超标提醒数据,
      autoCalFields: {}
    }
  }

  componentWillMount() {
    let { currentTrip } = this.props
    currentTrip.bus.watch('element:ref:select:staff', this.fnSelectStaff)
    currentTrip.bus.watch('element:ref:select:payee', this.fnSelectPayee)
    currentTrip.bus.watch('element:ref:select:staffs', this.fnSelectStaffs)
    currentTrip.bus.watch('element:select:dataLink', this.handleSelectDataLink)
    currentTrip.bus.on('dynamic:value:changed', this.handleDynamicValueChange)
    currentTrip.bus.on('update:calculate:template', this.updateCalTemplate)
    this.getCalculateField()
    this.updateAutoCalResult()
  }

  componentWillUnmount() {
    const { currentTrip } = this.props
    currentTrip.bus.un('element:ref:select:staff', this.fnSelectStaff)
    currentTrip.bus.un('element:ref:select:payee', this.fnSelectPayee)
    currentTrip.bus.un('element:ref:select:staffs', this.fnSelectStaffs)
    currentTrip.bus.un('element:select:dataLink', this.handleSelectDataLink)
    currentTrip.bus.un('update:calculate:template', this.updateCalTemplate)
  }

  componentDidMount() {
    let { currentTrip, templates, external } = this.props
    const template = this.getTemplate(this.props)
    let newTemplates = cloneDeep(templates)
    if (currentTrip.tripTypeId) {
      let c = currentTrip.tripTypeId
      if (!newTemplates.find(t => t.id === c.id)) {
        if (!c.label) {
          c.label = c.active ? c.name : c.name + i18n.get('（已停用）')
        }
        newTemplates.unshift(c)
      }
    }
    const { bus } = currentTrip
    this.setState({ template, templates: newTemplates, externalData: external }, () => {
      external && bus.setFieldsExternalsData && bus.setFieldsExternalsData({ ...external })
    })
  }

  componentWillReceiveProps(np) {
    if (this.props.currentTrip !== np.currentTrip) {
      const template = this.getTemplate(np)
      this.setState({ template })
    }
    if (this.props.external !== np.external) {
      const external = np.external || {}
      this.props.currentTrip.bus.setFieldsExternalsData({ ...external })
    }
  }

  getTemplate(props) {
    const { baseDataProperties, isLockFeeTypeVersion = false, onlyTrips = [], currentTrip } = props
    const tripTypeId = getV(currentTrip, 'tripTypeId', {})
    const targetOldDetail =
      onlyTrips.find(trip => trip.specificationId.originalId === tripTypeId.specificationId.originalId) || {}
    const oldSpecification = getV(targetOldDetail, 'specificationId', {})
    const specificationId = getV(currentTrip, 'specificationId', oldSpecification)
    const tripForm = getV(targetOldDetail, 'tripForm', null)
    const originalId = oldSpecification.originalId
    //比对保存过的行程类型和新选的是否一个,同一个的话用旧版本
    if (tripForm && isLockFeeTypeVersion && originalId === specificationId.originalId) {
      return parseAsMeta(oldSpecification, baseDataProperties)
    } else {
      const { specificationId } = tripTypeId
      return parseAsMeta(specificationId, baseDataProperties)
    }
  }

  formatValue = (value, baseDataProperties) => {
    let values = cloneDeep(value)
    Object.keys(values).forEach(item => {
      let baseItem = baseDataProperties.find(oo => oo.name === item)
      if (baseItem && baseItem.dataType && baseItem.dataType.type === 'ref') {
        values[item] = values[item] && values[item].id ? values[item].id : values[item]
      }
    })
    return values
  }

  handleSelectDataLink = entityInfo => {
    let { flowId, currentTrip, bus, baseDataProperties } = this.props
    return bus.getValue().then(value => {
      let formValue = this.formatValue(value, baseDataProperties)
      let saveValue = {
        form: { ...formValue, tripTypeId: currentTrip.tripTypeId && currentTrip.tripTypeId.id }
      }
      return api.open('@bills:SelectDataLinkModal', {
        entityInfo,
        flowId,
        isDetail: true,
        formValue: saveValue
      })
    })
  }

  updateCalTemplate = value => {
    const { bus } = this.props
    const { template } = this.state
    const clearValidFields = []
    const fields = Object.keys(value)
    fields.forEach(field => {
      let index = template.findIndex(el => el.field === field)
      if (index !== -1) {
        template[index] = { ...template[index], ...value[field] }
        if (value[field]['optional']) {
          clearValidFields.push(field)
        }
      }
    })
    this.setState({ template: [...template] }, () => {
      if (clearValidFields && clearValidFields.length) {
        bus.getValueWithValidate(clearValidFields)
      }
    })
  }

  fnSelectStaff(data) {
    const isNewContactList = window?.PLATFORMINFO?.isNewContactList
    return api.open('@layout:SelectMemberModal', { data, isVisibilityStaffs: true, isNewContactList })
  }

  fnSelectPayee(selectedPayee) {
    return api.open(
      getBoolVariation('new_version_of_payment_account', false) ? '@bills:SelectPayeePopup' : '@bills:SelectPayeeModal',
      { data: selectedPayee }
    )
  }

  fnSelectStaffs = data => {
    const { allowExternalStaff = false } = data
    const isNewContactList = window?.PLATFORMINFO?.isNewContactList
    if (allowExternalStaff) {
      return api
        .open('@layout:SelectStaffsModal', { ...data, isVisibilityStaffs: true, isNewContactList })
        .then(data => {
          return data
        })
    } else {
      return api.open('@layout:SelectMemberModal', { data, isVisibilityStaffs: true, isNewContactList }).then(data => {
        return data
      })
    }
  }

  handleTripTypeChange = value => {
    this.getCalculateField(value)
    const {
      baseDataProperties,
      index,
      onUpdateTrip,
      currentTrip,
      resetFieldsExternalsData,
      onlyTrips = [],
      isLockFeeTypeVersion = false
    } = this.props
    const { templates, externalData } = this.state
    const type = templates.find(t => t.specificationId.id === value)
    const newSpecification = getV(type, 'specificationId', {})
    let template = parseAsMeta(newSpecification, baseDataProperties)
    //找保存过的记录中有没有相同类型模板的行程类型
    const target =
      onlyTrips.find(trip => trip.specificationId && trip.specificationId.originalId === newSpecification.originalId) ||
      {}
    const oldSpecification = getV(target, 'specificationId', {})
    const feeTypeForm = getV(target, 'feeTypeForm', {})
    //比对保存过的行程类型和新选的是否一个,同一个的话用旧版本
    if (
      feeTypeForm &&
      isLockFeeTypeVersion &&
      oldSpecification.originalId === newSpecification.originalId &&
      !isEqual(oldSpecification, newSpecification)
    ) {
      currentTrip.specificationId = oldSpecification
      template = parseAsMeta(oldSpecification, baseDataProperties)
    }
    const ValidExternal = getValidRiskWarningData(template, externalData) || {}
    const toFather = []
    const externalCopy = cloneDeep(externalData) || {}
    Object.keys(ValidExternal).forEach(v => {
      delete externalCopy[v]
    })
    Object.values(externalCopy).forEach(v => toFather.push(v[0]))
    toFather.forEach(v => resetFieldsExternalsData(v))
    onUpdateTrip(index, type).then(_ => {
      this.setState({ template, externalData: ValidExternal }, () => {
        currentTrip.bus.setFieldsExternalsData && currentTrip.bus.setFieldsExternalsData({ ...ValidExternal })
      })
    })
    this.updateAutoCalResult(undefined, undefined, true)
  }

  renderTripType = () => {
    const { currentTrip } = this.props
    let { templates } = this.state
    const Options = templates.map(t => (
      <Select.Option
        label={t.label || t.name}
        disabled={!t.active}
        value={t.specificationId.id}
        key={t.specificationId.id}
        className={styles['options-trip']}
      >
        <div className="icon" style={{ backgroundColor: t.color }}>
          <img src={t.icon} alt="" />
        </div>
        {t.label || t.name}
        {t.description && <span className="description">{`(${t.description})`}</span>}
      </Select.Option>
    ))
    return (
      <Row className="trip-type">
        <Col span={24} className="label">
          {i18n.get('出行方式：')}
        </Col>
        <Col span={24}>
          <Select
            showSearch
            style={{ width: '100%' }}
            optionFilterProp="label"
            value={currentTrip.tripTypeId.specificationId.id}
            onChange={this.handleTripTypeChange}
          >
            {Options}
          </Select>
        </Col>
      </Row>
    )
  }

  render() {
    const { template } = this.state
    const {
      flowId,
      tripId,
      index,
      onDeleteTrip,
      currentTrip,
      submitterId,
      total,
      lastChoice,
      shouldUpdate,
      validateError,
      canEdit
    } = this.props
    const { tripTypeId } = currentTrip
    if (!template) {
      return null
    }
    getFormFieldi18n(template)
    const elements = editable
    const count = index + 1
    return (
      <div className={styles['trip-card-warp']}>
        <header>
          {canEdit ? <h4 className="title">{i18n.get('第{__k0}程', { __k0: count })}</h4> : null}
          {total !== 1 && (
            <Icon
              className="delete-btn"
              type="delete"
              onClick={() => {
                onDeleteTrip(index)
              }}
            />
          )}
        </header>
        <section>
          {this.renderTripType()}
          <Dynamic
            bus={currentTrip.bus}
            create={create}
            layout="vertical"
            loading={Loading}
            elements={elements}
            template={template}
            tripTypeId={tripTypeId}
            onValueChange={this.handleDynamicValueChange}
            value={currentTrip.tripForm}
            submitterId={submitterId}
            lastChoice={lastChoice}
            refId="tripCardWarp"
            tripId={tripId}
            flowId={flowId}
            isTrip={true}
            shouldUpdate={shouldUpdate}
            validateError={validateError}
          />
        </section>
      </div>
    )
  }

  getCalculateField = async specificationId => {
    let { tripTypeId } = this.props.currentTrip
    let autoCalFields = await api
      .invokeService(
        '@bills:get:calculaterfield',
        specificationId ? specificationId : tripTypeId.specificationId.id,
        this.props.submitterId.id
      )
      .then(res => {
        return res.items[0]
      })

    this.updateAutoCalResult(undefined, undefined, true)
    this.setState({
      autoCalFields
    })
  }
  handleDynamicValueChange = changeValue => {
    let { autoCalFields } = this.state
    for (let key in changeValue) {
      if (needUpdateResult(key, autoCalFields)) {
        this.updateAutoCalResult(changeValue)
      }
    }
  }
  updateAutoCalResult = debounce(async (changeValue, isTextChange, checkDefaultValue) => {
    const {
      currentTrip,
      billSpecification,
      baseDataProperties,
      billData,
      baseDataPropertiesMap,
      flowId,
      submitterId,
      changeLoading
    } = this.props
    const template = this.state.template ? this.state.template : this.getTemplate(this.props)
    const formValue = await currentTrip.bus.getValue().then(res => {
      return {
        specificationId: currentTrip.tripTypeId.specificationId,
        tripForm: res,
        tripTypeId: currentTrip.tripTypeId
      }
    })
    billData.trips = [formValue]
    if (flowId) {
      billData.flowId = flowId
    }
    changeLoading(true)
    await updateAutoCalResult(
      'trip_',
      billData,
      formValue,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      currentTrip.bus,
      submitterId.id,
      changeValue,
      isTextChange,
      checkDefaultValue,
      template
    )
    changeLoading(false)
  }, 400)
}
