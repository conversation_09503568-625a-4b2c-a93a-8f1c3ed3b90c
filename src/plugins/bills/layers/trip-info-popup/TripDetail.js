import React, { Component } from 'react'
import { <PERSON><PERSON>, Popover } from 'antd'
import moment from 'moment'
import style from './TripDetail.module.less'
import TripCard from '../../../../components/dynamic/dataLinkEdit/TripCard'
import { app as api } from '@ekuaibao/whispered'
import { getStrLastWord } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { uniq, get, forIn, size } from 'lodash'
import { billType, stateMap } from '../../../../lib/enums'
import Money from '../../../../elements/puppet/Money'
import WARINGIMAGE from '../../../../images/warning.svg'
import {getFlowId} from '../../util/intent'
const DATEFORMAT = 'YYYY-MM-DD'

export default class TripDetail extends Component {
  constructor(props) {
    super(props)
    this.state = {
      disabled: false,
      showButton: false,
      tripDataLink: undefined,
      deptList: [],
      tripsTemplate: [],
      referenceStartTime: '',
      referenceEndTime: ''
    }
  }
  getTripData = dataLinkForm => {
    if (!dataLinkForm) return {}
    const { tripType } = this.props
    const startCity =
      dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === '出发地')] || // @i18n-ignore
      dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === '住宿地')] // @i18n-ignore
    const endCity = dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === '目的地')] // @i18n-ignore
    const startTime =
      dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === '行程日期')] || // @i18n-ignore
      dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === '入住日期')] // @i18n-ignore
    const endTime = dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === '离店日期')] // @i18n-ignore
    const name = dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === 'name')]
    const traveler = dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === '出行人')] || [] // @i18n-ignore
    const bill = dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === '原始单据')] // @i18n-ignore
    let orders = dataLinkForm[Object.keys(dataLinkForm).find(item => getStrLastWord(item, '_') === '订单')] || [] // @i18n-ignore
    orders = this.getOrders(orders)
    return { startCity, endCity, startTime, endTime, name, traveler, bill, orders, tripType }
  }
  getOrders = data => {
    const orders = {}
    data.forEach(i => {
      if (!i) return // 兼容[null]
      let dataLink = get(i, 'data.dataLink')
      dataLink = this.formatDataLink(dataLink)
      const traveler = dataLink['出行人'] || [] // @i18n-ignore
      traveler.forEach(traveler => {
        if (!orders[traveler.id]) {
          orders[traveler.id] = []
        }
        orders[traveler.id].push(i)
      })
    })
    return orders
  }
  componentDidMount() {
    const {
      entityInfo: { dataLink }
    } = this.props
    // 获取对象包含key中得某个字符串，得value
    const reconsitutionValue = (value, property) => {
      return value?.[Reflect?.ownKeys(value)?.find((e) => e?.indexOf(property) > 0)]
    }
    const referenceStartTime = moment(reconsitutionValue(dataLink, '行程开始日期'))?.format('MM月DD日')
    const referenceEndTime = moment(reconsitutionValue(dataLink, '行程结束日期'))?.format('MM月DD日')
    this.setState({ referenceStartTime, referenceEndTime })
    const tripData = this.getTripData(dataLink)
    this.getTripsTemplate()
    const { traveler, bill, tripType = '' } = tripData
    this.TK = `BUTTON_${tripType}_ORDER`
    const deptIds = traveler && traveler.map(item => item.defaultDepartment)
    if (deptIds && deptIds.length > 0) {
      api.invokeService('@common:get:dept:by:id', uniq(deptIds)).then(result => {
        this.setState({ tripDataLink: tripData, deptList: result.items })
      })
    } else {
      this.setState({ tripDataLink: tripData, deptList: [] })
    }
    if (bill) {
      const specificationId = get(bill, 'form.specificationId')
      api.invokeService('@custom-triptype:get:template:by:id', specificationId).then(result => {
        this.setState({ specificationId: result.value })
      })
    }
    const applyId = bill?.id
    const tripId = dataLink.id
    const tripQuery = tripId && applyId ? `&applyId=${applyId}&tripId=${tripId}` : ''
    //机票下单：BUTTON_FLIGHT_ORDER 火车下单：BUTTON_TRAIN_ORDER  酒店下单：BUTTON_HOTEL_ORDER
    const white = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI', 'FOOD']
    if (!white.includes(tripType)) {
      return
    }
    Promise.all([
      api.invokeService('@custom-triptype:get:travel:intent', { type: this.TK }),
      api.invokeService('@custom-triptype:get:travel:intent:jwt', { type: this.TK })
    ]).then(async result => {
      let items = result[0] && result[0].items ? result[0].items : []
      let token = result[1] ? result[1].id : ''
      await getFlowId(dataLink, items)

      items.forEach(i => {
        let type = /\?/.test(i.source) ? '&' : '?'
        if (!i.source.includes('token')) {
          i.source = i.source + `${type}token=${token}${tripQuery}`
        }
      })
      this.setState({
        showButton: items.length > 0,
        disabled: !dataLink.active
      })
      api.thirdResources.deleteByType(this.TK)
      api.thirdResources.add(items)
    })
  }
  getTripsTemplate = () => {
    return api.invokeService('@bills:get:getTripsTemplate').then(res => {
      const tripsTemplate = res.items
      this.setState({
        tripsTemplate
      })
    })
  }

  handleOrderDetail = order => {
    api.open('@bills:TripOrderPopup', {
      title: i18n.get('订单详情'),
      entityInfo: order.data,
      tripType: this.props.tripType
    })
  }

  handleBillDetail = bill => {
    api.invokeService('@bills:get:flow-info', { id: bill.id }).then(result => {
      api.open('@bills:BillStackerModal', {
        viewKey: 'BillInfoView',
        dataSource: result.value
      })
    })
  }

  handleOnClick = async () => {
    const services = {
      token: (...args) => {
        console.log('token', args)
        return new Promise((resolve, reject) => {
          setTimeout(() => resolve({ RD: { c: 3 } }), 1000)
        })
      }
    }
    const result = await api.request({
      type: this.TK || 'BUTTON_TRAVEL_ORDER',
      services: services
    })
    console.log('result:@@demo:001', result)
  }
  getDeptName = item => {
    const { defaultDepartment } = item
    const { deptList } = this.state
    const dept = deptList.find(item => item.id === defaultDepartment)
    return dept ? dept.fullName : i18n.get('无')
  }
  getButtonText = typeName => {
    const { showButton } = this.state
    if (!showButton) {
      return ''
    }
    switch (typeName) {
      case 'FLIGHT':
        return i18n.get('订机票')
      case 'HOTEL':
        return i18n.get('订酒店')
      case 'TRAIN':
        return i18n.get('订火车票')
      case 'TAXI':
        return i18n.get('用车')
      // case 'FOOD':
      //   return i18n.get('餐')
      default:
        return ''
    }
  }
  renderContent = data => {
    const value = data.replace('[未同步]', '').replace('餐补标准:', '') // @i18n-ignore
    const allList = value.split(',')
    const list = allList.filter(item => item.indexOf('差标未维护') > -1) // @i18n-ignore
    const other = allList.find(item => item.indexOf('接口调用失败') > -1) // @i18n-ignore
    return (
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {list.length > 0 && <h3>{`当前行程有${list.length}个员工差标未维护`}</h3>}
        {list.length > 0 &&
          list.map((item, index) => {
            return <span>{`${index + 1}、${item.replace(':差标未维护', '的差标')}`}</span> // @i18n-ignore
          })}
        {other && <h3>{other}</h3>}
      </div>
    )
  }
  formatDataLink = dataLink => {
    let data = {}
    forIn(dataLink, (value, key) => {
      const lastIndex = key.lastIndexOf('_')
      data[lastIndex > -1 ? key.substring(lastIndex + 1) : key] = value
    })
    return data
  }
  renderOrderList = orders => {
    const { tripType } = this.props
    return orders.map((item, index) => {
      const order = this.formatDataLink(item.data.dataLink)
      return (
        <div className="order-list-item" key={index}>
          <div>
            {i18n.get('订单号')}:
            <a className="num" onClick={() => this.handleOrderDetail(item)}>
              {
                order['订单号'] // @i18n-ignore
              }
            </a>
          </div>
          {tripType === 'HOTEL' || tripType === 'FOOD' || tripType === 'TAXI' ? (
            <div className="fw-500">
              {moment(order['入住日期']).format(DATEFORMAT) // @i18n-ignore
              }{' '}
              -{' '}
              {moment(order['离店日期']).format(DATEFORMAT) // @i18n-ignore
              }
              , {order.name}
            </div>
          ) : (
            <div className="fw-500">
              {moment(order['出发时间']).format(DATEFORMAT) // @i18n-ignore
              }
              , {order.name}
            </div>
          )}
        </div>
      )
    })
  }
  getActionText = typeName => {
    switch (typeName) {
      case 'HOTEL':
        return i18n.get('订店')
      case 'FOOD':
        return i18n.get('订餐')
      case 'TAXI':
        return i18n.get('用车')
      default:
        return '订票'
    }
  }
  renderTravelers = () => {
    const { tripDataLink } = this.state
    const { orders, traveler, tripType } = tripDataLink
    const buttonText = this.getButtonText(tripType)
    const actionText = this.getActionText(tripType)
    return (
      <section className="trip-order">
        <h5>{i18n.get('出行人')}</h5>
        {traveler.map((item, i) => {
          const ownOrders = orders[item.id]
          let orderStatusCls = ''
          let currentOrder = null
          if (ownOrders) {
            currentOrder = this.formatDataLink(ownOrders[0].data.dataLink)
            orderStatusCls = currentOrder['订单状态'] === '出票' ? 'green' : 'orange' // @i18n-ignore
          }
          return (
            <div className="trip-order-item" key={i}>
              {currentOrder && (
                <div className="header">
                  {i18n.get(`订单号 {__k0}`, {
                    __k0: currentOrder['订单号'] // @i18n-ignore
                  })}
                  {ownOrders.length === 1 ? (
                    <a onClick={() => this.handleOrderDetail(ownOrders[0])}>{i18n.get('详情')}</a>
                  ) : (
                    <Popover
                      overlayClassName="order-tooltip-wrapper"
                      title={i18n.get('订单列表')}
                      content={this.renderOrderList(ownOrders)}
                      trigger="hover"
                      getPopupContainer={trigger => trigger.parentElement}
                    >
                      <a>{i18n.get('查看更多')}</a>
                    </Popover>
                  )}
                </div>
              )}
              <div className="content">
                <div className="dis-f jc-sb mb-4">
                  <span className="em">{item.name}</span>
                  {currentOrder && (
                    <span className={orderStatusCls}>
                      {
                        currentOrder['订单状态'] // @i18n-ignore
                      }
                    </span>
                  )}
                  {!currentOrder && buttonText && (
                    <span className="gray">{i18n.get(`待{__k0}`, { __k0: actionText })}</span> // @i18n-ignore
                  )}
                </div>
                <div>{this.getDeptName(item)}</div>
              </div>
            </div>
          )
        })}
      </section>
    )
  }
  render() {
    const { tripDataLink, disabled, specificationId, referenceStartTime, referenceEndTime } = this.state
    const { entityInfo } = this.props
    if (!tripDataLink) return null
    const { orders, traveler, bill, tripType } = tripDataLink
    const buttonText = this.getButtonText(tripType)
    const isFood = tripType === 'FOOD'
    const dataLink = entityInfo.dataLink
    const remark = dataLink[Object.keys(dataLink).find(o => !!o.endsWith('备注'))] || '' // @i18n-ignore
    const budgetAmount = dataLink[Object.keys(dataLink).find(o => o.endsWith('_预算金额'))] // @i18n-ignore
    const billState = get(bill, 'state', '')
    const stateDisplayObj = stateMap()[billState]
    const stateClassName = billState === 'nullify' ? 'status-nullify' : 'status'
    return (
      <div className={style['trip-detail-wrapper']}>
        {isFood &&
        remark.indexOf('[未同步]') > -1 && ( // @i18n-ignore
            <section className="trip-warning">
              <div className="tips">
                <img src={WARINGIMAGE} />
                {i18n.get('行程单未同步')}
                <Popover
                  trigger="click"
                  title={i18n.get('风险详情')}
                  placement="bottom"
                  content={this.renderContent(remark)}
                >
                  <a>&nbsp;{i18n.get('查看')}</a>
                </Popover>
                {i18n.get('，')}
                {i18n.get('请注意')}
              </div>
            </section>
          )}
        <section className="trip-detail">
          <TripCard trip={tripDataLink} tripsTemplate={this.state.tripsTemplate} referenceStartTime={referenceStartTime} referenceEndTime={referenceEndTime} />
          {disabled && <div className="status">{i18n.get('已停用')}</div>}
          {buttonText && (
            <Button type="primary" disabled={disabled} onClick={this.handleOnClick}>
              {i18n.get(`一键{__k0}`, { __k0: buttonText })}
            </Button>
          )}
          {buttonText && (
            <div className="color-gray ta-c">
              {i18n.get(`当前共 {__k0}/{__k1} 人已成功{__k2}`, {
                __k0: size(orders),
                __k1: traveler.length,
                __k2: buttonText
              })}
            </div>
          )}
        </section>
        {this.renderTravelers()}
        {tripType === 'FOOD' && budgetAmount && (
          <section className="trip-bill">
            <h5>{i18n.get('餐饮金额')}</h5>
            <div className="option-line">
              <div className="grow">
                <div className="dis-f jc-e mb-4">
                  <div className="em">
                    <Money value={budgetAmount} />
                  </div>
                </div>
                <div className="dis-f jc-e">
                  <span>{i18n.get('总金额')}</span>
                </div>
              </div>
            </div>
          </section>
        )}
        {bill && (
          <section className="trip-bill">
            <h5>{i18n.get('关联单据')}</h5>
            <div className="option-line cur-p" onClick={() => this.handleBillDetail(bill)}>
              <label>{billType()[bill.formType]}</label>
              <div className="grow">
                <div className="dis-f jc-sb mb-4">
                  <span className="em">{get(bill, 'form.title', '')}</span>
                  <div className="em">
                    <Money value={get(bill, `form.${bill.formType}Money`, '')} />
                  </div>
                </div>
                <div className="dis-f jc-sb">
                  <span>{specificationId && specificationId.name}</span>
                  <span className={stateClassName}>{stateDisplayObj?.text}</span>
                </div>
              </div>
            </div>
          </section>
        )}
      </div>
    )
  }
}
