import styles from './BillHistoryVersionView.module.less'
import { OutlinedTipsClose } from '@hose/eui-icons'
import React, { PureComponent } from 'react'
import MessageCenter from '@ekuaibao/messagecenter'
import LeftListView from './LeftListView'
import RightDetailView from './RightDetailView'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { app as api } from '@ekuaibao/whispered'

@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
export default class BillHistoryVersionView extends PureComponent {
  bus = new MessageCenter()
  _handleCancel = () => {
    api.invokeService('@bills:change:status:of:notes', false)
    this.props.onCancel && this.props.onCancel()
  }

  render() {
    const {
      item: { flowVersionedId },
      flowId,
      privilegeId,
      showAllFeeType,
      viewFlowState
    } = this.props
    return (
      <div>
        <div id={'BillHistoryVersionView'} className="modal-header">
          <div className="title flex">{i18n.get('查看历史版本')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this._handleCancel} />
        </div>
        <div className={styles['content-wrapper']}>
          <LeftListView flowId={flowId} selectedItemId={flowVersionedId} bus={this.bus} privilegeId={privilegeId} />
          <RightDetailView
            flowId={flowId}
            bus={this.bus}
            privilegeId={privilegeId}
            showAllFeeType={showAllFeeType}
            viewFlowState={viewFlowState}
          />
        </div>
      </div>
    )
  }
}
