@import '~@ekuaibao/web-theme-variables/styles/colors';
.change-apply-modal {
  display: flex;
  flex-direction: column;
  :global {
    .modal-header {
      border-bottom: none;
      height: 56px;
      font-size: 20px;
      .cross-icon {
        font-size: 16px;
      }
    }
    .comment {
      padding: 0 16px 16px 16px;
      .description {
        opacity: 0.75;
        font-size: 14px;
        color: #142234;
        margin-bottom: 20px;
      }
      .ant-form-item {
        margin-bottom: 0;
      }

      .warning-box {
        border-radius: 6px;
        background: var(--eui-function-warning-50, #FFF7E8);
        padding: 10px 16px;
      }
      .warning-item {
        display: flex;
        align-items: flex-start;
        font-size: 14px;
        font-weight: 400;
        color: #142234;
        line-height: 1.5;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      .dot {
        font-size: 6px;
        color: #142234;
        margin: 0 8px;
        flex-shrink: 0;
        margin-top: 6px;
      }
      .highlight {
        color: #ff6b35;
        font-weight: 500;
      }
    }
    .modal-footer-button {
      display: flex;
      flex-direction: row;
      padding: 12px 24px;
      height: 56px;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 4px 24px 0 rgba(29, 43, 61, 0.2);
      .btn-ml {
        margin-right: 8px;
      }
    }
    .footer{
      display: flex;
      justify-content: flex-end;
      padding: 0px 16px 16px 16px;
      margin-top: -8px;
      .btn{
        margin-left: 8px;
      }
    }
  }
}
