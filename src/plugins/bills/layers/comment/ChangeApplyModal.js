/*
 * @Author: 樊超
 * @Date: 2021-08-09 15:39:54
 * @LastEditTime: 2021-08-09 19:12:35
 * @LastEditors: Please set LastEditors
 * @Description: 变更申请原因
 * @FilePath: /web/src/plugins/bills/layers/comment/ChangeApplyModal.js
 */
import React, { PureComponent } from 'react'
import { Icon } from 'antd'
import { Button, Input, Form } from '@hose/eui'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './ChangeApplyModal.module.less'
import EnhanceFormCreate from '../../../../elements/enhance/enhance-form-create'
const { TextArea } = Input
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
@EnhanceFormCreate()
export default class ChangeApplyModal extends PureComponent {
  formRef = React.createRef()

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleModalSave = async () => {
    try {
      let values = await this.formRef.current.validateFields()
      if(values.message === undefined){
        values.message = ''
      }
      this.props.layer.emitOk(values)
    } catch (e) {
      console.log('changeApplyMessageError_', e)
    }
  }

  render() {
    const { mustRequire } = this.props
    return (
      <div id={'ChangeApplyModal'} className={styles['change-apply-modal']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('变更申请')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="comment">
          <div className="warning-box">
            <div className="warning-item">
              <span className="dot">●</span>
              <span>{i18n.get('发起变更将使申请单进入待提交状态，您可以对申请单进行编辑')}</span>
            </div>
            <div className="warning-item">
              <span className="dot">●</span>
              <span>{i18n.get('编辑完成后，单据需要重新审批')}</span>
            </div>
            <div className="warning-item">
              <span className="dot">●</span>
              <span>
                {i18n.get('审批完成前，申请单将')} <span className="highlight">{i18n.get('不可关联订票或报销')}</span>
              </span>
            </div>
            <div className="warning-item">
              <span className="dot">●</span>
              <span>
                {i18n.get('【变更申请】')}<span className="highlight">{i18n.get('操作不可撤回，')}</span>
                <span>{i18n.get('请谨慎操作')}</span>
              </span>
            </div>
          </div>
          <Form ref={this.formRef} layout="vertical" style={{ marginTop: 16 }}>
            <Form.Item
              name="message"
              label={i18n.get('变更申请理由')}
              rules={[
                { required: mustRequire, message: '请填写变更申请理由' },
                { max: 1000, message: i18n.get('变更申请理由不能超过1000个字符') }
              ]}
            >
              <Input.TextArea style={{ height: 140 }} placeholder="请输入变更申请理由" maxLength={1000} showCount />
            </Form.Item>
          </Form>
        </div>
        <div className="footer">
          <Button category="secondary" onClick={this.handleModalClose}>
            {i18n.get('取消')}
          </Button>
          <Button className="btn" type="primary" onClick={this.handleModalSave}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}