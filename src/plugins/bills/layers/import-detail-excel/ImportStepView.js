import React, { PureComponent, Fragment } from 'react'
import { Button, Spin, Alert, Icon, Checkbox, Tooltip } from 'antd'
import { FilesUploader } from '@ekuaibao/uploader'
import { getDownloadTemplate, getUploadUrl } from './util'
import { EnhanceConnect } from '@ekuaibao/store'
import FAIL_ICON from '../../../../images/fail-icon.svg'
import SUCCESS_ICON from '../../../../images/success-icon.svg'
import Excel from '../../images/Excel.svg'

import { enableMemoryMonitoring, endMemoryMonitoring } from '../../../../utils/ImportStepViewMemoryPatch'
import excelImportTracker from '../../../../utils/ExcelImportMemoryTracker'

@EnhanceConnect(state => ({
  HSBC: state['@common'].powers.HSBC,
  KA_FOREIGN_ACCOUNT: state['@common'].powers.KA_FOREIGN_ACCOUNT
}))
export default class ImportStepView extends PureComponent {
  state = {
    step: 1,
    details: [],
    successCount: 0,
    importResult: null,
    errorFile: undefined,
    errorType: '',
    errorText: '',
    expenseStandardData: '',
    autoFix: false
  }

  componentDidMount() {
    enableMemoryMonitoring(this)
  }

  // 优化的ArrayBuffer转字符串方法
  arrayBufferToString = (buffer, encoding = 'utf-8') => {
    try {
      excelImportTracker.trackArrayBufferConversionStart(buffer)
      // 检查数据大小，如果太大则分批处理
      const maxChunkSize = 1024 * 1024 // 1MB chunks
      const uint8Array = new Uint8Array(buffer)

      let result = ''
      if (uint8Array.length <= maxChunkSize) {
        // 小文件直接处理
        result = new TextDecoder(encoding).decode(uint8Array)
      } else {
        // 大文件分批处理
        for (let i = 0; i < uint8Array.length; i += maxChunkSize) {
          const chunk = uint8Array.slice(i, i + maxChunkSize)
          result += new TextDecoder(encoding).decode(chunk)
        }
      }

      excelImportTracker.trackArrayBufferConversionEnd(result)
      return result
    } catch (error) {
      console.error('TextDecoder转换失败，尝试降级方案:', error)

      try {
        // 降级方案1: 尝试不同编码
        const encodings = ['utf-8', 'gbk', 'gb2312', 'iso-8859-1']
        for (const enc of encodings) {
          try {
            const result = new TextDecoder(enc).decode(new Uint8Array(buffer))
            console.log(`使用编码 ${enc} 转换成功`)
            excelImportTracker.trackArrayBufferConversionEnd(result)
            return result
          } catch (e) {
            continue
          }
        }

        // 降级方案2: 使用原始方法但分块处理
        console.log('使用原始方法分块处理')
        if (buffer.byteLength > 50 * 1024 * 1024) { // 50MB
          throw new Error('文件过大，请联系管理员')
        }

        const uint8Array = new Uint8Array(buffer)
        const chunkSize = 8192 // 8KB chunks for String.fromCharCode
        let result = ''

        for (let i = 0; i < uint8Array.length; i += chunkSize) {
          const chunk = uint8Array.slice(i, i + chunkSize)
          result += String.fromCharCode.apply(null, chunk)
        }

        excelImportTracker.trackArrayBufferConversionEnd(result)
        return result
      } catch (fallbackError) {
        console.error('所有转换方法都失败:', fallbackError)
        throw new Error('数据转换失败，请检查文件格式')
      }
    }
  }

  handleChange = info => {
    let file = info[0]
    this.setState({ step: 2 })

    if (file.xhr && file.xhr.status === 204) {
      if (this.props.type === 'dimensionMap') {
        this.setState({ step: 3, errorType: '' })
        return
      } else {
        this.props.onOk && this.props.onOk({ state: 'loading', name: file.name })
      }
    }

    if (file.status === 'done') {
      let { xhr } = file
      let type = xhr && xhr.getResponseHeader('content-type')
      const { response } = file

      if (type === 'application/json') {
        try {
          // 使用优化的转换方法
          const textData = this.arrayBufferToString(response)
          excelImportTracker.trackJSONParseStart(textData)

          // 尝试直接解析JSON，如果失败则尝试不同的编码处理
          let obj
          try {
            obj = JSON.parse(textData)
          } catch (parseError) {
            console.log('直接JSON解析失败，尝试编码处理:', parseError.message)

            // 尝试不同的编码方式
            const encodingMethods = [
              // 方法1: 尝试UTF-8 BOM处理
              () => {
                let cleanText = textData
                if (cleanText.charCodeAt(0) === 0xFEFF) {
                  cleanText = cleanText.slice(1) // 移除BOM
                }
                return JSON.parse(cleanText)
              },

              // 方法2: 尝试Latin-1编码重新解析
              () => {
                const latinText = this.arrayBufferToString(response, 'iso-8859-1')
                const utf8Text = decodeURIComponent(escape(latinText))
                return JSON.parse(utf8Text)
              },

              // 方法3: 手动处理特殊字符
              () => {
                const cleanText = textData.replace(/[\x00-\x1F\x7F]/g, '') // 移除控制字符
                return JSON.parse(cleanText)
              }
            ]

            for (let i = 0; i < encodingMethods.length; i++) {
              try {
                obj = encodingMethods[i]()
                console.log(`编码方法 ${i + 1} 解析成功`)
                break
              } catch (e) {
                console.log(`编码方法 ${i + 1} 失败:`, e.message)
                if (i === encodingMethods.length - 1) {
                  throw parseError // 抛出原始错误
                }
              }
            }
          }

          excelImportTracker.trackJSONParseEnd(obj)

          // 导入出错时，兼容服务器报错
          if (obj.errorCode) {
            this.setState({ step: 3, errorType: 'other', errorText: obj.errorMessage })
          } else {
            // 使用setTimeout让UI有机会更新，避免阻塞
            setTimeout(() => {
              this.fnHandleChange(obj)
            }, 0)
          }
        } catch (err) {
          console.error('JSON解析失败:', err)
          this.setState({ step: 3, errorType: 'status', errorText: '数据解析失败，请检查文件格式' })
        }
      } else {
        let headerStr = decodeURIComponent((xhr && xhr.getResponseHeader('content-disposition')) || '')
        let errorList = []
        try {
          let h = headerStr.substring(headerStr.search(/(\[(\d+),(\d+)\]\.xlsx")$/))
          let l = h.split('.')
          let s = l[0]
          errorList = JSON.parse(s)
        } catch (err) {}

        let blob = new Blob([response], { type: 'application/octet-stream' })
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          let errorFile = {
            name: file.name,
            errorList: errorList,
            blob
          }
          this.setState({ step: 3, errorFile, errorType: 'file' })
        } else {
          let urlObject = window.URL || window.webkitURL || window
          let url = urlObject.createObjectURL(blob)
          let errorFile = {
            url,
            name: file.name,
            errorList: errorList
          }
          this.setState({ step: 3, errorFile, errorType: 'file' })
        }
      }
    }

    if (file.status === 'error') {
      try {
        // 使用优化的转换方法处理错误响应
        const textData = this.arrayBufferToString(file.response.errorResonse)

        // 直接使用转换后的文本，避免escape/decodeURIComponent的问题
        let errorText = textData

        // 如果需要处理编码问题，尝试不同方法
        if ((textData && textData.includes('�')) || textData.includes('\ufffd')) {
          try {
            // 尝试Latin-1编码处理
            const latinText = this.arrayBufferToString(file.response.errorResonse, 'iso-8859-1')
            errorText = decodeURIComponent(escape(latinText))
          } catch (encodeError) {
            console.log('编码处理失败，使用原始文本:', encodeError.message)
            errorText = textData
          }
        }

        this.setState({ step: 3, errorType: 'status', errorText })
      } catch (err) {
        console.error('错误响应解析失败:', err)
        this.setState({ step: 3, errorType: 'status', errorText: '上传失败，请重试' })
      }
    }
  }

  handleChangeStep = step => {
    this.setState({ step })
  }

  handleFinish = () => {
    const { result } = this.fnGetKindsTypeMap()
    this.props.onOk && this.props.onOk(result)
  }

  fnBuildData(file) {
    const { type, flag } = this.props
    let fileData = { name: file.name }
    if (type === 'expenseStandard') {
      const {
        flag: { fieldConfig }
      } = this.props
      fileData = { ...fileData, fieldConfig: JSON.stringify(fieldConfig) }
    }

    if (type === 'detail') {
      fileData.billData = JSON.stringify(flag.billData)
    }
    if (type === 'role') {
      fileData.file2 = file
    }
    if (type === 'approveMatrix') {
      const {
        flag: { fieldConfig }
      } = this.props
      fileData = { ...fileData, config: JSON.stringify(fieldConfig) }
    }
    return fileData
  }

  // 分批处理大数据
  processBatchData = (data, batchSize = 1000) => {
    return new Promise((resolve) => {
      const batches = []
      for (let i = 0; i < data.length; i += batchSize) {
        batches.push(data.slice(i, i + batchSize))
      }

      let processedData = []
      let currentBatch = 0

      const processBatch = () => {
        if (currentBatch >= batches.length) {
          resolve(processedData)
          return
        }

        // 处理当前批次
        processedData = [...processedData, ...batches[currentBatch]]
        currentBatch++

        // 使用setTimeout让浏览器有机会处理其他任务
        setTimeout(processBatch, 10)
      }

      processBatch()
    })
  }

  fnHandleChange = async obj => {
    const { type, flag, bus } = this.props

    if (type === 'detail') {
      const newDetails = obj.details

      // 检查数据量，如果过大则分批处理
      if (newDetails && newDetails.length > 5000) {
        console.log(`数据量较大(${newDetails.length}条)，启用分批处理`)

        try {
          const processedDetails = await this.processBatchData(newDetails)
          const currentDetails = this.state.details
          const f = processedDetails[0]
          const {
            billSpecification: { type }
          } = flag

          if (f && f.specificationId && !~f.specificationId.indexOf(type)) {
            this.setState({ step: 3, errorType: 'type' })
            return
          }

          const details = [...currentDetails, ...processedDetails]
          this.setState({ step: 3, details, errorType: '' })
          return
        } catch (error) {
          console.error('分批处理失败:', error)
          this.setState({ step: 3, errorType: 'other', errorText: '数据处理失败，请重试' })
          return
        }
      }

      // 小数据量直接处理
      const currentDetails = this.state.details
      const f = newDetails[0]
      const {
        billSpecification: { type }
      } = flag

      if (f && f.specificationId && !~f.specificationId.indexOf(type)) {
        this.setState({ step: 3, errorType: 'type' })
        return
      }

      // 使用展开运算符，更高效的数组合并
      const details = [...currentDetails, ...newDetails]
      this.setState({ step: 3, details, errorType: '' })
      return
    }
    if (type === 'apportion' || type === 'planPay') {
      const { status, data, url, message } = obj.value
      if (status === 500) {
        this.setState({ errorFile: { url, message }, step: 3, errorType: 'file' })
        return
      }
      // 优化：使用展开运算符合并数组
      const details = [...this.state.details, ...data]
      excelImportTracker.trackArrayOperation('concat', this.state.details, details)
      this.setState({ step: 3, errorType: '', details, successCount: data.length })
      const report = window.memoryMonitor.generateReport()
      console.log('📊 手动生成的内存报告:', report)
      endMemoryMonitoring()
      excelImportTracker.endTracking()
      return
    } else if (type === 'receipt') {
      const { status, url, message } = obj.value
      if (status === 200) {
        this.setState({ step: 3, errorType: '' })
        return
      }
      this.setState({ errorFile: { url, message }, step: 3, errorType: 'file' })
      return
    }
    let count = obj.count || 0
    let retCode = obj.retCode || 0 //重复个数
    if (type === 'dataLink') {
      count = obj.successCount
    } else if (type === 'staff') {
      count = obj.newCnt
    }
    let expenseStandardData = ''
    let expenseStandarderrorType = ''
    let errorFile = {}
    if (type === 'expenseStandard') {
      if (obj.error.count) {
        expenseStandarderrorType = 'file'
        errorFile.url = obj.error.url
      } else {
        count = obj.successCount
      }
      expenseStandardData = obj
    }
    if (type === 'supplier_bill_import_excel') {
      this.setState({ step: 3, details: obj?.value, errorType: '' }, () => {
        bus.emit('upload:over')
      })
      return
    }
    if (type === 'corppayment') {
      this.setState({ step: 3, details: obj?.items, errorType: '', successCount: obj?.items?.length }, () => {
        bus.emit('upload:over')
      })
    }
    if (type === 'auditPaid' && obj?.state != 'SUCCESS') {
      ;(expenseStandarderrorType = 'file'), (errorFile.url = obj?.data?.url)
      expenseStandardData = obj
    }
    if (type === 'approveMatrix') {
      if (obj.error.count) {
        expenseStandarderrorType = 'file'
        errorFile.url = obj.error.url
      } else {
        count = obj.successCount
      }
      expenseStandardData = obj
    }

    if (type === 'datalink4flow') {
      let errorType = ''
      if (obj.value?.state === 'FAILED') {
        errorFile.url = obj.value?.href
        errorType = 'file'
      } else {
        count = obj?.value?.items?.length
      }
      this.setState({ step: 3, errorType, errorFile, successCount: count, details: obj?.value?.items }, () => {
        bus.emit('upload:over')
      })
      return
    }
    this.setState({
      step: 3,
      errorType: expenseStandarderrorType,
      errorFile,
      successCount: count,
      retCode,
      expenseStandardData,
      importResult: type === 'staff' ? obj : null
    })
  }

  fnGetKindsTypeMap = () => {
    const { type, flag, HSBC, KA_FOREIGN_ACCOUNT } = this.props
    const { autoFix } = this.state
    const { details = [], successCount, errorType, expenseStandardData, importResult, retCode } = this.state
    const HSBCandKA = KA_FOREIGN_ACCOUNT
    let result = 'success'
    let finalCount = successCount
    const downloadTemplateAction = getDownloadTemplate.call(this, type, flag, HSBCandKA)
    const uploadUrl = getUploadUrl(type, flag, autoFix)

    if (type === 'detail') {
      result = details
      finalCount = details.length
    }
    if (type === 'apportion' || type === 'planPay') {
      result = details
      finalCount = details.length
    }

    if (type === 'expenseStandard') {
      result = expenseStandardData
    }

    if (type === 'corppayment') {
      result = details
      finalCount = details.length
    }

    let title = i18n.get('import-success', { count: finalCount })
    let label = i18n.get('validation-success', { length: finalCount })
    if (type === 'supplier_bill_import_excel') {
      result = details
      const { entityList, errorMessage = [], errorTotal, successTotal } = details
      title =
        errorTotal === 0
          ? `恭喜！成功导入 ${successTotal} 个商品品类`
          : `成功导入 ${successTotal} 个商品品类,失败 ${errorTotal} 个商品品类,原因如下:`
      label =
        errorTotal === 0
          ? i18n.get('解析完成！您可在账单模板设置中进行编辑')
          : errorMessage?.map((it, i) => {
              return (
                <div className="">
                  {i + 1}. {it}
                </div>
              )
            })
    }
    if (
      type === 'dimensionMap' ||
      type === 'role' ||
      type === 'cityGroup' ||
      type === 'auditPaid' ||
      type === 'receipt'
    ) {
      title = i18n.get('恭喜！成功导入')
      label = ''
    }
    let needRepeat = true
    if (type === 'dimensionMap') {
      title = i18n.get('文件上传成功')
      label = i18n.get('导入结果在页面中查看')
      needRepeat = !!errorType
    }
    if (type === 'approveMatrix') {
      result = expenseStandardData
      needRepeat = !!errorType
    }
    if (type === 'datalink4flow') {
      result = details
      finalCount = details?.length
    }
    if (type === 'staff' && importResult) {
      label = i18n.get('成功停用{__k0}个员工，激活{__k1}个员工', {
        __k0: importResult.unAuthCnt,
        __k1: importResult.authCnt
      })
    }
    if (type === 'paymentSetting' && retCode) {
      label = i18n.get('成功导入{__k0}条数据，有{__k1}条数据已存在', {
        __k0: finalCount,
        __k1: retCode
      })
    }
    if (errorType) {
      const { errorTitle, errorLabel } = this.getErrorInfo()
      title = errorTitle
      label = errorLabel
    }
    return {
      downloadTemplateAction,
      uploadUrl,
      result,
      title,
      label,
      needRepeat
    }
  }

  getErrorInfo = () => {
    const { type } = this.props
    const { errorType, errorText, errorFile, expenseStandardData } = this.state
    let errorTitle = i18n.get('抱歉无法导入')
    let errorLabel = errorText
    if (type === 'detail') {
      errorLabel = i18n.get('无法解析该文件')
      if (errorType === 'file') {
        errorLabel = i18n.get('detail-error-count', {
          count: errorFile.errorList[0],
          detail: errorFile.errorList[1]
        })
      }
      if (errorType === 'type') {
        errorLabel = i18n.get('导入消费记录的模版不匹配')
      }
      if (errorType === 'other') {
        errorLabel = errorText || errorLabel
      }
    }
    if (errorType === 'file' && type === 'dimensionMap') {
      errorLabel = i18n.get('dimension-err-count', {
        count: errorFile.errorList[0]
      })
    }
    if (type === 'dataLink') {
      if (errorType === 'file') errorLabel = i18n.get('errorName')
    }
    if (type === 'expenseStandard') {
      if (errorType === 'file') {
        errorLabel = i18n.get('expenseStandard-detail-error', {
          count: expenseStandardData.error.count
        })
      }
    }
    if (type === 'apportion') {
      if (errorType === 'file') {
        errorLabel = errorType.message
      }
    }
    if (type === 'planPay') {
      if (errorType === 'file') {
        errorLabel = i18n.get(`错误数据 {__k0} 条`, { __k0: errorFile.message })
      }
    }
    if (type === 'auditPaid') {
      if (errorType === 'file') {
        errorTitle = i18n.get('导入失败')
        errorLabel = expenseStandardData?.failureReason
      }
    }
    if (type === 'datalink4flow') {
      if (errorType === 'file') {
        errorTitle = i18n.get('导入失败')
      }
    }
    return { errorTitle, errorLabel }
  }

  renderFinishView() {
    const { errorFile, errorType } = this.state
    const { confirmMsg } = this.props
    const { title, label, needRepeat } = this.fnGetKindsTypeMap()
    return (
      <div className="finish-wrapper">
        <div className="img-wrapper">
          <img src={errorType ? FAIL_ICON : SUCCESS_ICON} alt="" />
        </div>
        <div className="result-txt">{title}</div>
        <div className="text">{label}</div>
        <div className="next-step">
          {errorType === 'file' && errorFile.url && (
            <Button
              type="primary"
              className="mb-5"
              onClick={() => (errorFile.blob ? window.navigator.msSaveOrOpenBlob(errorFile.blob, errorFile.name) : '')}
            >
              <a download={errorFile.name} style={{ color: '#FFFFFF' }} href={errorFile.url}>
                {i18n.get('下载标错版Excel')}
              </a>
            </Button>
          )}
          {!errorType && (
            <Button type="primary" className="finish-button mb-5" onClick={this.handleFinish}>
              {i18n.get('完 成')}
            </Button>
          )}
          {needRepeat && (
            <div className="repeat-upload">
              <a href="javascript:void(0);" onClick={() => this.handleChangeStep(confirmMsg ? 4 : 1)}>
                {!errorType ? i18n.get('返回继续') : i18n.get('返回重新')}
                {i18n.get('上传Excel')}
              </a>
            </div>
          )}
        </div>
      </div>
    )
  }

  changeAutoFix = e => {
    this.setState({
      autoFix: e.target.checked
    })
  }

  getUploadData = () => {
    const { uploadUrl } = this.fnGetKindsTypeMap()
    return {
      action: uploadUrl,
      onChange: this.handleChange.bind(this),
      multiple: false,
      data: this.fnBuildData.bind(this),
      accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      responseType: 'arraybuffer'
    }
  }

  renderConfirmView = () => {
    const { confirmMsg } = this.props
    const props = this.getUploadData()
    return (
      <div className="finish-wrapper">
        <div className="img-wrapper">
          <img src={FAIL_ICON} alt="" />
        </div>
        <div className="result-txt">{confirmMsg}</div>
        <div className="next-step" style={{ marginTop: '60px' }}>
          <FilesUploader {...props}>
            <Button type="primary" className="mb-5">
              {i18n.get('确认并上传')}
            </Button>
          </FilesUploader>
          <div className="repeat-upload" onClick={() => this.handleChangeStep(1)}>
            <a href="javascript:void(0);">
              {i18n.get('返回重新')}
              {i18n.get('上传Excel')}
            </a>
          </div>
        </div>
      </div>
    )
  }

  render() {
    const { downloadTemplateAction } = this.fnGetKindsTypeMap()
    const props = this.getUploadData()
    const {
      noticeMsg,
      confirmMsg,
      type,
      getCurrentConfigtemp,
      getBlankTemplate,
      Component,
      renderReminder
    } = this.props
    const { step } = this.state
    return (
      <Fragment>
        <div className="invoice-step-wrapper">
          {(confirmMsg ? step === 1 : step !== 3) && (
            <div className="step-content">
              {!!noticeMsg && <Alert className="alert-wrapper" message={noticeMsg} type="info" showIcon />}
              {type === 'supplier_import_excel' || type === 'supplier_bill_import_excel' ? (
                <FilesUploader {...props}>
                  <div className="btn">
                    <img src={Excel} />
                    {i18n.get('上传 Excel 文件')}
                  </div>
                </FilesUploader>
              ) : (
                <>
                  <div className="button-wrapper">
                    <span className="label">
                      {i18n.get('步骤一')}
                      {i18n.get('：')}
                    </span>
                    {type === 'expenseStandard' ? (
                      <div>
                        <a
                          className="expense-link-button"
                          href="javascript:void(0);"
                          onClick={() => getCurrentConfigtemp()}
                        >
                          {i18n.get('导出当前配置')}
                        </a>
                        <span className="or">{i18n.get('或者')}</span>
                        <a
                          className="expense-link-button"
                          href="javascript:void(0);"
                          onClick={() => getBlankTemplate()}
                        >
                          {i18n.get('下载空白模板')}
                        </a>
                      </div>
                    ) : (
                      <a className="link-button" href="javascript:void(0);" onClick={downloadTemplateAction}>
                        {i18n.get('点击下载Excel模板')}
                      </a>
                    )}
                  </div>
                  <div className="button-wrapper">
                    <span className="label">
                      {i18n.get('步骤二')}
                      {i18n.get('：')}
                    </span>
                    {confirmMsg ? (
                      <Button className="button" onClick={() => this.handleChangeStep(4)}>
                        {i18n.get('上传Excel')}
                      </Button>
                    ) : (
                      <FilesUploader {...props}>
                        <Button className="button">{i18n.get('上传Excel222')}</Button>
                      </FilesUploader>
                    )}
                  </div>
                </>
              )}
            </div>
          )}
          {step === 2 && (
            <div className="load">
              <Spin
                tip={
                  <div>
                    <div className="load-text">{i18n.get('Excel校验并导入中')}</div>
                    <div className="load-text">{i18n.get('请稍后…')}</div>
                  </div>
                }
              />
            </div>
          )}
          {step === 3 && this.renderFinishView()}
          {confirmMsg && (step === 4 || step === 2) && this.renderConfirmView()}
        </div>
        {Component && <Component onChange={this.changeAutoFix} />}
        {renderReminder && renderReminder()}
      </Fragment>
    )
  }
}
