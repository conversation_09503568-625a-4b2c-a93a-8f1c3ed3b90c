/**************************************************
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/07/2017 13:15.
 **************************************************/
import styles from './FeeDetailReadonly.module.less'
import React, { PureComponent } from 'react'
import { Col, Row } from 'antd'
import { showModal } from '@ekuaibao/show-util'
import { EnhanceConnect } from '@ekuaibao/store'
import DynamicReadonly from '../../views/DynamicReadonly'
import { parseAsMeta, parseAsReadOnlyFormTemplate, presetFormulaValue } from '../../util/parse'
import FeeItem from '../../../../elements/puppet/consume/FeeItem'
import { getCalculateField, getFeeTypeTemplateById } from '../../bills.action'
import WarningOrErrorTips from '../../elements/WarningOrErrorTips'
import FeeDetailBottom from './FeeDetailBottom'
import { app as api } from '@ekuaibao/whispered'
import { cloneDeep, get, debounce } from 'lodash'
const { standardValueMoney } = api.require('@lib/misc')
import InvoiceCard from '../../../../elements/invoice-card'
import ThirdPartyCard from 'ekbc-thirdParty-card/esm/thirdCard'
import ThirdCard from '../../../../elements/thirdCard/third-card'
import { inputInvoiceImport } from '../../parts/right-part/billInfo/BillImport/invoiceImport'
import MessageCenter from '@ekuaibao/messagecenter'
import { getExtraRiskWarningList, getRiskInTemplate } from '../../riskWarning/formatRiskWarningData'
import EKBIcon from '../../../../elements/ekbIcon'
import Money from '../../../../elements/puppet/Money'
import { toJS } from 'mobx'
import { fnLinkDetailEntitiesValue, getSpecificationHiddenFields } from '../../util/billUtils'
import InvoiceTaxInfo from './../../../../elements/CarouselInvoiceReviewer/utils/InvoiceTaxInfo'
import { connect } from '@ekuaibao/mobx-store'
import { updateAutoCalResult } from '../../util/autoCalculate'
import { fnHideFieldsNote, fnFlowHideFields } from '../../../../components/utils/fnHideFields'
import { splitTemplateToGroups } from '../../util/billUtils'
import { fnFlowShowFields } from '../../../../components/utils/fnShowFields'
import { getTrueKey } from '../../../../lib/utils'
import { updateInvoiceDeduction } from '../../../../lib/InvoicePriceTaxSeparated'
import { resetForeignInvoiceForEntity } from '../../../../lib/InvoiceUtil'
const svg = (
  <span style={{ display: 'inline-flex' }}>
    <EKBIcon name="#EDico-didi" style={{ color: '#1E96FA', width: '16px', height: '16px' }} />
  </span>
)

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(
  state => {
    return {
      baseDataProperties: state['@common'].globalFields.data,
      multiplePayeesMode: state['@bills'].multiplePayeesMode,
      payPlanMode: state['@bills'].payPlanMode,
      baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap,
      dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo,
      smartApproveCharge: state['@common'].powers.INTELLIGENT_APPROVAL, // 智能审批charge开关
      allStandardCurrency: state['@common'].allStandardCurrency
    }
  },
  {
    getFeeTypeTemplateById,
    getCalculateField
  }
)
export default class FeeDetailReadonly extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      hiddenFields: [],
      currentDoc: void 0,
      currentIdx: 0,
      nextAndPr: false,
      submitterId: undefined,
      calValue: [],
      waitCalculateFields: [],
      templateAll: [],
      template: [],
      tagChangeCount: 0 // 明细标签变化次数
    }
    this.invoiceTaxInfo = new InvoiceTaxInfo()
    this.bus = props.bus || new MessageCenter()
  }

  getSpeHideFields = async dataSource => {
    const specification = dataSource?.specificationId ?? {}
    const hiddenFields = await getSpecificationHiddenFields(specification)
    this.setState({ hiddenFields })
  }

  async componentWillMount() {
    api.dataLoader('@common.payerInfo').load()
    let { dataSource, details = [] } = this.props
    this.ds = details
    let currentIdx = dataSource?.idx
    if (currentIdx === undefined) {
      const index = details.findIndex(el => el.feeTypeId.id === dataSource.feeTypeId.id)
      currentIdx = !!~index ? index : 0
    }

    await this.getSpeHideFields(this.props.dataSource)

    this._fnGetCalculateFields(currentIdx, dataSource)

    this.bus.on('invoice-Attachment-Upload', this.handleAttachmentUploadSucc)
    this.bus.on('save:supplement:invoice:import', this.handleSupplementaryInvoice)
    this.bus.on('supplement:invoice:edit', this.handleSupplementaryInvoiceEdit)
    this.bus.on('detail:delete:invoice', this.handleDeleteInvoice)
    this.bus.on('supplement:invoice:confirm', this.handleConfirmInvoice)
    this.bus.watch('supplement:invoice:import:click', this.fnDetailsImportClick)
    this.bus.watch('supplement:invoice:input:import:click', this.fnDetailsInputImportClick)
    this.bus.watch('supplement:invoice:import:ocr:click', this.fnDetailsImportOCRClick)
    this.bus.watch('supplement:invoice:import:aliPay:click', this.fnDetailsImportAliPayClick)
    this.bus.watch('supplement:invoice:import:aifapiao:click', this.fnDetailsAifapiaoImportClick)
    this.bus.on('feedetails:update:detail', this.handleUpdateDetail)
    this.bus.on('element:datalink:card:click', this.fnHandleDataLinkCardClick)
    this.bus.on('previous:action', this.handlePrev)
    this.bus.on('next:action', this.handleNext)
    this.bus.on('invoice:review:close', this.setCurrentDocForReview)
    this.bus.on('update:billInfo', this.handleUpdateBillInfo)
    this.bus.on('change:feedetails:linkDetailEntities:data', this.changeLinkDetailEntities)
    this.bus.on('feedetails:approveamount:change', this._fnAutoApproveAmountChange)
    this.bus.on('update:calculate:detail:template', this.updateCalDetailTemplate)
    this.bus.on('details:tag:change', this.handleTagChange)
    api.watch('get:current:feeType:value', this.handleCurrentFeeTypeValue)
  }
  handleCurrentFeeTypeValue = () => {
    const { dataSource, billSpecification } = this.props
    const { feeTypeId } = dataSource
    return {
      formSpecId: billSpecification?.id ?? '',
      feeTypeSpecId: feeTypeId?.id ?? ''
    }
  }
  componentWillUnmount() {
    api.un('get:current:feeType:value', this.handleCurrentFeeTypeValue)
    this.bus.un('invoice-Attachment-Upload', this.handleAttachmentUploadSucc)
    this.bus.un('save:supplement:invoice:import', this.handleSupplementaryInvoice)
    this.bus.un('supplement:invoice:confirm', this.handleConfirmInvoice)
    this.bus.un('supplement:invoice:edit', this.handleSupplementaryInvoiceEdit)
    this.bus.un('supplement:invoice:import:click', this.fnDetailsImportClick)
    this.bus.un('supplement:invoice:input:import:click', this.fnDetailsInputImportClick)
    this.bus.un('supplement:invoice:import:ocr:click', this.fnDetailsImportOCRClick)
    this.bus.un('supplement:invoice:import:aifapiao:click', this.fnDetailsAifapiaoImportClick)
    this.bus.un('feedetails:update:detail', this.handleUpdateDetail)
    this.bus.un('previous:action', this.handlePrev)
    this.bus.un('next:action', this.handleNext)
    this.bus.un('invoice:review:close', this.setCurrentDocForReview)
    this.bus.un('update:billInfo', this.handleUpdateBillInfo)
    this.bus.un('detail:delete:invoice', this.handleDeleteInvoice)
    this.bus.un('element:datalink:card:click', this.fnHandleDataLinkCardClick)
    this.bus.un('change:feedetails:linkDetailEntities:data', this.changeLinkDetailEntities)
    this.bus.un('feedetails:approveamount:change', this._fnAutoApproveAmountChange)
    this.bus.un('update:calculate:detail:template', this.updateCalDetailTemplate)
    this.bus.un('details:tag:change', this.handleTagChange)
  }
  // 格式化海外发票数据
  formatOverseasInvoiceBytDimension = value => {
    const { formAllData } = this.props
    const legalEntityMultiCurrency = get(formAllData, 'legalEntityMultiCurrency')
    const baseCurrencyId = get(legalEntityMultiCurrency, 'form.baseCurrencyId', '')
    // 有法人实体时检查
    if (legalEntityMultiCurrency && baseCurrencyId) {
      value.feeTypeForm = resetForeignInvoiceForEntity(value.feeTypeForm)
      return value
    }
    return value
  }

  _fnAutoApproveAmountChange = detailId => {
    let { details = [] } = this.props
    this.ds = details
    let { currentIdx } = this.state
    let fee = this.ds[currentIdx] || {}
    this._fnGetCalculateFields(currentIdx, fee, true)
  }
  _fnGetCalculateFields(idx, dataSource, nextAndPr = false) {
    const { getCalculateField, submitterId } = this.props
    const { specificationId } = dataSource
    this.bus.emit('delete:invoiceForm:invoiceAllDetails')
    getCalculateField(specificationId.id, submitterId?.id).then(action => {
      if (action.error) {
        return
      }
      const autoRules = action.payload.items
      const autoCalFields = autoRules?.[0]
      this.isFirstAutoCalFinished = false
      this.setState({
        waitCalculateFields: autoCalFields?.onFields || []
      })
      this.fnGetFeetypeStandard(undefined, undefined, true, toJS(dataSource))
      this.splicingDataLogic(idx, toJS(dataSource), autoCalFields, nextAndPr)
    })
  }
  updateCalDetailTemplate = value => {
    this.setState({
      calValue: value
    })
  }

  handleTagChange = () => {
    const { tagChangeCount } = this.state
    this.setState({ tagChangeCount: tagChangeCount + 1 })
  }

  isFirstAutoCalFinished = false
  fnGetFeetypeStandard = debounce(async (changeValues = {}, isTextChange, checkDefaultValue) => {
    const {
      bus,
      billData = {},
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      flowId,
      dataSource,
      dimentionCurrencyInfo,
      submitterId,
      formAllData
    } = this.props
    const { feeTypeForm, specificationId, feeTypeId } = dataSource
    let formValue = { ...feeTypeForm, ...changeValues }
    let template = parseAsMeta(specificationId, baseDataProperties)

    //老补助的自动计算后台要求必须传amount
    const amountField = template.find(t => t.name === 'amount') || {}
    const type = get(amountField, 'defaultValue.type')
    //手动填写的自动计算影响老费标
    if (!(amountField.editable === true && type === 'formula') && !formValue.amount) {
      formValue = { ...formValue, amount: standardValueMoney(0, dimentionCurrencyInfo?.currency) }
    }

    const fee = {
      feeTypeId: feeTypeId.id,
      specificationId: specificationId?.id,
      feeTypeForm: formValue
    }
    let allData = cloneDeep(formAllData)
    for (let key in allData) {
      billData[key] = allData[key]
    }
    billData.details = [fee]
    billData.submitterId = submitterId
    if (!!dataSource.flowId) {
      billData.flowId = dataSource.flowId
    }

    const needUpdateDefaultValue = checkDefaultValue
    const updateAutoCalResultAttribute = true
    await updateAutoCalResult(
      'detail_',
      billData,
      fee,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      bus,
      undefined,
      changeValues,
      isTextChange,
      needUpdateDefaultValue,
      template,
      updateAutoCalResultAttribute
    )
    if (checkDefaultValue) {
      this.isFirstAutoCalFinished = true
    }
  }, 400)

  updateCalDetailTemplate = value => {
    const calValue = value
    let { templateAll: template } = this.state
    const { fullDataSource } = this.props
    // 隐藏字段公式计算后符合条件则隐藏
    const calFields = Object.keys(calValue)
    const { currentNodeShowFieldMap, isShowFileds } = fnFlowShowFields(fullDataSource?.plan)
    calFields.forEach(field => {
      if (currentNodeShowFieldMap[field]) {
        return
      }
      let index = template.findIndex(el => el.field === field)
      if (index !== -1) {
        // template[index] = { ...template[index], ...calValue[field] }
        let attrs = calValue[field]
        let autoHide = attrs?.attributeHide
        const _hideVisibility = template[index]?.hideVisibility
        let hasStaffs = true
        const { departments, roles, staffs } = _hideVisibility || { departments: [], roles: [], staffs: [] }
        if (!departments?.length && !roles?.length && !staffs?.length) {
          hasStaffs = false
        }
        if ((autoHide && !hasStaffs) || (autoHide && !fnHideFieldsNote(_hideVisibility))) {
          template.splice(index, 1)
        } else {
          template[index] = { ...template[index], ...calValue[field] }
        }
      }
    })

    if (isShowFileds) {
      template = template.map(field => {
        if (currentNodeShowFieldMap[field.name]) {
          return { ...field, currentNodeShowField: true }
        }
        return field
      })
    }

    this.setState({
      calValue: value,
      template
    })
  }

  isFirstAutoCalFinished = false
  fnGetFeetypeStandard = debounce(async (changeValues = {}, isTextChange, checkDefaultValue, currentDoc) => {
    const {
      bus,
      billData = {},
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      flowId,
      dataSource,
      dimentionCurrencyInfo,
      submitterId,
      formAllData
    } = this.props
    const { feeTypeForm, specificationId, feeTypeId } = currentDoc
    let formValue = { ...feeTypeForm, ...changeValues }
    let template = parseAsMeta(specificationId, baseDataProperties)
    //老补助的自动计算后台要求必须传amount
    const amountField = template.find(t => t.name === 'amount') || {}
    const type = get(amountField, 'defaultValue.type')
    //手动填写的自动计算影响老费标
    if (!(amountField.editable === true && type === 'formula') && !formValue.amount) {
      formValue = { ...formValue, amount: standardValueMoney(0, dimentionCurrencyInfo?.currency) }
    }

    const fee = {
      feeTypeId: feeTypeId.id,
      specificationId: specificationId?.id,
      feeTypeForm: formValue
    }
    let allData = cloneDeep(formAllData)
    for (let key in allData) {
      billData[key] = allData[key]
    }
    billData.details = [fee]
    billData.submitterId = submitterId
    if (!!currentDoc.flowId) {
      billData.flowId = currentDoc.flowId
    }
    const needUpdateDefaultValue = checkDefaultValue
    const updateAutoCalResultAttribute = true
    await updateAutoCalResult(
      'detail_',
      billData,
      fee,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      bus,
      undefined,
      changeValues,
      isTextChange,
      needUpdateDefaultValue,
      template,
      updateAutoCalResultAttribute
    )
    if (checkDefaultValue) {
      this.isFirstAutoCalFinished = true
    }
  }, 400)

  changeLinkDetailEntities = () => {
    const feeTypeForm = this.state.feeTypeForm || get(this.props, 'dataSource.feeTypeForm') || {}
    const value = fnLinkDetailEntitiesValue(feeTypeForm)
    this.setState({ feeTypeForm: value })
  }

  splicingDataLogic(currentIdx, currentDoc, autoCalFields, nextAndPr) {
    if (!currentDoc) return false
    let {
      baseDataProperties,
      flowRulePerformLogs,
      external,
      multiplePayeesMode,
      billSpecification,
      payPlanMode,
      fullDataSource
    } = this.props

    const riskWarning = external?.invoiceForm ? external : external && external[currentDoc.feeTypeForm.detailId]
    const currentResult = ((flowRulePerformLogs && flowRulePerformLogs.results) || []).filter(
      element => element.loc === currentIdx && element.dataFrom === 'details'
    )

    let { specificationId, feeTypeId, feeTypeForm = {} } = currentDoc
    let template = parseAsMeta(specificationId, baseDataProperties)
    template = parseAsReadOnlyFormTemplate(template)
    template = presetFormulaValue(currentResult, template, true)
    if (multiplePayeesMode && !payPlanMode) {
      const feeDetailPayee = template?.find(cp => cp.field === 'feeDetailPayeeId')
      const { components } = billSpecification
      const payeeId = feeDetailPayee
        ? feeDetailPayee
        : cloneDeep(toJS(components).find(item => item.field === 'payeeId'))
      const oldPayeeIdIdx = template.findIndex(item => item.field === 'payeeId')
      if (oldPayeeIdIdx > -1) template.splice(oldPayeeIdIdx, 1)
      payeeId.field = 'feeDetailPayeeId'
      payeeId.name = 'feeDetailPayeeId'
      payeeId.editable = false
      if (!feeDetailPayee) {
        template.push(payeeId)
      }
    }
    let { ordersData = [], thirdPartyOrders = [], linkDetailEntities = [] } = feeTypeForm
    let invoiceCard = ordersData.find(o => o && o.platform === 'fp')
    let thirdPartyOrder = thirdPartyOrders[0]
    let thirdOrder = ordersData.filter(o => o && o.platform !== 'fp')
    const extraRiskWarningList = getExtraRiskWarningList(template, riskWarning)
    const riskInTemplateList = getRiskInTemplate(template, riskWarning)
    if (linkDetailEntities && linkDetailEntities.length) {
      template.push({ name: 'linkDetailEntities', type: 'linkDetailEntities', showLabel: false })
    }
    const cValue = fnLinkDetailEntitiesValue(toJS(feeTypeForm))
    //对账单和结算单对字段的特殊处理
    const { type } = billSpecification
    if (['reconciliation', 'settlement'].includes(type)) {
      template.forEach(v => {
        //对账单时费用模版的结算单数据互联字段需要隐藏
        if (type === 'reconciliation' && v.field === 'supplierSettlement') {
          v.hide = true
        }
        //结算单时费用模版的对账单数据互联字段需要隐藏
        if (type === 'settlement' && v.field === 'supplierReconciliation') {
          v.hide = true
        }
      })
    }
    const { currentNodeShowFieldMap } = fnFlowShowFields(fullDataSource?.plan)
    const _template = []
    template.forEach(item => {
      if (currentNodeShowFieldMap[item.name]) {
        _template.push({ ...item, currentNodeShowField: true })
      } else if (this.state.waitCalculateFields.includes(item.name)) {
        const attrs = item?.configs || null || undefined
        if (attrs?.length) {
          let index = attrs.findIndex(el => el.ability === 'caculate' && el.property === 'hide')
          if (index === -1) {
            _template.push(item)
          }
        } else {
          _template.push(item)
        }
      } else {
        _template.push(item)
      }
    })
    this.setState({
      currentIdx,
      currentDoc,
      autoCalFields,
      nextAndPr,
      extraRiskWarningList,
      invoiceCard,
      thirdPartyOrder,
      thirdOrder,
      riskInTemplateList,
      template: _template,
      templateAll: template,
      feeTypeForm: cValue,
      feeTypeId,
      riskWarning
    })
  }

  handleSupplementaryInvoice = async invoiceData => {
    if (invoiceData.length > 0) {
      let { currentDoc, feeTypeId } = this.state
      await updateInvoiceDeduction([feeTypeId], invoiceData, true)
      let { flowId } = this.props
      let params = { flowId: flowId, detailId: currentDoc.feeTypeForm.detailId }
      let invoices = [],
        attachments = []
      invoiceData.forEach(item => {
        if (item.details) {
          let ids = []
          let taxAmount = this.invoiceTaxInfo.getTaxAmount(item)
          const taxRate = this.invoiceTaxInfo.getTaxRate(item)
          taxAmount = standardValueMoney(taxAmount)
          item.details.forEach(v => ids.push(v.id))
          invoices.push({
            itemIds: ids,
            invoiceId: item.master.id,
            taxAmount,
            taxRate
          })
          params.invoices = invoices
        } else {
          //附件
          attachments.push({
            key: item.key,
            fileId: item.id,
            fileName: item.fileName
          })
          params.attachments = attachments
        }
      })
      // 单据补开发票
      api.invokeService('@bills:save:invoice:wait', params).then(resp => {
        // 这个地方是补充发票后必走的地方，在此处主动发起发票风险的计算申请,
        api.invokeService('@bills:record:invoice:riskWarning', resp.id, params).then(() => {
          this.fnUpdateBillInfo(resp.id, params.detailId)
        })
      })
    }
  }

  handleConfirmInvoice = () => {
    let { flowId, dataSource } = this.props
    let param = { flowIds: [flowId], detailId: dataSource?.feeTypeForm?.detailId }
    this.getConfirmCheck(param)
  }

  getConfirmCheck = param => {
    api.invokeService('@invoice-manage:wait:invoice:confirmCheck', param).then(result => {
      if (result.value && result.value.message) {
        showModal.confirm({
          title: result.value.message,
          onOk: () => {
            this.handleConfirmResult()
          }
        })
      } else {
        this.handleConfirmResult()
      }
    })
  }

  handleConfirmResult = () => {
    let { flowId } = this.props
    let { currentDoc } = this.state
    let params = { flowId: flowId, detailId: currentDoc.feeTypeForm.detailId }
    api
      .invokeService('@invoice-manage:confirm:added:invoice', params)
      .then(() => this.fnUpdateBillInfo(flowId, currentDoc.feeTypeForm.detailId))
  }

  handleSupplementaryInvoiceEdit = () => {
    let { currentDoc } = this.state
    let { flowId } = this.props
    this.fnUpdateBillInfo(flowId, currentDoc.feeTypeForm.detailId)
  }

  handleDeleteInvoice = info => {
    let { currentDoc } = this.state
    let { flowId } = this.props
    let params = { flowId: flowId, detailId: currentDoc.feeTypeForm.detailId }
    const { invoice, attachment } = info
    if (invoice) {
      const { invoiceId } = invoice
      params.invoiceId = invoiceId
    } else if (attachment) {
      const { key } = attachment[0]
      params.attachmentKey = key
    }
    api.invokeService('@bills:delete:invoice:wait', params).then(resp => {
      this.fnUpdateBillInfo(resp.id, params.detailId)
      api.invokeService('@bills:record:invoice:riskWarning', resp.id, params)
    })
  }

  fnHandleDataLinkCardClick = ({ entityInfo, field, disabledStaff = false }) => {
    let { stackerManager, keel } = this.props
    const { ability = '', name = '' } = field
    if (ability === 'contractSettle') {
      disabledStaff = true
    }
    if (getTrueKey(name) === 'relationContract') {
      disabledStaff = true
    }
    if (stackerManager && stackerManager.values().find(v => v.key === 'DataLinkDetailModal')) {
      stackerManager.push('DataLinkDetailModal', {
        entityInfo: { ...entityInfo, entityId: field.referenceData },
        disabledStaff
      })
    } else if (keel && keel.dataset.values().find(v => v.key === 'DataLinkDetailModal')) {
      keel.open('DataLinkDetailModal', { entityInfo: { ...entityInfo, entityId: field.referenceData }, disabledStaff })
    } else {
      return api.open('@bills:DataLinkDetailModal', {
        entityInfo: { ...entityInfo, entityId: field.referenceData },
        showClose: true,
        disabledStaff
      })
    }
  }

  handleUpdateBillInfo = () => {
    let { flowId } = this.props
    let { currentDoc } = this.state
    this.fnUpdateBillInfo(flowId, currentDoc.feeTypeForm.detailId)
  }

  fnUpdateBillInfo = (flowId, detailId) => {
    const { bus } = this.props
    bus.emit('bills:detail:changed', { flowId, detailId })
  }

  handleAttachmentUploadSucc = fileList => {
    this.handleSupplementaryInvoice(fileList)
  }

  fnDetailsInputImportClick = () => {
    const { template } = this.state
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    return inputInvoiceImport.apply(this, [{ source: 'addDetails', isSupplyInvoice: true, importMode }])
  }

  fnDetailsImportOCRClick = (visibilityFeeTypes, isMedical, isOverseas, options) => {
    let { bus, submitterId } = this.props
    const { template } = this.state
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    return bus.getValue().then(feeTypeForm => {
      return api.open('@bills:ImportUploadOCRModal', { isMedical, isOverseas }).then(attachmentList => {
        return api.open('@bills:ImportInvoiceDetailModal', {
          visibilityFeeTypes,
          invoiceList: attachmentList,
          attachmentList,
          submitterId,
          isMedical,
          source: 'addDetails',
          isSupplyInvoice: true,
          isOcr: true, // 智能识别入口
          importType: isOverseas ? 'overseasInvoice' : 'ocr',
          bus,
          importMode,
          notShowModalIfAllInvoiceSuccess: !isMedical && options?.notShowModalIfAllInvoiceSuccess
        })
      })
    })
  }

  fnDetailsImportAliPayClick = () => {
    let { bus, submitterId } = this.props
    const { template } = this.state
    if (!submitterId) {
      submitterId = this.state.submitterId
    }
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    return bus.getValue().then(value => {
      return api.open('@bills:AliPayInvoiceListModal', { submitterId }).then(data => {
        return api.open('@bills:ImportInvoiceDetailModal', {
          invoiceList: data || [],
          source: 'addDetails',
          importMode,
          importType: 'alipay',
          submitterId,
          isSupplyInvoice: true
        })
      })
    })
  }

  fnDetailsAifapiaoImportClick = (feeTypes, invoiceList, data) => {
    const { bus } = this.props
    let { submitterId } = this.props
    const { template } = this.state
    if (!submitterId) {
      submitterId = this.state.submitterId
    }
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    return bus.getValue().then(value => {
      return api.open('@bills:ImportInvoiceDetailModal', {
        invoiceList: data || [],
        source: 'addDetails',
        importMode,
        importType: 'aifapiao',
        submitterId,
        isSupplyInvoice: true
      })
    })
  }

  handleUpdateDetail = ({ details, targetDetail, submitterId }) => {
    this.ds = details
    const { feeTypeForm = {} } = targetDetail
    const value = fnLinkDetailEntitiesValue(feeTypeForm)
    this.bus.setFieldsValue(value)
    this.setState({ currentDoc: targetDetail, feeTypeForm: value, submitterId: submitterId })
  }

  fnDetailsImportClick = () => {
    let { dataSource = {}, submitterId } = this.props
    const { template } = this.state
    let { id } = dataSource
    if (!submitterId) {
      submitterId = this.state.submitterId
    }
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    return this.bus.getValue().then(value => {
      let { details = [] } = value
      return api.open('@bills:ImportInvoiceModal', { submitterId, details, flowId: id, multiple: false }).then(data => {
        return api.open('@bills:ImportInvoiceDetailModal', {
          invoiceList: data.invoiceList || [],
          submitterId,
          source: 'addDetails',
          isSupplyInvoice: true,
          importType: 'pdf',
          importMode
        })
      })
    })
  }

  handlePrev = () => {
    this.setCurrentDoc(true)
  }

  handleNext = () => {
    this.setCurrentDoc(false)
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  setCurrentDoc(isPrev) {
    document.getElementsByClassName('feeDetail_forScroll')[0].scrollTop = 0
    let { currentIdx } = this.state
    let idx = isPrev ? currentIdx - 1 : currentIdx + 1
    let fee = this.ds[idx] || {}
    this.getSpeHideFields(fee)
    this._fnGetCalculateFields(idx, fee, true)
  }

  setCurrentDocForReview = idx => {
    document.getElementsByClassName('feeDetail_forScroll')[0].scrollTop = 0
    let fee = this.ds[idx] || {}
    this._fnGetCalculateFields(idx, fee, true)
  }

  renderBillBar() {
    const { showBillBar, onClickBillBar, billForm, submitterId } = this.props
    if (!showBillBar) {
      return null
    }
    return (
      <div className={styles.fee_detail_readonly_bill_bar}>
        <div className="label">{i18n.get('所属单据')}</div>
        <div className="title">
          <div className="name">
            <span onClick={onClickBillBar}>{billForm.title || i18n.get('[无标题]')}</span>
          </div>
          <Money withoutStyle className="money" value={billForm.payMoney} isShort />
        </div>
        <div className="intro">
          <div className="tags">
            <span className="item code">{billForm.code}</span>
            <span className="item submitter">
              {submitterId.name}
              {i18n.get('提交')}
            </span>
          </div>
          <div className="detail" onClick={onClickBillBar}>
            {i18n.get('查看详情')}
          </div>
        </div>
      </div>
    )
  }
  handleOldDiDiCardClick = thirdPartyOrder => {
    if (thirdPartyOrder.platform === 'DIDI') {
      api
        .invokeService('@bills:get:datalink:template:byId', { entityId: thirdPartyOrder?.id, type: 'CARD' })
        .then(resp => {
          const dataLink = get(resp, 'value.data.dataLink')
          const tripType = get(dataLink, 'entity.type', '')
          const data = get(resp, 'value.data')
          api.open('@bills:TripOrderPopup', {
            title: i18n.get('订单详情'),
            entityInfo: { ...data },
            tripType: tripType
          })
        })
    }
  }
  render() {
    const {
      hiddenFields,
      currentIdx,
      autoCalFields,
      nextAndPr,
      extraRiskWarningList,
      invoiceCard,
      thirdPartyOrder,
      thirdOrder,
      riskInTemplateList,
      template = [],
      feeTypeId = {},
      feeTypeForm = {},
      riskWarning = {},
      calValue,
      tagChangeCount
    } = this.state
    const {
      suppleInvoiceBtn,
      source,
      submitterId,
      stackerManager,
      isForbid,
      isInHistory,
      flowId,
      ownerId,
      keel,
      isPopup,
      bus,
      fullDataSource,
      billSpecification,
      allowAddTag, // 是否允许明细可以添加标签,
      showPreAndNext = true, //是否显示上一页下一页
      updateDetailsCallBack, // 明细列表回调
      smartApproveCharge, // 智能审批charge开关
      ...others
    } = this.props
    const { dataSource = {} } = this.props
    const { apportionVisibleList = [], showAllFeeType } = dataSource
    const dataSourceC = this.formatOverseasInvoiceBytDimension(dataSource)
    let detailId = feeTypeForm.detailId
    const feeAmount = feeTypeForm ? feeTypeForm.amount : {}
    // const top = this.props.stackerManager ? styles['fee-detail-readonly-top-none'] :
    // styles['fee-detail-readonly-top'] const position = needPosition && !window.isNewHome ?
    // styles.fee_detail_readonly_position : styles.fee_detail_readonly_position_none 修改发票字段 label
    template.some(item => {
      const isInvoiceForm = item.field === 'invoiceForm'
      if (isInvoiceForm) {
        item.labelView = () => null
        item.noColon = true
      }
      return isInvoiceForm
    })
    // 隐藏字段公式计算后符合条件则隐藏
    const calFields = Object.keys(calValue)
    const { currentNodeShowFieldMap } = fnFlowShowFields(fullDataSource?.plan)
    calFields.forEach(field => {
      if (currentNodeShowFieldMap[field]) {
        return
      }
      let index = template.findIndex(el => el.field === field)
      if (index !== -1) {
        template[index] = { ...template[index], ...calValue[field] }
        // let attrs = calValue[field]
        // let autoHide = attrs?.attributeHide
        // if (!autoHide) {
        //   template[index] = { ...template[index], ...calValue[field] }
        // } else {
        //   template.splice(index, 1)
        // }
      }
    })

    //是否设置了审批流字段隐藏
    const flowHiddenFields = fnFlowHideFields(fullDataSource?.plan)
    flowHiddenFields?.length > 0 &&
      flowHiddenFields.forEach(v => {
        if (currentNodeShowFieldMap[v]) {
          return
        }
        let index = template.findIndex(el => el.name === v)
        if (index !== -1) {
          template.splice(index, 1)
        }
      })
    const feeDetail = document.getElementById('bills-FeeDetailReadonly')
    const param = { ...others }
    if (feeDetail) {
      param.offsetWidth = feeDetail.offsetWidth
    }

    const { groupTemplate, isGroup } = splitTemplateToGroups(template)
    const detailNo = get(feeTypeForm, 'detailNo', '')
    return (
      <div id={'bills-FeeDetailReadonly'} className={`${styles['fee-detail-readonly-wrapper']} h-100b`}>
        <div className={`${styles['fee-detail-readonly']}`}>
          <div className="content feeDetail_forScroll">
            <div className="group-item">
              {this.renderBillBar()}
              <FeeItem
                inFeeTypeMode={allowAddTag}
                detailId={detailId}
                feeTypeId={feeTypeId}
                amount={feeTypeForm.amount}
                detailNo={detailNo}
                tagChangeCount={tagChangeCount}
                smartApproveCharge={smartApproveCharge}
                modalOffsetWidth={param?.offsetWidth}
              />
              {!riskInTemplateList?.length && !extraRiskWarningList?.length ? null : (
                <div className="risk-content">
                  <WarningOrErrorTips
                    riskInTemplateList={riskInTemplateList}
                    extraRiskWarningList={extraRiskWarningList}
                    isForbid={isForbid}
                    isFeeDetail={true}
                  />
                </div>
              )}
            </div>
            <div className={!isGroup ? 'group-item' : ''}>
              <DynamicReadonly
                {...param}
                dataSource={dataSourceC}
                hiddenFields={hiddenFields}
                keel={keel}
                isPopup={isPopup}
                stackerManager={stackerManager}
                bus={this.bus}
                autoCalFields={autoCalFields}
                layout="horizontal"
                template={template}
                groupTemplate={groupTemplate}
                value={feeTypeForm}
                source={source}
                suppleInvoiceBtn={suppleInvoiceBtn}
                submitterId={submitterId}
                ownerId={ownerId}
                riskWarning={riskWarning}
                nextAndPr={nextAndPr}
                isForbid={isForbid}
                detailId={detailId}
                flowId={flowId}
                isInHistory={isInHistory}
                isDetail={true}
                feeAmount={feeAmount}
                apportionVisibleList={apportionVisibleList}
                showAllFeeType={showAllFeeType}
                isFeeDetail={false}
                classNameGroup="group-item"
                isGroup={isGroup}
                billSpecification={billSpecification}
              />
            </div>
            <div
              className={invoiceCard || (thirdOrder && thirdOrder.length > 0) || thirdPartyOrder ? 'group-item' : ''}
            >
              {invoiceCard && (
                <Row className={styles['third-card-readOnly']}>
                  <Col span={24} className="label">
                    {i18n.get('发票信息')}
                  </Col>
                  <Col span={20}>
                    <div className="card_wrapper">
                      <InvoiceCard invoice={invoiceCard} />
                    </div>
                  </Col>
                </Row>
              )}
              {thirdOrder && thirdOrder.length > 0 && (
                <Row className={styles['third-card-readOnly']}>
                  <Col span={24} className="label">
                    {i18n.get('订单信息')}
                  </Col>
                  {thirdOrder.map(item => (
                    <Col span={20}>
                      <div className="card_wrapper">
                        <ThirdCard orders={item} />
                      </div>
                    </Col>
                  ))}
                </Row>
              )}
              {thirdPartyOrder && (
                <Row className={styles['third-card-readOnly']}>
                  <Col span={24} className="label">
                    {i18n.get('订单信息')}
                  </Col>
                  <Col span={20}>
                    <div className="card_wrapper" onClick={() => this.handleOldDiDiCardClick(thirdPartyOrder)}>
                      <ThirdPartyCard
                        dataSource={thirdPartyOrder}
                        iconFont={thirdPartyOrder.platform === 'DIDI' ? svg : null}
                      />
                    </div>
                  </Col>
                </Row>
              )}
            </div>
          </div>
        </div>
        <FeeDetailBottom
          bus={this.bus}
          currentIdx={currentIdx}
          detailId={detailId}
          count={this.ds.length}
          allowAddTag={allowAddTag}
          dataSource={this.ds}
          billSpecification={billSpecification}
          showPreAndNext={showPreAndNext}
          fullDataSource={fullDataSource}
          updateDetailsCallBack={updateDetailsCallBack}
        />
      </div>
    )
  }
}
