import React, { PureComponent } from 'react'
import { Form, Row, Col } from 'antd'
import { readonly } from '../../../components'
import classNames from 'classnames'
import { Dynamic } from '@ekuaibao/template'
import Loading from '@ekuaibao/loading'
import { app as api } from '@ekuaibao/whispered'
import { setFieldsExternal } from '../riskWarning/formatRiskWarningData'
import styles from './dynamicReadonly.module.less'
import { getLayoutConfig } from '../../../elements/layoutBtnGroups/utils'

const dynamicColWhiteMap = {
  payeeInfo(value = {}) {
    return value.multiplePayeesMode ? undefined : 24
  }
}

const colWhiteMap = {
  separator: 24,
  details: 24,
  requisitionDetails: 24,
  trips: 24,
  attachments: 24,
  aiAttachments: 24,
  expenseLink: 24,
  expenseLinks: 24,
  linkRequisitionInfo: 24,
  apportions: 24,
  annotation: 24,
  dataLink: 24,
  amortizes: 24,
  invoice: 24,
  linkDetailEntities: 24,
  dataLinkEdits: 24,
  splitCalculation: 24,
  engineConnect: 24,
  engineBlockUI: 24,
  supplier: 24,
  travel: 24,
  group: 24,
  budgetAdjustDetails: 24,
  corporateExpenseCardForm: 24,
  widget:24,
}

// 用于单据布局调整字号
const fontSizeMap = {
  small: 'billInfo_Small',
  standard: 'billInfo_Standard',
  big: 'billInfo_Big'
}

const fieldWhiteMap = {
  separator: 'separator',
  details: 'details',
  requisitionDetails: 'requisitionDetails',
  trips: 'trips',
  attachments: 'attachments',
  aiAttachments: 'aiAttachments',
  expenseLink: 'expenseLink',
  expenseLinks: 'expenseLinks',
  linkRequisitionInfo: 'linkRequisitionInfo',
  apportions: 'apportions',
  amortizes: 'amortizes',
  annotation: 'annotation',
  dataLink: 'dataLink',
  invoice: 'invoice',
  dataLinkEdits: 'dataLinkEdits',
  splitCalculation: 'splitCalculation',
  supplier: 'supplier',
  travel: 'travel'
}

const layoutColCountMap = {
  1: 24,
  2: 12,
  3: 8
}

const layoutSizeMap = {
  small: { min: 320, max: 768, colCount: 2 },
  middle: { min: 768, max: 1280, colCount: 3 }
}

const isWx = window.__PLANTFORM__ === 'WEIXIN'

export default class DynamicReadonly extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { layoutBtnGroupMap: {} }
  }
  componentDidMount() {
    if (this.props.riskWarning) {
      const props = this.props
      setTimeout(() => {
        setFieldsExternal(props)
      }, 0)
    }
  }

  componentWillReceiveProps(np) {
    if (this.props.riskWarning !== np.riskWarning) {
      setTimeout(() => {
        setFieldsExternal(np)
      }, 0)
    }
  }

  groupWrapper = (props, components, index) => {
    const { classNameGroup, isGroup, value } = this.props
    let rowChildren = []; // 用于收集当前行的 Col 组件
    const rows = []; // 用于收集所有的 Row 组件

    const { colCount } = this.getCurrentLayoutConfig()
    const wrapperWith = document.querySelector('.bill-dynamic-readonly-wrapper')?.clientWidth

    let percent2Width = 0
    if (wrapperWith > 0) {
      percent2Width = wrapperWith * 0.015
    }

    for(let idx = 0; idx < components.length; idx++) {
      const c = components[idx]
      const { props } = c;
      const { field = {} } = props;
      let defaultSpan = colWhiteMap[field.type] || colWhiteMap[field.name]
      if (dynamicColWhiteMap[field.type]) {
        defaultSpan = dynamicColWhiteMap[field.type](value)
      } else if (dynamicColWhiteMap[field.name]) {
        defaultSpan = dynamicColWhiteMap[field.name](value)
      }
      const span = defaultSpan ? defaultSpan : layoutColCountMap[colCount]
      const isLast = idx === components.length - 1
      const isTakingOneRow = span === 24
      if (!isTakingOneRow) {
        rowChildren.push(<Col span={span}>{c}</Col>)
      }
      if (isTakingOneRow) {
        rows.push(
          <Row key={idx} align="middle" gutter={ rowChildren.length === 1 ? 0 : 16 + percent2Width}  className={classNames('form-list-item')}>
            {rowChildren}
          </Row>
        )
        rowChildren = []
        rows.push(
          <Row key={idx} align="middle" gutter={ rowChildren.length === 1 ? 0 : 16 + percent2Width}  className={classNames('form-list-item', { 'form-list-item-one-row': true })}>
            <Col span={span}>{c}</Col>
          </Row>
        )
      } else if (Number.isInteger((idx+1)/colCount) || (isLast && rowChildren.length)) {
        rows.push(
          <Row key={idx} align="middle" gutter={ rowChildren.length === 1 ? 0 : 16 + percent2Width}  className={classNames('form-list-item')}>
            {rowChildren}
          </Row>
        );
        rowChildren = []
      }
    }

    return (
      <div key={index} className={isGroup ? classNameGroup : ''}>
          {rows}
      </div>
    )
  }

  arrayConvertToMusilArray = (template, layoutColCount = 2) => {
    let arr = []
    const newTemplate = []
    let num = 0
    const { value = {} } = this.props
    for (let idx = 0; idx <= template.length - 1; idx++) {
      const item = template[idx]
      if (fieldWhiteMap[item.type] || (item.type === 'payeeInfo' && !value.multiplePayeesMode) || item.hidden) {
        arr.length > 0 && newTemplate.push(arr)
        newTemplate.push(new Array(item))
        arr = new Array()
        num = 0
        continue
      }
      if ((arr.length === layoutColCount && num % layoutColCount === 0) || idx === 0) {
        if (idx != 0) {
          newTemplate.push(arr)
          arr = new Array()
        }
        arr.push(item)
        num++
      } else {
        arr.push(item)
        num++
      }
      if (idx === template.length - 1) {
        newTemplate.push(arr)
      }
    }
    return newTemplate
  }

  getCurrentLayoutConfig = () => {
    const me = api.getState()['@common'].userinfo.staff
    const res = getLayoutConfig(me.id + 'layoutBtnGroup')
    return {
      layoutMode: res['layout-mode'],
      formFontSize: res['form-font-size'],
      colCount: res['form-col-count'],
    }
  }

  render() {
    let {
      bus,
      template,
      value,
      tags,
      layout,
      flowRulePerformLogs,
      autoCalFields,
      baseDataProperties,
      flowId,
      suppleInvoiceBtn,
      source,
      submitterId,
      keel,
      stackerManager,
      isPopup,
      isForbid,
      isInHistory,
      dataSource,
      ownerId,
      detailId,
      offsetWidth,
      size,
      isFeeDetail = false,
      isGroup,
      groupTemplate,
      ...others
    } = this.props

    const {
      layoutMode,
      formFontSize,
      colCount
    } = this.getCurrentLayoutConfig()

    if (!isFeeDetail) {
      template = this.arrayConvertToMusilArray(template, Number(colCount))
      groupTemplate = isGroup ? groupTemplate : this.arrayConvertToMusilArray(groupTemplate, Number(colCount))
    }

    const wrapClassKey = formFontSize ? fontSizeMap[formFontSize] : ''
    return (
      <Dynamic
        {...others}
        className={classNames(
          'bill-dynamic-readonly-wrapper',
          styles[wrapClassKey],
          `bill-field-font-size-${formFontSize ?? 'standard'}`,
          `bill-field-display-mode-${layoutMode}`,
          {[`bill-field-display-col-${colCount}`]: colCount !== undefined }
        )}
        offsetWidth={offsetWidth}
        groupWrapper={this.groupWrapper}
        keel={keel}
        isPopup={isPopup}
        stackerManager={stackerManager}
        autoCalFields={autoCalFields}
        bus={bus}
        create={T => Form.create()(T)}
        layout={layoutMode}
        flowRulePerformLogs={flowRulePerformLogs}
        loading={Loading}
        template={template}
        groupTemplate={groupTemplate}
        value={value}
        tags={tags}
        state={dataSource.state}
        ownerId={ownerId}
        elements={readonly}
        baseDataProperties={baseDataProperties}
        flowId={flowId}
        detailId={detailId}
        source={source}
        suppleInvoiceBtn={suppleInvoiceBtn}
        submitterId={submitterId}
        isEdit={false}
        isForbid={isForbid}
        isInHistory={isInHistory}
        multiplePayeesMode={value?.multiplePayeesMode}
        payPlanMode={value?.payPlanMode}
        payeePayPlan={value?.payeePayPlan}
        noPayInfo={!value?.payPlan?.length && !value?.payeeId}
        detailData={value}
        dataSource={dataSource}
      />
    )
  }
}
