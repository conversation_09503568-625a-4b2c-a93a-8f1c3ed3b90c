import React, { useEffect, useMemo, useRef, useState } from 'react'
import './index.less'
import { Button, Dropdown, message, SkeletonParagraph, Tooltip } from '@hose/eui'
import { FilledDirectionExpandDown, FilledDirectionExpandUp, OutlinedGeneralChange } from '@hose/eui-icons'
import { app } from '@ekuaibao/whispered'
import { SelectInfo } from '@hose/eui-rc-menu/lib/interface'
import { ItemType } from '@hose/eui/es/components/menu/hooks/useItems'
import FlowPlanContent from './FlowPlanContent'
import { StaffIF } from '@ekuaibao/ekuaibao_types'
import FlowHistoryModifyView from './FlowHistoryModifyView'
import { IFlowLog, IPlan } from './types'
import { HistoryApi } from '../../../../history/api'
import { getBillHistoryVersionList } from '../../../../bills.action'
import MessageCenter from '@ekuaibao/messagecenter'
import { logEvent } from '../../../../../../lib/logs'
import { debounce } from 'lodash'
import { fetchNodesAIAgentMap } from '../../../../../../elements/ai-agent-utils'

interface Props {
  flowInfo: any
  dynamicChannelMap: Record<string, any>
  staffDisplayConfigField: any
  userInfo: StaffIF
  isEditConfig?: boolean
  bus: MessageCenter
  filterType?: string
  versionItemAction: (item: IFlowLog) => void
  canDeleteComment?: boolean
  privilegeId?: string
  staffMap?: Record<string, StaffIF>
}

export const FlowPlanHistory: React.FC<Props> = props => {
  const {
    flowInfo,
    userInfo,
    dynamicChannelMap,
    staffDisplayConfigField,
    isEditConfig,
    bus,
    filterType: defaultFilterType,
    versionItemAction,
    canDeleteComment = false,
    privilegeId = '',
    staffMap
  } = props
  const flowId = flowInfo?.id
  const [filterType, setFilterType] = useState<string>(defaultFilterType)
  const [showModifyView, setShowModifyView] = useState<boolean>(false)
  const [flowLog, setFlowLog] = useState<IFlowLog>()
  const flowPlanHistoryRef = useRef<HTMLDivElement>(null)
  const [planNodes, setPlanNodes] = useState<IPlan[]>([])
  const [loading, setLoading] = useState(false)
  const [nodesAIAgentMap, setNodesAIAgentMap] = useState<any>({})

  useEffect(() => {
    bus.on('flow:plan:history:loading', handleSetFlowPlanHistoryLoading)
    bus.on('flow:plan:history:view:log:by:type', handleViewFlowPlanHistoryLog)
    logEvent('view_flow_plan_history', { title: '查看侧边栏审批流程' })
    return () => {
      bus.un('flow:plan:history:loading', handleSetFlowPlanHistoryLoading)
      bus.un('flow:plan:history:view:log:by:type', handleViewFlowPlanHistoryLog)
    }
  }, [])

  const handleScrollToCurrentNode = debounce(() => {
    const currentApprovingNode = document.getElementsByClassName("current-approving")[0];
    const parentNode = document.getElementById('FlowPlanHistoryLog');
    if (!currentApprovingNode || !parentNode) return;

    const { offsetTop: nodeTop, offsetHeight: nodeHeight } = currentApprovingNode;
    const parentNodeVisibleHeight = parentNode.getBoundingClientRect().height;

    parentNode.scrollTop = nodeTop - parentNodeVisibleHeight / 2 + nodeHeight / 2;
  }, 10);

  useEffect(() => {
    handleScrollToCurrentNode()
  }, [flowInfo?.logs?.length])

  useEffect(() => {
    if (flowInfo?.plan?.id?.length) {
      app.invokeService('@bills:get:flow:plan:version:by:id', { flowPlanId: flowInfo.plan.id }).then(({ items }) => {
        const planNodes = items.map(item => item.flowPlan)
        // 追加当前的流程节点
        planNodes.push(flowInfo.plan)
        setPlanNodes(planNodes)
      })
    }
  }, [flowInfo])

  useEffect(() => {
    (async () => {
      const map = await fetchNodesAIAgentMap(nodesAIAgentMap, flowInfo?.plan?.nodes, flowInfo?.logs)
      setNodesAIAgentMap(map)
    })()
  }, [flowInfo])

  const handleSetFlowPlanHistoryLoading = (loading: boolean) => {
    setLoading(loading)
    setTimeout(() => {
      setShowModifyView(false)
      setLoading(false)
    }, 500)
  }

  const handleViewFlowPlanHistoryLog = (filterType: string) => {
    handleFilterChange(filterType)
    handleSetFlowPlanHistoryLoading(true)
  }

  const handleFilterChange = (filterType: string) => {
    setFilterType(filterType)
  }

  const handleViewModifyClick = async (item: IFlowLog) => {
    const isHistoryVersion = await HistoryApi.getFlowVersionModifyState(item.flowVersionedId)
    if (isHistoryVersion?.value) {
      versionItemAction && versionItemAction(item)
      return
    }
    setFlowLog(item)
    setShowModifyView(true)
  }

  const handleModifyViewBack = () => {
    setShowModifyView(false)
  }

  if (loading) {
    return <SkeletonParagraph className="bill-more-info-skeleton" lineCount={13} />
  }

  return (
    <>
      <div
        id="FlowPlanHistoryLog"
        style={{ display: showModifyView ? 'none' : 'flex' }}
        ref={flowPlanHistoryRef}
        className="flow-plan-history-wrapper"
      >
        <FlowPlanHistoryTop
          filterType={filterType}
          flowId={flowId}
          flowInfo={flowInfo}
          onFilterChange={handleFilterChange}
        />
        <div className="flow-plan-history-wrapper-content">
          <FlowPlanContent
            flowInfo={flowInfo}
            userInfo={userInfo}
            staffMap={staffMap}
            dynamicChannelMap={dynamicChannelMap}
            staffDisplayConfigField={staffDisplayConfigField}
            isEditConfig={isEditConfig}
            filterType={filterType}
            planNodes={planNodes}
            onViewModifyClick={handleViewModifyClick}
            canDeleteComment={canDeleteComment}
            privilegeId={privilegeId}
            bus={bus}
            nodesAIAgentMap={nodesAIAgentMap}
          />
        </div>
      </div>
      {showModifyView ? <FlowHistoryModifyView flowLog={flowLog} onBack={handleModifyViewBack} /> : null}
    </>
  )
}

interface FlowPlanHistoryTopProps {
  flowInfo: any
  flowId: string
  filterType: string
  onFilterChange: (filterType: string) => void
}

type MenuItem = ItemType & { value: string }

export const FlowPlanHistoryTop: React.FC<FlowPlanHistoryTopProps> = props => {
  const defaultItem = { label: i18n.get('全部'), key: 'all', value: 'ALL' }
  const items: Array<ItemType & { value: string }> = [
    defaultItem,
    { label: i18n.get('仅包含评论事件'), key: 'comment', value: 'CONTAIN_COMMENT' },
    { label: i18n.get('仅包含审批事件'), key: 'approve', value: 'APPROVAL_EVENT' },
    { label: i18n.get('仅包含人员审批事件'), key: 'staff_approval', value: 'STAFF_APPROVAL' },
    { label: i18n.get('仅包含有效审批事件'), key: 'effective', value: 'EFFECTIVE_APPROVAL' }
  ]
  const { flowInfo, flowId, filterType, onFilterChange } = props
  const [data, setData] = useState<MenuItem[]>(items)
  const [sort, setSort] = useState(false)
  const [currentSort, setCurrentSort] = useState<MenuItem>(defaultItem)
  const [versionList, setVersionList] = useState()

  useEffect(() => {
    const detailsLayoutPower = app.getState()['@common']?.powers.KA_DETAILS_LAYOUT // KA-单据详情展示
    if (detailsLayoutPower) {
      const specificationId = flowInfo?.form?.specificationId?.id
      if (!specificationId) {
        return
      }
      app.invokeService('@bills:get:layoutConfig', { type: 'ARRANGE', specificationId }).then(layoutConfig => {
        const approvalHistoryList = layoutConfig?.value?.configDetail?.approvalHistoryList
        let data = items
        if (approvalHistoryList && approvalHistoryList.length) {
          data = data.filter(menuItem => !!~approvalHistoryList.indexOf(menuItem.value))
        }
        const approvalHistory = layoutConfig?.value?.configDetail?.approvalHistory
        const defaultSelectedMenu = items.find(v => v.value === approvalHistory)
        setData(data)
        const currentSort = defaultSelectedMenu || defaultItem
        setCurrentSort(currentSort)
        onFilterChange(currentSort.key as string)
      })
    } else {
      const defaultSelectedMenu = items.find(v => v.key === filterType)
      const currentSort = defaultSelectedMenu || defaultItem
      setCurrentSort(currentSort)
    }
  }, [])

  const hasHistoryVersion = useMemo(() => {
    const versionConfig = app.getState('@common.specificationVersionConfig')
    if (versionConfig && versionConfig.hideVersion) {
      return false
    }
    const allSubmitLogs = flowInfo?.logs?.filter(item => item.action === 'freeflow.submit')
    if (allSubmitLogs.length > 0) {
      const lastSubmitLog = allSubmitLogs[allSubmitLogs.length - 1]
      const item = lastSubmitLog.modifyFlowLog?.length > 0 ? lastSubmitLog.modifyFlowLog[0] : null
      return !!item
    }
    return false
  }, [flowInfo?.logs])

  const handleOpenChange = (open: boolean) => {
    setSort(open)
  }

  const handleMenuSelect = (info: SelectInfo) => {
    const current = items.find(item => item.key === info.key)
    setCurrentSort(current)
    setSort(false)
    onFilterChange(info.key)
  }

  const handleOpenHistory = async () => {
    let currentVersionList: any[] = versionList
    if (!currentVersionList) {
      const res = await getBillHistoryVersionList(flowId)
      if (res.items) {
        currentVersionList = res.items
        setVersionList(res.items)
      }
    }

    if (currentVersionList?.length > 0) {
      app.open('@bills:BillHistoryVersionModal', {
        flowId,
        item: { flowVersionedId: currentVersionList[0]?.id },
        viewFlowState: flowInfo?.state
      })
    } else {
      message.info(i18n.get('暂无历史版本'))
    }
  }

  const HiddenFlowHistory = app.getState()['@common'].powers.HiddenFlowHistory

  return (
    <div className="flow-plan-history-top">
      <Dropdown
        trigger={['click']}
        menu={{ items: data, onClick: handleMenuSelect }}
        placement="bottom"
        onOpenChange={handleOpenChange}
      >
        <div className="left">
          <div className="left-text">{(currentSort as any).label || i18n.get('全部')}</div>
          {sort ? <FilledDirectionExpandUp fontSize={12} /> : <FilledDirectionExpandDown fontSize={12} />}
        </div>
      </Dropdown>
      {hasHistoryVersion && !HiddenFlowHistory && (
        <div className="right">
          <Button category="text" onClick={handleOpenHistory}>
            <OutlinedGeneralChange fontSize={14} onClick={handleOpenHistory} />
            <span className='text'>{i18n.get('历史版本')}</span>
          </Button>
        </div>
      )}
    </div>
  )
}

type FlowPlanVersionType = {
  flowInfo: any
}

export const FlowPlanVersion: React.FC<FlowPlanVersionType> = props => {
  const { flowInfo } = props
  const [isShowVersion, setIsShowVersion] = useState(false)
  const [flowPlanConfig, setFlowPlanConfig] = useState<any>()

  useEffect(() => {
    if (!!flowInfo?.plan?.flowPlanConfigId?.length) {
      app
        .invokeService('@bills:get:flow:config:by:id', {
          id: flowInfo.plan.flowPlanConfigId
        })
        .then(({ value }) => {
          setFlowPlanConfig(value)
          setIsShowVersion(!!flowInfo.plan.configVersion)
        })
    }
  }, [flowInfo?.plan?.flowPlanConfigId])

  const flowVersion = useMemo(() => {
    if (!flowPlanConfig?.version) {
      return ''
    }
    let versionTxt = flowPlanConfig?.name
    if (isShowVersion) {
      versionTxt = `${versionTxt}:v${Number(flowInfo?.plan?.configVersion).toFixed(1)}`
    }
    return versionTxt
  }, [flowPlanConfig, isShowVersion])

  if (!isShowVersion) {
    return null
  }

  return <div className="flow-plan-version">{flowVersion}</div>
}

export default FlowPlanHistory
