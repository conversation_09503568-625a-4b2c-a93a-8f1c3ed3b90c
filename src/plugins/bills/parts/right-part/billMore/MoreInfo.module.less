@import '~@eku<PERSON>bao/eui-styles/less/token-mobile.less';

:global {
  .eui-tabs-nav-wrap {
    border-radius: 8px 8px 0px 0px;
    border: 2px solid var(--eui-bg-body, #FFF);
    border-bottom: none;
  }
}

.bill-more-info-container {
  border-radius: 8px;
  margin-left: 8px;


  :global {
    .bill-more-tabs {
      padding-top: 0 !important;
    }

    .eui-tabs-nav-wrap {
      padding-top: 12px;
      border-radius: 8px 8px 0px 0px;
      border-top: 2px solid var(--eui-bg-body, #FFF);
      border-right: 2px solid var(--eui-bg-body, #FFF);
      border-left: 2px solid var(--eui-bg-body, #FFF);
      background: linear-gradient(180deg, #E2F0FF 0%, #F6FAFF 100%);

    }
  }
}


.content {
  display: flex;
  width: 100%;
  &.bill-more-info-expanded {
    .collapse-content {
      width: 0;
      overflow: hidden;
    }
  }
  &.bill-more-info-collapsed {
    :global{
      .bill-more-tabs {
        width: 0;
        overflow: hidden;
      }
    }
  }
}

.bill-more-info,
.bill-more-hide-info {
  height: 100%;
  position: relative;
  border-left: 1px solid var(--eui-line-divider-module);
  display: flex;
  flex: 0 0 auto; // 改为auto，让Resizable组件控制宽度
  // 为展开/收起添加快速过渡效果
  transition: width 0.15s ease-out, opacity 0.1s ease-out;

  &.is-dragging {
    transition: width 0.2s ease-out; // 只对宽度添加更快的过渡效果
  }

  :global {
    .show-btn {
      position: absolute;
      top: 50%;
      left: -12px;
      z-index: 998;
      width: 24px;
      height: 48px;
      border: 1px solid var(--eui-line-divider-default);
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      border-radius: 6px;
      cursor: pointer;
      &:hover {
        background-color: var(--eui-bg-base);
      }

      span {
        color: var(--eui-icon-n2);
      }
    }
    .eui-tabs-nav-list {
      transition: none !important;
    }
    .bill-more-info-skeleton {
      padding: 12px 16px 16px;
      width: 100%;
      // 确保鱼骨屏在400px宽度下显示合理
    }

    .bill-more-tabs {
      width: 100%;
      padding-top: 12px;
    }
    .single-tab-content {
      width: 100%;
      height: 100%;
      padding-top: 12px;
      background-color: #fff;
      border-radius: 8px;
      overflow: auto;
    }
    //.eui-tabs > .eui-tabs-nav .eui-tabs-nav-wrap {
    //  padding: 0;
    //}
    .eui-tabs .eui-tabs-content-holder {
      background-color: #fff;
    }

    .eui-tabs-top>.eui-tabs-nav {
      margin-bottom: 0;
    }

    .eui-tabs-top>.eui-tabs-nav::before {
      border-bottom: 1px solid var(--eui-line-divider-default);
    }

    .eui-tabs .eui-tabs-content-holder {
      overflow: auto;
      height: 100%;
      border-radius: 8px;
    }

    .eui-tabs .eui-tabs-content-holder .eui-tabs-content {
      height: 100%;

      .eui-tabs-tabpane-active {
        height: 100%;
        overflow: hidden;
      }
    }
  }
}

.bill-more-info-lang-zh {
  .bill-more-hide-info {
    max-width: 48px !important;
  }
}

.bill-more-info-lang-others {
  .bill-more-hide-info {
    max-width: 72px !important;
  }
}

.bill-more-hide-info {
  padding: 8px;
  flex: 0 0 10px !important;
  display: flex !important;

  :global {
    .eui-tabs-content-holder {
      display: none;
    }
  }
}

.bill-more-info {
  border-radius: 8px;
  background-color: var(--eui-bg-body);
  border-left-color: transparent;
}

.resizable-custom-handle {
  // 扩大热区到整个8px缝隙区域
  width: 8px !important;
  margin-left: -8px; // 覆盖8px的margin间距
  cursor: col-resize;
  position: relative;
  z-index: 10;
  left: 0px !important;
  // 默认状态下不显示任何内容，保持透明
  background: transparent;

  // 悬停时显示拖拽指示线
  &:hover, &:active {
    &::after {
      opacity: 1;
    }
  }

  // 伪元素 - 蓝线居中显示在8px缝隙的中心
  &::after {
    content: '';
    position: absolute;
    left: 3px; // 在8px宽度的热区中心位置（8/2 - 1 = 3px）
    top: 0;
    bottom: 0;
    width: 2px;
  }

  &:hover,
  &:active {
    background: var(--eui-primary-pri-500);
    transition: opacity 0.2s ease-in-out;
    border-left: 3px solid var(--eui-bg-base);
    border-right: 3px solid var(--eui-bg-base);
    box-sizing: border-box;
  }

}

.bill-more-hide-info {
  background-color: var(--eui-bg-body);
  border-radius: 8px;
}

.collapse-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.collapse-content-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 8px;
  &:first-child {
    margin-top: 0;
  }
  .collapse-inner {
    display: flex;
    justify-content: center;
    flex-direction: column;
    .icon {
      color: var(--eui-icon-n1);
    }
    .text {
      word-break: keep-all;
      color: var(--eui-text-title);
      font: var(--eui-font-note-r2);
    }
  }
}

.bill-more-hide-info {
  background-color: var(--eui-bg-body);
  border-radius: 8px;
}

.collapse-content {
  display: flex;
  flex-direction: column;
}

.collapse-content-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 8px;
  &:first-child {
    margin-top: 0;
  }
  .collapse-inner {
    display: flex;
    justify-content: center;
    flex-direction: column;
    .icon {
      color: var(--eui-icon-n1);
    }
    .text {
      word-break: keep-all;
      color: var(--eui-text-title);
      font: var(--eui-font-note-r2);
    }
  }
}


.ai-chat-container-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.ai-chat-container {
  height: 50px;
  border-radius: 8px 8px 0px 0px;
  border-top: 2px solid var(--eui-bg-body, #FFF);
  border-right: 2px solid var(--eui-bg-body, #FFF);
  border-left: 2px solid var(--eui-bg-body, #FFF);
  background: linear-gradient(180deg, #E2F0FF 0%, #F6FAFF 100%);
  color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
  font: var(--eui-font-head-b1);
  padding: 0 16px;
  display: flex;
  align-items: center;
}