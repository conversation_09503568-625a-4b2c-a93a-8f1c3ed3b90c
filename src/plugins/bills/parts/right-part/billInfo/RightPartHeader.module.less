/**************************************************
 * Created by nanyuantingfeng on 11/07/2017 14:21.
 **************************************************/
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import "~@ekuaibao/eui-styles/less/token";

.right_part_header {
  width: 100%;
  // border-radius: 4px;
  background-color: var(--eui-bg-body);
  position: relative;
  // padding: 0px 24px 12px 24px;
  flex-shrink: 0;
  padding: 16px;
  border-bottom: 1px solid var(--eui-line-divider-default);

}


.right_part_header_draft {
  width: 100%;
  border-radius: 4px 4px 0 0;
  padding: 16px 24px 12px 24px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  border-bottom: 1px solid var(--eui-line-divider-default);
  .draft-code {
    color: var(--eui-text-caption);
    font: var(--eui-font-body-r1);
  }
  :global{
    .navigation-bar{
      padding: 0;
      .navigation-bar-title-wrapper{
        align-items: center;
        .navigation-bar-title-content{
          font-size: 16px;
          // width: 100%;
          // padding-right: 70px;
        }
      }
    }
  }
}

.title_wrap {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  :global{
    .icon-wrapper {
      width: 32px;
      height: 32px;
      font-size: 16px;
      border: 1px solid @color-line-2;
      border-radius: @radius-2;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      &:hover {
        border-color: @color-brand-2;
        color: @color-brand-2;
      }
    }
  }

}
.title {
  max-width: 90%;
  padding: 8px 0 8px 0;
  white-space: nowrap;
  // padding-right: 70px;
  margin-right: 10px;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 20px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :global{
    .icon-wrapper {
      width: 32px;
      height: 32px;
      font-size: 16px;
      border: 1px solid @color-line-2;
      border-radius: @radius-2;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      &:hover {
        border-color: @color-brand-2;
        color: @color-brand-2;
      }
    }
  }
}
.title_icon {
  padding: 2px 8px;
  background: rgba(29,43,61,0.06);
  color: rgba(29,43,61,0.75);
  margin-left: 16px;
  border-radius: 4px;
  font-size: 12px;
}
.subtitle {
  margin-top: 4px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.template_type {
  height: 18px;
  font-size: 12px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.45);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-right: 16px;
}

.bill_id {
  height: 18px;
  flex-shrink: 0;
  font-size: 12px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.45);
}

.owner_name {
  height: 18px;
  font-size: 12px;
  line-height: 1.5;
  flex-shrink: 0;
  margin-left: 16px;
  color: rgba(0, 0, 0, 0.45);
}

.status {
  margin-left: 16px;
}

.urgentDisable {
  font-size: 14px;
  margin-top: 6px;
  > span {
    margin-right: 12px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.57;
    width: 64px;
    height: 28px;
    border-radius: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
    background-color: #f5f5f5;
    &:before {
      content: '';
      position: relative;
      left: -5px;
      width: 12px;
      height: 12px;
      display: inline-block;
      background: url('../../../../../images/gray-urgent.svg') center no-repeat;
    }
  }
}

.urgent {
  font-size: 14px;
  margin-top: 6px;
  color: #262626;
  > span {
    margin-right: 12px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.57;
    color: #f5222d;
    padding: 8px;
    height: 28px;
    border-radius: 14px;
    background-color: #fff1f0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    &:before {
      content: '';
      position: relative;
      left: -5px;
      width: 12px;
      height: 12px;
      display: inline-block;
      background: url('../../../../../images/red-urgent.svg') center no-repeat;
    }
  }
}

.new_search_info_subtitle {
  margin-top: 8px;
}


.right_part{
  padding: 16px;
  border-bottom: 1px solid var(--eui-line-divider-default);
  background-color: var(--eui-bg-body);
  :global{
    .header{
      display: flex;
      justify-content: space-between;
    }
    .center {
      font-size: 16px;
      color: var(--eui-text-title);
      flex-grow: 1;
      .title {
        font: var(--eui-font-display-b1);
        align-self: flex-start;
      }
    }
    .right{
      display: flex;
      padding-left: 8px;
      align-self: flex-start;
      .eui-button {
        display: flex;
        align-items: center;
        word-break: keep-all;
        white-space: nowrap;
      }
      .text {
        color: var(--eui-text-title);
        font: var(--eui-font-note-r2);
      }
    }
    .sub-title{
      display: flex;
      font: var(--eui-font-note-r2);
      margin-top: 8px;
      .list{
        max-width: 35%;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
        color: var(--eui-text-caption);
        &:first-child::before,&:last-child::before{
          content: '';
          margin: 0;
        }
        &::before{
          content: '|';
          margin: 0 8px;
          color: var(--eui-text-caption);
        }
        &.status{
          width: auto;
          display: flex;
        }
        .eui-tag{
          color:var(--eui-text-placeholder);
          background-color:var(--eui-bg-base);
        }
      }
    }
    .other{
      .eui-tag-color-danger.eui-tag-solid{
        color: var(--eui-function-danger-500);
      }
      color: var(--eui-function-danger-500);
    }
  }
}