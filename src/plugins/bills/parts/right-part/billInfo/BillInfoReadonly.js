/**************************************************
 * Created by nany<PERSON>ingfeng on 11/07/2017 14:51.
 **************************************************/
import React, { PureComponent, createRef } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import DynamicReadonly from '../../../views/DynamicReadonly'
import {
  parseAsFormTemplate,
  parseAsFormValue,
  parseAsReadOnlyFormTemplate,
  presetFormulaValue,
  parseFlowRisk,
  parseFormValueAsParam,
  canModifyApproveMoney,
  getPayConfig,
  parseFlowRiskV2
} from '../../../util/parse'
import { app as api } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import WrittenOffPartReadOnly from '../../../elements/writtenoff/WrittenOffPart.readonly'
import AutoRepaymentPartReadOnly from '../../../elements/autoRepayment/AutoRepaymentPart.readonly'
import RightBottomLineReadOnly from '../../../elements/RightBottomLine.readonly'
import WarningOrErrorTips from '../../../elements/WarningOrErrorTips'
import WarningSensitive from '../../../elements/WarningSensitive'
import LogsCardView from '../../../../../elements/ekbc-business/bills/LogsCardView'
import TripOrderAction from '../../../elements/TripOrderAction'
import { __unsafe__getFileIdFromFileKey, getCalculateField, saveMultiplePayeed } from '../../../bills.action'
import { getExtraRiskWarningList, getRiskInTemplate } from '../../../riskWarning/formatRiskWarningData'
import classnames from 'classnames'
import styles from './BillInfoReadOnly.module.less'
import { Provider } from 'mobx-react'
import TableStore from '../../../../../elements/payPlan/table/table.store'
import PayPlayTableWrapper from '../../../../../elements/payPlan/PayPlayTableWrapper'
import {
  showSensitiveContent,
  STATE_LIST,
  getFeeTypeVisibleList,
  checkQuickExpends,
  splitTemplateToGroups, getSpecificationHiddenFields
} from "../../../util/billUtils";
import { get, debounce, throttle } from 'lodash'
import { connect } from '@ekuaibao/mobx-store'
import { checkIsRemuneration, fixRemunerationSpecification } from '../../../../../lib/lib-util'
import { updateAutoCalResult } from '../../../util/autoCalculate'
import { getV } from '@ekuaibao/lib/lib/help'
import { fnHideFieldsNote, fnFlowHideFields } from '../../../../../components/utils/fnHideFields'
import FlowPlanReadonly from './FlowPlanReadonly'
import { GET } from '@ekuaibao/fetch'
import CalculateBillState from './CalculateBillState'
import { fnFlowShowFields } from '../../../../../components/utils/fnShowFields'
import { getTrueKey } from '../../../../../lib/utils'
import { Resource } from '@ekuaibao/fetch'
import { adaptFormColumnCount } from '../../../../../elements/layoutBtnGroups/utils';
import { flowPartDrawerConfig, billDrawerConfig } from '../../../../../elements/configure/bill-drawer';
import { getFlowPlanFromLogs, fetchNodesAIAgentMap } from '../../../../../elements/ai-agent-utils'
import { polyfillForAIRiskData } from '../../../riskWarning/ai-audit-result/utils'

const {
  leaveFlowPerformanceStatistics,
  endOpenFlowPerformanceStatistics,
  reportBillPagePaintDuration,
  startOpenFlowThirdPartyPerformanceStatistics,
  flowDetailsStatistics
} = api.require('@lib/flowPerformanceStatistics')

@EnhanceConnect(
  state => {
    return {
      userInfo: state['@common'].userinfo.data,
      baseDataProperties: state['@common'].globalFields.data,
      feeTypes: state['@common'].feetypes.data,
      noRootPathMap: state['@common'].department.noRootPathMap,
      showBillNotesInHistory: state['@bills'].showBillNotesInHistory,
      remunerationBatchField: state['@remuneration'].remunerationBatchField,
      baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap,
      KA_DETAILS_LAYOUT_POWER: state['@common'].powers.KA_DETAILS_LAYOUT,
      skipCheck: state['@bills'].skipCheck,
      allStandardCurrency: state['@common'].allStandardCurrency,
      dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo
    }
  },
  {
    getCalculateField,
    saveMultiplePayeed
  }
)
@connect(store => ({ size: store.states['@layout'].size }))
export default class BillInfoReadonly extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      hiddenFields: [],
      autoCalFields: {},
      riskWarning: void 0,
      riskInfo: {},
      extraRiskWarningList: void 0,
      riskInTemplateList: [],
      riskFieldNum: 0,
      showAllFeeType: false,
      feeTypeVisibleList: [],
      apportionVisibleList: [],
      templateAll: [],
      template: [],
      arrangeLayout: [],
      skipCheck: props.skipCheck,
      allowViewLoanList: false,
      nodesAIAgentMap: {}
    }
    const { dataSource } = props
    if (dataSource.form) {
      this.payPlanStore = new TableStore()
    }
    startOpenFlowThirdPartyPerformanceStatistics()
    this.defaultArrangeLayout = [
      {
        label: '风险提示',
        key: 'risks',
        type: 'default'
      },
      {
        label: '单据状态',
        key: 'status',
        type: 'default'
      },
      {
        label: '差旅订购',
        key: 'travel_order',
        type: 'default'
      },
      {
        label: '单据详情',
        key: 'details',
        type: 'default'
      },
      {
        label: '支付计划',
        key: 'payPlan',
        type: 'default'
      },
      {
        label: '核销组件',
        key: 'writtenOff',
        type: 'default'
      }
    ]

    this.contentRef = createRef()
  }

  componentWillMount() {
    api.invokeService('@common:get:payee:shared')
    api.dataLoader('@common.feetypes').load()
    api.dataLoader('@common.department').load()
    api.dataLoader('@common.specificationVersionConfig').load()
    this._fnGetCalculateFields()
    this.fetchDimensionByCurrencyId()
    this.judgeLoanViewAuth()
    let { bus = new MessageCenter() } = this.props
    bus.watch('element:details:line:click', this.fnDetailsLineClick)
    bus.on('check:requisition:detail', this.handleCheckRequisitionDetail)
    bus.watch('bills:update:for:detail:changed', this.handleDetailChanged)
    api.watch('bills:update:for:detail:changed', this.handleDetailChanged)
    bus.on('element:datalink:card:click', this.fnHandleDataLinkCardClick)
    bus.on('update:calculate:detail:template', this.updateCalDetailTemplate)
    bus.watch('element:trips:addTrip', this.openTripsModal)
    this.getNodesAIAgentMap()

    const { dataSource, saveMultiplePayeed, KA_DETAILS_LAYOUT_POWER, flowId } = this.props
    if (dataSource.form) {
      const { multiplePayeesMode, payPlanMode } = dataSource.form
      saveMultiplePayeed({ multiplePayeesMode, payPlanMode })
    }
    const specificationId = dataSource.form?.specificationId?.id || dataSource.form?.specificationId
    if (KA_DETAILS_LAYOUT_POWER && specificationId) {
      api.invokeService('@bills:get:layoutConfig', { type: 'ARRANGE', specificationId }).then(resp => {
        const { arrangeLayout: arrangeLayoutResp } = resp.value?.configDetail || {}
        if (arrangeLayoutResp) {
          this.setState({
            arrangeLayout: arrangeLayoutResp
          })
        }
      })
    }
    dataSource.form?.details?.length > 0 && api.invokeService('@bills:get:getFeeTypeChange', flowId || dataSource?.id)

    this.fixInvoicePhotoFileIdMissProblem()
  }

  isOverseasByDimension() {
    const { dataSource } = this.props
    const baseCurrencyId = get(dataSource, 'form.legalEntityMultiCurrency.form.baseCurrencyId', '')
    const details = get(dataSource, 'form.details', [])
    const foundInvoice = details.find(detail =>
      detail.feeTypeForm?.invoiceForm?.invoices?.some(invoice => invoice.master?.entityId === 'system_海外发票')
    )

    return baseCurrencyId && foundInvoice
  }

  async fetchDimensionByCurrencyId() {
    const { allStandardCurrency, dataSource } = this.props
    const baseCurrencyId = get(dataSource, 'form.legalEntityMultiCurrency.form.baseCurrencyId', '')
    if (this.isOverseasByDimension()) {
      const { items: rates = [] } = await api.invokeService('@currency-manage:get:currency:rates:by:Id', baseCurrencyId)
      const currency = allStandardCurrency.find(item => item.id === baseCurrencyId)
      api.invokeService('@bills:update:dimention:currency', { currency, rates })
    }
  }

  /**
   * XSG-32526
   * 修复这个工单专用配置，只有 2023/07/29-2023/07/31 的 SaaS 单据才需要修复
   * 由于这个工单造成了发票照片 file id 丢失，预览失效，所以需要通过 key 去还原 file id 来预览发票
   * @returns {Promise<void>}
   */
  async fixInvoicePhotoFileIdMissProblem() {
    const { dataSource } = this.props
    const { form } = dataSource ?? {}
    const { details } = form ?? {}
    if (!details) {
      return
    }

    /**
     * 发票照片中，所有缺失发票的 fileId 的照片
     */
    const allInvoiceFile = details
      .reduce((r, detail) => {
        return r.concat(detail.feeTypeForm.invoiceForm?.attachments ?? [])
      }, [])
      .filter(v => !v?.fileId)
    const isPhotoFileIdMiss = allInvoiceFile.length > 0

    if (!isPhotoFileIdMiss) {
      return
    }

    const result = await __unsafe__getFileIdFromFileKey(allInvoiceFile.map(v => v.key))
    const map =
      result.items?.reduce((r, v) => {
        r[v.key] = v
        return r
      }, {}) ?? {}

    allInvoiceFile.forEach(file => {
      file.fileId = map[file.key]
    })
  }

  fnCheckFeeTypeVisible = () => {
    const { dataSource = {}, userInfo, showAllFeeType } = this.props
    const { formType } = dataSource
    if (formType === 'permit') {
      return this.setState({ showAllFeeType: true })
    }
    const billTypeList = [
      'requisition',
      'expense',
      'reconciliation',
      'settlement',
      'receipt',
      'corpPayment',
      'reimbursement'
    ]
    if (!billTypeList.includes(formType)) return null
    if (showAllFeeType) return this.setState({ showAllFeeType: true })
    const staffId = get(userInfo, 'staff.id')
    const flowId = dataSource.id
    getFeeTypeVisibleList({ flowId, staffId }).then(res => {
      const { state, data } = res
      if (state === 200) {
        const { viewAll } = data
        const feeTypes = data.feeTypes || []
        const apportionVisibleList = []
        const feeTypeVisibleList = feeTypes.map(el => {
          if (el.apportionIds && el.apportionIds.length > 0) {
            apportionVisibleList.push(...el.apportionIds)
          }
          return el.feeTypeId
        })
        const param = {
          flowId,
          showAllFeeType: viewAll,
          feeTypeVisibleList,
          apportionVisibleList
        }
        api.invokeService('@bills:save:feeType:visible:list', param)
        this.setState(param)
      }
    })
  }

  getNodesAIAgentMap = async () => {
    const { dataSource } = this.props
    const nodes = getFlowPlanFromLogs(dataSource?.plan, dataSource?.logs)?.nodes || []
    const map = await fetchNodesAIAgentMap({}, nodes, dataSource?.logs)
    this.setState({ nodesAIAgentMap: map })
  }

  componentDidMount() {
    const {
      detailStack,
      dataSource = {},
      riskData,
      singleRiskData,
      privilegeId = '',
      showAllFeeType,
      showBillNotesInHistory,
      showBillMore = true
    } = this.props
    this.handleCheckBillisQuickExpends()
    this.getSpeHideFields()
    if (!showBillNotesInHistory && dataSource.id) {
      api.invokeService('@bills:get:active:credit:rules:group', { flowId: dataSource.id })
      api.invokeService('@bills:get:bill:notes', { flowId: dataSource.id })
    }
    this.fnCheckFeeTypeVisible()
    if (detailStack) {
      //穿透到明细
      let { details } = dataSource.form
      let detail = details.find(line => line.feeTypeForm.detailId === detailStack.id)

      details && this.fnDetailsLineClick({ ...detail, showAllFeeType }, details)
    }

    if (!riskData && !singleRiskData && showBillMore) {
      api.invokeService('@bills:get:flow:risk:warning', { id: dataSource.id, privilegeId }).then(riskData => {
        this.formatRiskData(riskData)
      })
    } else {
      this.formatRiskData({ ...riskData, ...singleRiskData })
    }
    this.loadFlowRiskInfo()

    if (dataSource.id && window.__PLANTFORM__ === 'DING_TALK') {
      GET('/api/flow/v2/flows/process/task', {
        flowId: dataSource.id
      })
    }
    this.props.skipCheck === null &&
      api.invokeService('@bills:get:calculate:corpId:whitelist').then(res => {
        const skipCheck = res.value
        this.setState({ skipCheck })
      })
    flowDetailsStatistics({
      formType: dataSource?.formType,
      state: dataSource?.state
    })
    endOpenFlowPerformanceStatistics({ flowId: dataSource?.flowId, specification: dataSource?.form?.specificationId })
    reportBillPagePaintDuration()

    window.addEventListener('drawerWidthResize', this.handleBillContentResize)
  }

  handleBillContentResize = throttle((ops) => {
    const refresh = () => {
      const { clientWidth, clientHeight } = document.body
      const size = { x: clientWidth, y: clientHeight, isFirstLocal: true }
      api.store.dispatch('@layout/windowSizeChange')(size)
    }
    const me = api.getState()['@common'].userinfo.staff
    if (ops?.detail?.width <= billDrawerConfig.minWidth) {
      adaptFormColumnCount(me.id + 'layoutBtnGroup', billDrawerConfig.minWidth - flowPartDrawerConfig.minWidth - 32 /* padding */, refresh)
      return
    }
    // 抽屉宽度包括详情和审批流内容，这里重新获取
    let contentRef
    if (contentRef = this.contentRef.current) {
      adaptFormColumnCount(me.id + 'layoutBtnGroup', contentRef.clientWidth, refresh)
    }
  }, 100)

  getSpeHideFields = async () => {
    const specification = this.props.dataSource?.form?.specificationId ?? {}
    const hiddenFields = await getSpecificationHiddenFields(specification)
    this.setState({ hiddenFields })
  }

  handleCheckBillisQuickExpends = async () => {
    const { dataSource } = this.props
    const isQuickExpends = await checkQuickExpends(dataSource)
    if (api.has('change:bills:isQuickExpends')) {
      api.invoke('change:bills:isQuickExpends', isQuickExpends)
    }
  }

  formatRiskData = _riskData => {
    if (_riskData.value) {
      const riskData = polyfillForAIRiskData(_riskData)
      const { dataSource = {}, bus, baseDataProperties, multiplePayeesMode } = this.props
      const template = this.getTemplate(dataSource, baseDataProperties)
      const details = get(dataSource, 'form.details', [])
      const riskWarning = parseFlowRiskV2(riskData, details).form
      const extraRiskWarningList = getExtraRiskWarningList(template, riskWarning, multiplePayeesMode)
      const riskInTemplateList = getRiskInTemplate(template, riskWarning)
      const riskFieldNum = riskData?.riskWarningV2?.length // getRiskFieldNum(riskWarning) 改用新版的数量
      riskWarning && bus?.setFieldsExternalsData?.({ ...riskWarning })
      this.setState({ riskWarning, extraRiskWarningList, riskInTemplateList, riskFieldNum })
    }
  }

  componentWillUnmount() {
    let {
      bus = new MessageCenter(),
      dataSource: {
        id,
        form: { specificationId }
      }
    } = this.props
    bus.un('element:details:line:click', this.fnDetailsLineClick)
    bus.un('check:requisition:detail', this.handleCheckRequisitionDetail)
    bus.un('bills:update:for:detail:changed', this.handleDetailChanged)
    api.un('bills:update:for:detail:changed', this.handleDetailChanged)
    bus.un('element:datalink:card:click', this.fnHandleDataLinkCardClick)
    bus.un('update:calculate:detail:template', this.updateCalDetailTemplate)
    bus.un('element:trips:addTrip', this.openTripsModal)
    if (this.isOverseasByDimension()) {
      api.invokeService('@bills:update:dimention:currency', null)
    }
    leaveFlowPerformanceStatistics({ flowId: id, specification: specificationId })

    window.removeEventListener('drawerWidthResize', this.handleBillContentResize)
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.riskData !== nextProps.riskData || this.props.singleRiskData !== nextProps.singleRiskData) {
      const { riskData = {}, singleRiskData = {} } = nextProps
      this.formatRiskData({ ...riskData, ...singleRiskData })
    }
  }

  _fnGetCalculateFields() {
    const { dataSource, getCalculateField } = this.props
    const { form } = dataSource
    const { submitterId, specificationId } = form
    if (submitterId.id && specificationId.id) {
      getCalculateField(specificationId.id, submitterId.id).then(action => {
        if (action.error) return
        const autoRules = action.payload.items
        const autoCalFields = autoRules?.[0]
        this.setState({ autoCalFields })
        this.isFirstAutoCalFinished = false
        this.updateAutoCalResult(undefined, undefined, true)
        //隐藏必填支持公式在详情中，在知道哪些字段有计算公式后，去修改template，先不让其显示出来
        this.dataLogic(autoCalFields)
      })
    }
  }
  dataLogic = autoCalFields => {
    const { baseDataProperties, dataSource } = this.props
    const template = this.getTemplate(dataSource, baseDataProperties)
    const _template = []
    const { currentNodeShowFieldMap } = fnFlowShowFields(dataSource?.plan)
    template.forEach(item => {
      if (currentNodeShowFieldMap[item.name]) {
        _template.push({ ...item, currentNodeShowField: true })
      } else if (autoCalFields?.onFields.includes(item.name)) {
        const attrs = item?.configs || null || undefined
        if (attrs?.length) {
          let index = attrs.findIndex(el => el.ability === 'caculate' && el.property === 'hide')
          if (index === -1) {
            _template.push(item)
          }
        } else {
          _template.push(item)
        }
      } else {
        _template.push(item)
      }
    })
    //是否设置了审批流字段隐藏
    const flowHiddenFields = fnFlowHideFields(dataSource?.plan)
    const finalTemplate = _template.filter(v => !flowHiddenFields.includes(v?.name))
    this.setState({
      template: finalTemplate,
      templateAll: template
    })
  }

  judgeLoanViewAuth = async () => {
    const configRule = new Resource('/api/v2/loan/config')
    const userInfo = await api.invokeService('@common:get:userinfo', {})
    const loanConfig = await configRule.GET('')

    const hasLoanManage = userInfo?.permissions?.includes("LOAN_MANAGE");
    const restrictAccessItem = loanConfig?.items?.find(item => item?.type === "RESTRICT_ACCESS")?.forbid

    if (userInfo?.staff?.id === this.props.dataSource?.plan?.submitterId) { // 自己看自己的单据
      this.setState({ allowViewLoanList: true })
    } else if (userInfo?.staff?.id === this.props.dataSource?.ownerId?.id) { // 委托人查看单据
      this.setState({ allowViewLoanList: true })
    } else {
      this.setState({ allowViewLoanList: !restrictAccessItem || hasLoanManage });
    }
  }

  updateCalDetailTemplate = value => {
    const calValue = value
    let { templateAll: template } = this.state
    const { dataSource } = this.props
    // 隐藏字段公式计算后符合条件则隐藏
    const calFields = Object.keys(calValue)
    const { currentNodeShowFieldMap, isShowFileds } = fnFlowShowFields(dataSource?.plan)
    calFields?.forEach(field => {
      if (currentNodeShowFieldMap[field]) {
        return
      }
      let index = template.findIndex(el => el.field === field)
      if (index !== -1) {
        // template[index] = { ...template[index], ...calValue[field] }
        let attrs = calValue[field]
        let autoHide = attrs?.attributeHide
        const _hideVisibility = template[index]?.hideVisibility
        let hasStaffs = true
        const { departments, roles, staffs } = _hideVisibility || { departments: [], roles: [], staffs: [] }
        if (!departments?.length && !roles?.length && !staffs?.length) {
          hasStaffs = false
        }
        if ((autoHide && !hasStaffs) || (autoHide && !fnHideFieldsNote(_hideVisibility))) {
          template.splice(index, 1)
        } else {
          template[index] = { ...template[index], ...calValue[field] }
        }
      }
    })
    if (isShowFileds) {
      template = template.map(field => {
        if (currentNodeShowFieldMap[field.name]) {
          return { ...field, currentNodeShowField: true }
        }
        return field
      })
    }
    //是否设置了审批流字段隐藏
    const flowHiddenFields = fnFlowHideFields(dataSource?.plan)
    const finalTemplate = template.filter(v => !flowHiddenFields.includes(v?.name))
    this.setState({
      template: finalTemplate
    })
  }

  isFirstAutoCalFinished = false
  updateAutoCalResult = debounce(async (changeValues = {}, isTextChange, checkDefaultValue) => {
    const { bus, baseDataProperties, baseDataPropertiesMap, dataSource } = this.props
    const { form } = dataSource
    const { submitterId, specificationId } = form
    let formValue = { ...form, ...changeValues }
    formValue.specificationId = formValue?.specificationId?.id || formValue?.specificationId

    const template = this.getTemplate(dataSource, baseDataProperties)
    const needUpdateDefaultValue = checkDefaultValue
    const updateAutoCalResultAttribute = true
    await updateAutoCalResult(
      'master_',
      formValue,
      formValue,
      specificationId,
      baseDataProperties,
      baseDataPropertiesMap,
      bus,
      submitterId.id,
      changeValues,
      isTextChange,
      needUpdateDefaultValue,
      template,
      updateAutoCalResultAttribute
    )
    if (checkDefaultValue) {
      this.isFirstAutoCalFinished = true
    }
  }, 400)

  fnDetailsLineClick = (line, details, flowRulePerformLogs, external) => {
    let {
      isModal,
      stackerManager,
      keel,
      dataSource = {},
      suppleInvoiceBtn,
      source,
      bus,
      riskData = {},
      singleRiskData = {},
      isInHistory,
      needPosition,
      isFlowEditable,
      backLogOwnerId,
      remunerationBatchField
    } = this.props
    const { riskInfo } = this.state
    let { specificationId = {}, submitterId } = dataSource.form
    let flowId = dataSource.id
    const { ownerId, state: billState } = dataSource
    const canEditNote = billState !== 'draft' && billState !== 'rejected'
    let params = {
      dataSource: line,
      viewTitle: i18n.get('查看消费详情'),
      billSpecification: specificationId,
      details: details,
      isEdit: false,
      flowId,
      billState,
      submitterId,
      suppleInvoiceBtn,
      source,
      bus,
      external,
      riskInfo,
      isInHistory,
      isForbid: riskData.isForbid,
      needPosition,
      ownerId,
      canEditNote,
      riskData,
      isFlowEditable,
      backLogOwnerId
    }
    const modifyApproveMoney = canModifyApproveMoney(specificationId)
    const isRemuneration = checkIsRemuneration(specificationId)
    if (isRemuneration) {
      //酬金申报进入明细不走费用类型
      return api.open('@remuneration:RemunerationDetailPopup', {
        bus,
        dataSource,
        isReadonly: true,
        remunerationBatchField,
        title: i18n.get('查看酬金明细')
      })
    }
    if (isModal && (stackerManager || keel)) {
      if (keel) {
        return keel.open('FeeDetailView', { ...params, keel })
      } else if (stackerManager) {
        return stackerManager.push('FeeDetailView', { ...params, stackerManager })
      }
    } else {
      return api.open('@bills:FeeDetailViewPopup', {
        dataSource: line,
        title: i18n.get('查看消费详情'),
        billSpecification: specificationId,
        details,
        flowId,
        billState,
        flowRulePerformLogs: flowRulePerformLogs,
        isEdit: false,
        submitterId,
        suppleInvoiceBtn,
        source,
        bus,
        external,
        riskInfo,
        isInHistory,
        isForbid: riskData.isForbid,
        ownerId,
        canEditNote,
        modifyApproveMoney,
        backLogOwnerId,
        isFlowEditable,
        riskData: { ...riskData, ...singleRiskData },
        formAllData: dataSource.form,
        fullDataSource: dataSource
      })
    }
  }
  fnHandleDataLinkCardClick = ({ entityInfo, field, showClose, disabledStaff = false }) => {
    let { isModal, stackerManager, keel } = this.props
    const { ability = '', name = '' } = field
    if (ability === 'contractSettle') {
      disabledStaff = true
    }
    if (getTrueKey(name) === 'relationContract') {
      disabledStaff = true
    }
    if (isModal) {
      if (keel) {
        keel.open('DataLinkDetailModal', {
          entityInfo: { ...entityInfo, entityId: field.referenceData },
          disabledStaff
        })
      } else if (stackerManager) {
        stackerManager.push('DataLinkDetailModal', {
          entityInfo: { ...entityInfo, entityId: field.referenceData },
          disabledStaff
        })
      } else {
        api.open('@bills:DataLinkDetailModal', {
          viewKey: 'DataLinkDetailModal',
          disabledStaff,
          entityInfo: { ...entityInfo, entityId: field.referenceData },
          field,
          showClose
        })
      }
    } else {
      api.open('@bills:DataLinkDetailModal', {
        viewKey: 'DataLinkDetailModal',
        disabledStaff,
        entityInfo: { ...entityInfo, entityId: field.referenceData },
        field,
        showClose
      })
    }
  }

  handleDetailChanged = ({ id }) => {
    let { bus, dataSource, privilegeId = '', isBacklog } = this.props
    api.invokeService('@bills:get:flow:risk:warning', { id: dataSource.id, privilegeId }).then(riskData => {
      this.formatRiskData(riskData)
      api.emit('viewer-update-riskdata', riskData)
    })
    const params = isBacklog ? undefined : { id }
    return bus.invoke('bills:update:flow', params)
  }

  loadFlowRiskInfo = () => {
    const { dataSource, baseDataProperties } = this.props
    const form = get(dataSource, 'form', {})
    const details = get(dataSource, 'form.details', [])
    const currentSpecification = get(dataSource, 'currentSpecification') || get(dataSource, 'form.specificationId')
    const components = currentSpecification.components || []
    const detailCmp = components.find(el => el.field === 'details')
    if (detailCmp?.realtimeCalculateBudget && !!details.length && ['approving', 'paying'].includes(dataSource.state)) {
      this.tempId = new Date().getTime()
      const currentValue = parseFormValueAsParam(form, currentSpecification, dataSource, baseDataProperties) || {}
      api
        .invokeService('@bills:get:flow:risk:info', {
          formType: dataSource.formType,
          form: currentValue.form,
          state: dataSource.state,
          version: this.tempId
        })
        .then(riskData => {
          if (this.tempId.toString() === riskData.flowId) {
            const riskInfo = parseFlowRisk(riskData, undefined, details).form?.details;
            if (riskInfo) {
              this.setState({ riskInfo })
            }
          }
        })
    }
  }

  handleCheckRequisitionDetail = value => {
    let { isModal, stackerManager, keel, privilegeId, showAllFeeType } = this.props
    const params = { ...value, privilegeId, showAllFeeType }
    if (isModal) {
      if (keel) {
        return keel.open('ApplyEventDetail', params)
      } else if (stackerManager) {
        return stackerManager.push('ApplyEventDetail', params)
      } else {
        return api.open('@bills:ApplyEventStackerModal', { viewKey: 'ApplyEventDetail', ...params })
      }
    } else {
      return api.open('@bills:ApplyEventStackerModal', { viewKey: 'ApplyEventDetail', ...params })
    }
  }

  getTemplate = (dataSource, baseDataProperties) => {
    const { flowRulePerformLogs } = dataSource
    let template = parseAsFormTemplate(dataSource, baseDataProperties)
    template = parseAsReadOnlyFormTemplate(template)
    return presetFormulaValue(flowRulePerformLogs && flowRulePerformLogs.results, template)
  }

  renderChild(data) {
    let child = []
    if (data && data.length > 0) {
      data.map((item, index) => {
        child.push(
          <div
            style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}
            key={index + 'rule'}
          >
            {item.errorMsg}
          </div>
        )
      })
    }
    return child
  }

  openTripsModal = ({ value, external, shouldUpdate, canEdit, currentTrips }) => {
    let { dataSource, bus, setValidateError } = this.props
    const onlyTrips = getV(dataSource, 'form.trips', [])
    const currentSpecification = dataSource?.form?.specificationId
    const configs = currentSpecification.configs || []
    const submitterId = dataSource?.form?.submitterId
    const flowId = dataSource?.id
    const requisitionConfig = configs.find(v => v.ability === 'requisition') || {}
    const {
      tripType: { isAll, ids = [] }
    } = requisitionConfig
    const trips = canEdit ? value : [currentTrips]
    return api.invokeService('@bills:get:getTripsTemplate').then(action => {
      let templates = action.items
      if (!isAll) {
        templates = templates.filter(v => !!~ids.indexOf(v.id))
      }
      if (templates.length) {
        return bus.getValue().then(data => {
          return api.open('@bills:AddTripsModal', {
            templates,
            trips,
            onlyTrips,
            submitterId,
            shouldUpdate,
            external,
            flowId,
            billSpecification: currentSpecification,
            billData: data,
            setValidateError,
            canEdit
          })
        })
      } else {
        showMessage.error(i18n.get('没有可用的费用类型,请前往「行程类型」配置'))
      }
    })
  }

  render() {
    let {
      bus,
      dataSource,
      baseDataProperties,
      feeTypes,
      keel,
      stackerManager,
      isModal,
      onOpenOwnerLoanList,
      changeTabAction,
      isInHistory = false,
      flowId,
      userInfo,
      noRootPathMap,
      noRiskWarning,
      riskData = {},
      budgetParams,
      offsetWidth,
      size,
      billState,
      backLogOwnerId,
      isEditConfig,
      flowlogType,
      versionItemAction,
      hiddenFlowLinks = false,
      showFlowPlan = true,
      singleRiskData = {},
      privilegeId,
      viewFlowState,
    } = this.props
    const {
      hiddenFields,
      arrangeLayout,
      autoCalFields,
      extraRiskWarningList,
      riskInTemplateList,
      riskFieldNum,
      feeTypeVisibleList,
      riskInfo,
      apportionVisibleList,
      showAllFeeType,
      template = [],
      skipCheck,
      allowViewLoanList
    } = this.state

    if (!dataSource) {
      return null
    }
    const billSpecification = get(dataSource, 'form.specificationId', {})
    const isRemuneration = checkIsRemuneration(billSpecification)
    isRemuneration && fixRemunerationSpecification(billSpecification)
    const canWrittenOff = !!billSpecification.configs.find(v => v.ability === 'writtenOff' && !v.writeOffTurnOff)
    const isReceiptTemplate = !!billSpecification.configs.find(v => v.ability === 'receipt')
    let value = parseAsFormValue(dataSource, noRootPathMap)
    const {
      flowRulePerformLogs,
      state,
      ownerId,
      logs = [],
      form: { submitterId },
      writtenOff = {},
      loanManualRepayment = {}
    } = dataSource
    let { records = [] } = writtenOff || {}
    let repaymentRecords = loanManualRepayment?.records || []
    const submitNodes = logs.filter(v => v.action === 'freeflow.submit').map(v => v.attributes)
    const { sensitiveContent, sensitiveAttachment } = submitNodes[submitNodes.length - 1] || {}
    const staff = get(userInfo, 'staff', {})
    // 如果是查看历史版本：viewFlowState取的查看当前单据的状态，否则已经审批完成的还可以通过修改历史查看敏感信息
    const flowState = viewFlowState || state
    const showSensitive = showSensitiveContent(dataSource, staff.id) && STATE_LIST.indexOf(flowState) < 0
    const canEditNote = state !== 'draft' && state !== 'rejected'

    // 待支付部分支付完成的支付计划  显示‘回单文件’列
    let isHaveRecipt = false
    if (state === 'paying') {
      value.payPlan?.forEach(el => {
        if (el?.dataLinkForm.E_system_支付计划_支付状态 === '支付成功') {
          isHaveRecipt = true
          return
        } else if (el?.receiptId !== undefined) {
          if (el?.receiptId.length > 0) {
            isHaveRecipt = true
            return
          }
        }
      })
    }
    // 单收款人只在支付完成后 显示支付计划表格
    let singlePayeesMode = false
    if (state === 'paid' || state === 'archived') {
      if (!value?.multiplePayeesMode) {
        singlePayeesMode = true
      }
    }
    const tags = {
      details: feeTypes,
      trips: { readOnly: true },
      isOpenAssociation: { tag: value, plan: dataSource?.plan }
    }
    const { ability } = getPayConfig(billSpecification)
    const showPayPlan = (value.multiplePayeesMode || singlePayeesMode) && !!value.payPlan?.length
    const arrangeLayoutList = arrangeLayout?.length ? arrangeLayout : this.defaultArrangeLayout
    const { groupTemplate, isGroup } = splitTemplateToGroups(template)
    // 为了分组
    const risksAndStatus = arrangeLayoutList.filter(item => item.key === 'risks' || item.key === 'status')
    const showClassItem = !(!riskInTemplateList?.length && !extraRiskWarningList?.length)
    return (
      <div className={classnames(styles['layout-bill-info-readonly'], styles['layout5-bill-info-readonly'])}>
        <div className={classnames('content-wrap')}>
          <div className={classnames('layout5-bill-info-content')} ref={this.contentRef}>
            <CalculateBillState
              className="group-item"
              billState={state}
              skipCheck={skipCheck}
              flowId={flowId || dataSource?.id}
            />
            {state !== 'pending' && (
              <>
                {risksAndStatus.length === 2 && (!isInHistory || showSensitive || showClassItem) && (
                  <div className="group-item">
                    {showSensitive && <WarningSensitive content={sensitiveContent} attachments={sensitiveAttachment} />}
                    <WarningOrErrorTips
                      isForbid={riskData.isForbid}
                      noRiskWarning={noRiskWarning}
                      riskFieldNum={riskFieldNum}
                      onOpenOwnerLoanList={onOpenOwnerLoanList}
                      riskInTemplateList={riskInTemplateList}
                      extraRiskWarningList={extraRiskWarningList}
                      budgetParams={budgetParams}
                      allowViewLoanList={allowViewLoanList}
                    />
                    {!isInHistory && (
                      <LogsCardView bus={bus} dataSource={dataSource} changeTab={changeTabAction} userInfo={staff} nodesAIAgentMap={this.state.nodesAIAgentMap} />
                    )}
                  </div>
                )}
                {risksAndStatus.length === 1 && risksAndStatus[0].key === 'risks' && (showSensitive || showClassItem) && (
                  <div className="group-item">
                    {showSensitive && <WarningSensitive content={sensitiveContent} attachments={sensitiveAttachment} />}
                    <WarningOrErrorTips
                      isForbid={riskData.isForbid}
                      noRiskWarning={noRiskWarning}
                      riskFieldNum={riskFieldNum}
                      onOpenOwnerLoanList={onOpenOwnerLoanList}
                      riskInTemplateList={riskInTemplateList}
                      extraRiskWarningList={extraRiskWarningList}
                      budgetParams={budgetParams}
                      allowViewLoanList={allowViewLoanList}
                    />
                  </div>
                )}
                {risksAndStatus.length === 1 && risksAndStatus[0].key === 'status' && !isInHistory && (
                  <div className="group-item">
                    <LogsCardView bus={bus} dataSource={dataSource} changeTab={changeTabAction} userInfo={staff} nodesAIAgentMap={this.state.nodesAIAgentMap} />
                  </div>
                )}
              </>
            )}
            {arrangeLayoutList.map(arrangeLayoutItem => {
              const arrangeLayoutItemKey = arrangeLayoutItem.key
              if (arrangeLayoutItemKey === 'travel_order') {
                return <TripOrderAction data={dataSource} classNames="group-item" key="TripOrderAction" />
              }
              if (arrangeLayoutItemKey === 'details') {
                return (
                  <div className={!isGroup ? 'group-item' : ''} key="DynamicReadonly">
                    <DynamicReadonly
                      hiddenFields={hiddenFields}
                      autoCalFields={autoCalFields}
                      bus={bus}
                      billState={billState || dataSource?.state}
                      ownerId={ownerId}
                      template={template}
                      groupTemplate={groupTemplate}
                      baseDataProperties={baseDataProperties}
                      flowRulePerformLogs={flowRulePerformLogs}
                      value={value}
                      isInHistory={isInHistory}
                      tags={tags}
                      flowId={flowId || dataSource.id}
                      isForbid={singleRiskData?.isForbid}
                      dataSource={dataSource}
                      showAllFeeType={showAllFeeType}
                      feeTypeVisibleList={feeTypeVisibleList}
                      apportionVisibleList={apportionVisibleList}
                      canEditNote={canEditNote}
                      billSpecification={billSpecification}
                      riskData={singleRiskData}
                      riskInfo={riskInfo}
                      backLogOwnerId={backLogOwnerId}
                      offsetWidth={offsetWidth}
                      size={size}
                      submitterId={submitterId}
                      classNameGroup="group-item"
                      isGroup={isGroup}
                      hiddenFlowLinks={hiddenFlowLinks}
                    />
                  </div>
                )
              }
              if (arrangeLayoutItemKey === 'payPlan' && showPayPlan) {
                return (
                  <div className="group-item" key="PayPlayTableWrapper">
                    <Provider PayPlanStore={this.payPlanStore}>
                      <PayPlayTableWrapper
                        key={dataSource?.updateTime}
                        className="mb-24"
                        isModify={false}
                        dataSource={dataSource}
                        payPlanValue={value.payPlan}
                        isHaveRecipt={isHaveRecipt}
                        singleCanPay={!!ability && singlePayeesMode}
                      />
                    </Provider>
                  </div>
                )
              }
              if (arrangeLayoutItemKey === 'writtenOff' && records.length > 0) {
                return (
                  <div className="group-item" key="WrittenOffPartReadOnly">
                    <WrittenOffPartReadOnly
                      isEdit={false}
                      isModal={isModal}
                      value={records}
                      flowId={flowId || dataSource.id}
                      keel={keel}
                      stackerManager={stackerManager}
                      showAllFeeTypeInManage={this.props.showAllFeeType}
                      writtenOffSumByCurrency={dataSource?.form?.writtenOffSumByCurrency}
                      writtenOffByDetails={dataSource?.form?.writtenOffByDetails}
                      writtenOffMoney={dataSource?.form?.writtenOffMoney}
                      billSpecification={billSpecification}
                    />
                  </div>
                )
              }
              if (arrangeLayoutItemKey === 'flow' && showFlowPlan) {
                return (
                  <div className="group-item" key="FlowPlanReadonly">
                    <FlowPlanReadonly
                      versionItemAction={versionItemAction}
                      bus={bus}
                      dataSource={dataSource}
                      isEditConfig={isEditConfig}
                      userInfo={userInfo}
                      flowlogType={flowlogType}
                      isSimpleMode={true}
                      privilegeId={privilegeId}
                    />
                  </div>
                )
              }
            })}
            {repaymentRecords?.length ? (
              <div className="group-item">
                <AutoRepaymentPartReadOnly isEdit={false} value={repaymentRecords} writtenOff={records} />
              </div>
            ) : (
              <div />
            )}
            {!isInHistory && (
              <RightBottomLineReadOnly
                className="group-item"
                canWrittenOff={canWrittenOff}
                onOpenOwnerLoanList={onOpenOwnerLoanList}
                submitter={value.submitterId}
                dataSource={dataSource}
                isReceiptTemplate={isReceiptTemplate}
                bus={bus}
                billSpecification={billSpecification}
              />
            )}
          </div>
        </div>
      </div>
    )
  }
}
