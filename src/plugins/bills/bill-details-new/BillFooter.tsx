import React, { useEffect, useRef, useState } from "react";
import { SkeletonButton } from "../parts/right-part/billInfo/SkeletonModal";
import { BillActions, BillActionsProps } from "./BillActions";
import { Action, Backlog, FlowItem } from "./types";
import { getBacklogInfoByFlow, getButtonOptionsByFlowId } from "./utils";
import { getButtonsOfCreatingNewBill } from './config'
import { get } from "lodash";
import { app as api } from '@ekuaibao/whispered'

export interface BillFooterProps extends Omit<BillActionsProps, 'buttons' | 'backlog'> {
  /**
   * 是否隐藏底部按钮
   */
  hideFooter?: boolean,
  /**
   * 待办
   */
  backlog?: Backlog
  /**
   * 按钮配置
   */
  buttons?: Action[]
  /**
   * 单据信息
   */
  flow: FlowItem
  /**
   * 兼容旧版的处理逻辑
  */
  params?: {
    autoClose?: boolean
  }
}

const NotAllowCreateApplication = api.getState()['@common'].powers?.powersList?.find(power => power.powerCode === '110415')?.state === 'using'

export const BillFooter = (props: BillFooterProps) => {
  const { hideFooter, buttons: _buttons, backlog, flow, ...otherProps } = props
  const [loading, setLoading] = useState(false)
  const [buttons, setButtons] = useState<Action[]>([])
  const [backlogInfo, setBacklogInfo] = useState<Backlog | null>(null)
  const loadedRef = useRef(false)

  const isInCreatingNewBill = !flow?.id

  useEffect(() => {
    const _getFlowButtons = async () => {
      if (hideFooter) {
        return []
      }
      if (_buttons && _buttons.length > 0) {
        return _buttons
      }
      if (isInCreatingNewBill) {
        return getButtonsOfCreatingNewBill(otherProps.layer)
      }
      return getButtonOptionsByFlowId(flow.id, flow)
    }

    const getBacklogInfoByFlowId = async () => {
      if (backlog || !flow?.id) {
        return {
          ...backlog || { id: '' },
          flowId: flow,
        }
      }
      return getBacklogInfoByFlow(flow, backlog?.id, otherProps.privilegeId)
    }

    const initButtons = async () => {
      try {
        setLoading(true)
        const [buttons, backlogInfo] = await Promise.all([_getFlowButtons(), getBacklogInfoByFlowId()])
        setButtons(buttons)
        setBacklogInfo(backlogInfo)
        loadedRef.current = true;
      } catch (e) {
        console.error(e)
      } finally {
        setLoading(false)
      }
    }

    initButtons();

  }, [hideFooter, flow, _buttons, NotAllowCreateApplication])

  if (hideFooter) {
    return null
  }

  if (loading && !loadedRef.current) {
    return <div style={{ backgroundColor: 'var(--eui-bg-body)' }}>
      <SkeletonButton lineCount={4} />
    </div>
  }

  return (
    <BillActions
      {...otherProps}
      buttons={buttons}
      backlog={backlogInfo}
    />
  )
}
