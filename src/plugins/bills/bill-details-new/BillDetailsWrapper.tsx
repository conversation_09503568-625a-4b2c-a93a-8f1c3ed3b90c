import React, { useEffect, useRef, useState, useMemo } from 'react'
import { Backlog, FlowItem, Action } from './types'
import { app as api } from '@ekuaibao/whispered'
import { BillDetailsV2Provider } from './context'
import { fnIsRiskError } from '../riskWarning/formatRiskWarningData'
import BillDetails, { BillDetailsProps } from './BillDetails'
import { getButtonsOfCreatingNewBill } from './config'
import LoadingSkeleton from './skeleton/loading'
import styles from './index.module.less'
import { getBacklogInfoByFlowId, getButtonOptionsByFlowId, getFlowInfoById } from './utils'
import { BillFooter, BillFooterProps } from './BillFooter'
import MessageCenter from '@ekuaibao/messagecenter'
import { get, pick } from 'lodash'

type IntegratedType = Omit<BillDetailsProps, 'backlog'> & Omit<BillFooterProps, 'forceUpdateFlow'>

interface OnSwitchOptions {
  backlog: Backlog
  flow: FlowItem
  index: number
}

export interface BillDetailsPropsWrapper extends IntegratedType {
  flows: FlowItem[]
  currentId: string
  /**
   * 代办时需要传入，与flows一一对应
   * 主要用于按钮的事件处理
  */
  backlogs?: Backlog[]
  privilegeId?: string
  /**
   * 根据flowId获取明细ID的函数，用于代办时自动打开明细
  */
  getExpenseDetailIdByFlowId?: (flowId: string) => string
  /**
   * 自定义函数，用于通过flowId获取flow详情
  */
  customGettingFlowInfo?: (flowId: string) => Promise<{ value: FlowItem }>
  onSwitch?: (params: OnSwitchOptions) => void
}

const BillDetailsWrapper = ({ flows, currentId, backlogs, getExpenseDetailIdByFlowId, customGettingFlowInfo, onSwitch, ...otherProps }: BillDetailsPropsWrapper) => {
  const currentIndexRef = useRef(flows.findIndex(bill => bill.id === currentId))
  const currentFlowInfo = useRef<FlowItem>(null)
  const [currentDataSource, setCurrentDataSource] = useState<{
    backlog: Backlog
    buttons: Action[]
    riskData: any
  } | null>(null)

  const [loading, setLoading] = useState(false)
  const busRef = useRef<MessageCenter>(otherProps.bus || new MessageCenter())

  const getIfDisabledShowUpDown = (): ('prev' | 'next')[] => {
    if (flows.length <= 1) {
      return ['prev', 'next']
    } else if (currentIndexRef.current === 0) {
      return ['prev']
    } else if (currentIndexRef.current === flows.length - 1) {
      return ['next']
    }
    return []
  }

  const getFlowActionsAndFlowInfo = async (id: string) => {

    const _getFlowButtons = async () => {
      if (otherProps.hideFooter) {
        return []
      }
      if (otherProps.buttons && otherProps.buttons.length > 0) {
        return otherProps.buttons
      }
      const flow = flows?.find(flow => flow?.id === currentId)
      return getButtonOptionsByFlowId(id, flow)
    }

    const getBacklog = async () => {
      const backlog = backlogs?.[currentIndexRef.current] || {} as Backlog
      let backlogId = backlog.id
      try {
        return getBacklogInfoByFlowId(id, backlogId, otherProps.privilegeId)
      } catch (error) {
        console.error('getBacklog error', error)
        // ignore error
        return {
          id: '',
        }
      }
    }

    const getRiskTip = async () => {
      if (!id) {
        return undefined
      }
      try {
        return await api.invokeService('@bills:get:flow:risk:warning', { id, privilegeId: otherProps.privilegeId })
      } catch (e) {
        console.error('getRiskTip error', e)
      }
    }

    const getFlowInfo = async () => {
      const resp = await (customGettingFlowInfo ? customGettingFlowInfo(id) : getFlowInfoById(id, otherProps.privilegeId))
      const { value: flowId } = (resp || {}) as any
      return flowId
    }

    const isDraftOrRejected = (flowId: FlowItem) => {
      return flowId.state === 'rejected' || flowId.state === 'draft'
    }

    const promiseAllDataNeeded = Promise.all([getRiskTip(), getFlowInfo(), _getFlowButtons(), getBacklog()])

    const [riskData, flowId, flowButtons, backlog] = await promiseAllDataNeeded
    return {
      buttons: flowButtons,
      flowId,
      riskData: {
        ...riskData,
        isForbid: isDraftOrRejected(flowId) && fnIsRiskError(riskData?.value?.riskWarning)
      },
      backlog
    }
  }

  /**
   * @param id 单据id
   * @param slient 是否静默，静默时不显示loading
  */
  const handleFetchFlowInfo = async (id: string, slient = false) => {
    try {
      !slient && setLoading(true)
      const { buttons, flowId, riskData, backlog } = await getFlowActionsAndFlowInfo(id)
      currentFlowInfo.current = flowId
      setCurrentDataSource({
        backlog: {
          ...backlog,
          flowId,
        },
        buttons,
        riskData,
      })
      return {
        success: true,
        backlog,
        flow: flowId,
      }
    } catch (error) {
      console.error(error)
      return {
        success: false,
        backlog: null,
        flow: null
      }
    } finally {
      !slient && setLoading(false)
    }
  }

  useEffect(() => {
    if (currentId) {
      handleFetchFlowInfo(currentId)
    } else {
      currentFlowInfo.current = flows?.[0] || {} as FlowItem
      setCurrentDataSource(
        {
          backlog: {
            id: '',
            flowId: flows?.[0] || {},
          } as unknown as Backlog,
          buttons: otherProps.buttons || getButtonsOfCreatingNewBill(otherProps.layer) || [],
          riskData: undefined
        }
      )
    }
  }, [currentId, flows, otherProps.buttons, otherProps.layer])

  const activeItemInDataGrid = (index: number) => {
    // 这种bus实现不太好，但是这个是改动最小的实现方式
    if (busRef.current?.has('table:change:active:row:index')) {
      busRef.current.emit('table:change:active:row:index', index)
    }
  }

  useEffect(() => {

    // destory
    return () => {
      activeItemInDataGrid(-1)
    }
  }, [])

  const maySaveDataBeforeToNext = async () => {
    try {
      const bus = busRef.current
      if (bus.has('check:value:changed')) {
        await bus.invoke('check:value:changed')
        // @ts-ignore
        bus?.reload?.()
      }
      return true
    } catch (e) {
      return false
    }
  }

  const handleUpDown = async (type: 'prev' | 'next') => {
    if (!await maySaveDataBeforeToNext()) {
      return
    }
    if (type === 'prev') {
      currentIndexRef.current--
    } else {
      currentIndexRef.current++
    }
    const switchResult = await handleFetchFlowInfo(flows[currentIndexRef.current].id)
    if (switchResult.success) {
      activeItemInDataGrid(currentIndexRef.current)
      onSwitch?.({
        backlog: switchResult.backlog,
        flow: switchResult.flow,
        index: currentIndexRef.current
      })
    }
  }

  const forceUpdateFlow = async () => {
    // flows 在部分场景里可能为空，所以需要currentId兜底
    const res = await handleFetchFlowInfo(flows[currentIndexRef.current]?.id || currentId, true)
    if (res.success) {
      return {
        backlog: res.flow,
      }
    }
    return {
      backlog: get(currentDataSource, 'backlog.flowId')
    }
  }

  useEffect(() => {
    // 删除评论之后的刷新是依赖bus的 bills:update:flow方法。
    if (busRef.current) {
      busRef.current.watch('bills:update:flow', forceUpdateFlow)
    }

    return () => {
      busRef.current.un('bills:update:flow', forceUpdateFlow)
    }
  }, [busRef, forceUpdateFlow])

  const autoOpenDetailId = useMemo(() => {
    if (flows && getExpenseDetailIdByFlowId) {
      return getExpenseDetailIdByFlowId(flows[currentIndexRef.current]?.id)
    }
    return undefined
  }, [getExpenseDetailIdByFlowId, flows, currentIndexRef.current])


  const canDeleteComment = useMemo(() => {
    if (otherProps.privilegeId) {
      const myPermissions = api.getState('@permission')?.roleListForMine || []
      const currentPrivilegePermission = (myPermissions.find((item: any) => item.id === otherProps.privilegeId) || {}).actions || []
      const KEYWORD_OF_DELETE_COMMENT_PERMISSION = 'freeflow.deleteComment'
      return currentPrivilegePermission.includes(KEYWORD_OF_DELETE_COMMENT_PERMISSION)
    }
    return false
  }, [otherProps.privilegeId])

  if (!currentDataSource || loading) {
    return (
      <div className={styles['bill-details-of-drawer']}>
        <LoadingSkeleton mode='table' />
      </div>
    )
  }

  return (
    <div className={styles['bill-details-of-drawer']}>
      <BillDetailsV2Provider
        value={{
          currentDataSource,
          goNext: () => handleUpDown('next'),
          goPrev: () => handleUpDown('prev'),
          disabledUpDown: getIfDisabledShowUpDown(),
          dataSource: flows
        }}
      >
        <BillDetails
          footer={
            <BillFooter
              {...otherProps}
              {...currentDataSource}
              // 被删除字段不需要显示footer
              hideFooter={currentFlowInfo.current?.active === false}
              forceUpdateFlow={forceUpdateFlow}
              flow={currentFlowInfo.current}
              bus={busRef.current}
              showAllFeeType={otherProps.billDetailsProps?.showAllFeeType}
              noCheckPermissions={otherProps.billDetailsProps?.noCheckPermissions}
            />
          }
          autoOpenDetailId={autoOpenDetailId}
          bus={busRef.current}
          {...otherProps}
          {...currentDataSource}
          canDeleteComment={canDeleteComment}
        />
      </BillDetailsV2Provider>
    </div>
  )
}

export default BillDetailsWrapper
