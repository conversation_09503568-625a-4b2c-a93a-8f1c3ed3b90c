/*
 * @Author: Onein
 * @Date: 2019-09-20 15:20:53
 * @Last Modified by: Onein
 * @Last Modified time: 2020-10-30 01:24:25
 */

import './Table.less'
import React from 'react'
import classnames from 'classnames'
import { Table, message, Checkbox } from 'antd'
import { observer, inject } from 'mobx-react'
import { toJS } from 'mobx'
import { ObjFormIF } from './types'
import TableAction from './table/TableAction'
import EditableFormRow from './table/EditRow'
import EditableCell from './table/EditCell'
import PayPlanTableStore from './table/table.store'
import Money from '../puppet/Money'
import EKBIcon from '../ekbIcon'
import { app as api } from '@ekuaibao/whispered'
import { formatColumn } from './helper/formatTableData'
import { formatCoumnForDisplay } from './table/ColumnFormat'
import { MoneyInterface } from '@ekuaibao/lib/lib/entityUtil/EntityTypes'
import { getNodeValueByPath, total, getDetailCalculateMoney } from '@ekuaibao/lib/lib/lib-util'
import { MoneyMath } from '@ekuaibao/money-math'
import MessageCenter from '@ekuaibao/messagecenter'
import { getTablePageSize } from './helper/tableHelper'
import { downloadReceipt } from './helper/fetchUtil'
import { isNegat, getV } from '@ekuaibao/lib/lib/help'
import { isIE } from '../../lib/misc'
import { EnhanceConnect } from '@ekuaibao/store'
import { getBoolVariation } from '../../lib/featbit'

interface Props {
  editable: boolean
  canPay: boolean
  errorMsg: string
  feeAmount?: MoneyInterface
  baseDataPropertiesMap: any
  baseDataProperties: any
  PayPlanStore?: PayPlanTableStore
  template: any[]
  value?: any
  dataSource?: any
  writtenOffRecords?: any
  payPlanValue?: any
  bus?: MessageCenter
  repay?: boolean
  handleSelect?: Function
  flowId?: string
  isModify?: boolean
  isHaveRecipt?: string
  dimentionCurrencyInfo?: any
  singleCanPay?: boolean
  billSpecification?: any
  multiplePayeesMode?: boolean
  payPlanMode?: boolean
  payeePayPlan?: boolean
}

interface States {}

@EnhanceConnect(state => {
  return {
    dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo,
    multiplePayeesMode: state['@bills'].multiplePayeesMode, // 多收款人模式
    payPlanMode: state['@bills'].payPlanMode, // 按金额多收款
    payeePayPlan: state['@bills'].payeePayPlan // 按收款信息汇总明细金额
  }
})
@inject('PayPlanStore')
@observer
export default class PayPlanTable extends React.Component<Props, States> {
  payPlanStore: PayPlanTableStore

  constructor(props: Props) {
    super(props)
    const {
      bus,
      PayPlanStore,
      payPlanValue,
      value = {},
      dataSource,
      multiplePayeesMode,
      payPlanMode,
      payeePayPlan,
      editable
    } = props
    this.payPlanStore = PayPlanStore
    this.payPlanStore.bus = bus
    this.checkPlanByApportionConfig()
    const payPlanInfos = payPlanValue || value.payPlan || []
    if (multiplePayeesMode) {
      // 多收款人模式中，从表单或保存过的单据中取值
      this.payPlanStore.paymentPlanByApportion = editable
        ? value?.paymentPlanByApportion
        : getV(dataSource, 'form.paymentPlanByApportion', false)

      if (!payPlanMode && payPlanInfos.length) {
        // 按明细多收款 需将收款信息同步到支付计划
        const details = getV(dataSource, 'form.details', [])
        payPlanInfos.forEach(el => {
          let target, apportionTarget
          if (this.payPlanStore.paymentPlanByApportion) {
            target =
              details?.find(item => {
                if (item.feeTypeForm.detailId === el.dataLinkId) {
                  return true
                }
                apportionTarget = item.feeTypeForm.apportions?.find(
                  apportion => apportion.apportionForm.apportionId === el.dataLinkId
                )
                return !!apportionTarget
              }) || {}
          } else {
            target = details?.find(item => item.feeTypeForm.detailId === el.dataLinkId) || {}
            this.fnDeleteLegelEntityInPayPlan(el)
          }
          if (target.feeTypeForm) {
            // 将明细中的收款信息赋值到对应的支付计划中
            const payeeId = target.feeTypeForm.feeDetailPayeeId
            el.dataLinkForm['E_system_支付计划_收款信息'] = payeeId
            const apportions = getV(target, 'feeTypeForm.apportions')
            // 按明细多收款时，仅分摊生成的支付计划带有法人实体字段，所以有法人实体的字段，不应赋值分摊数据
            if (apportionTarget) {
              // apportionTarget有值时，代表支付计划id与分摊id匹配，将分摊中的法人实体的值，赋值给分摊中的法人实体字段
              // 由分摊明细生成的支付计划，不能继续给支付计划赋值apportions
              el.dataLinkForm['E_system_支付计划_legalEntity'] = getV(apportionTarget, 'apportionForm.法人实体')
            } else if (apportions) {
              // 由费用明细生成的支付计划，赋值apportions
              el.apportions = apportions
            } else if (this.payPlanStore.paymentPlanByApportion && !editable) {
              // 勾选【包含分摊明细】，但支付计划中没有法人实体，在只读单据回显时，取单据上的法人实体
              const legalEntity = getV(dataSource, 'form.法人实体') as any
              if (
                legalEntity &&
                typeof el.dataLinkForm['E_system_支付计划_legalEntity'] === 'string' &&
                el.dataLinkForm['E_system_支付计划_legalEntity'] === legalEntity?.id
              ) {
                el.dataLinkForm['E_system_支付计划_legalEntity'] = legalEntity
              }
            }
          }
        })
      } else if (payPlanMode && payPlanInfos.length) {
        // 按金额多收款
        payPlanInfos.forEach(el => {
          this.fnDeleteLegelEntityInPayPlan(el)
          const legalEntity = getV(el, 'dataLinkForm.legalEntity')
          if (legalEntity && typeof el.dataLinkForm['E_system_支付计划_legalEntity'] === 'string') {
            el.dataLinkForm['E_system_支付计划_legalEntity'] = legalEntity
          }
        })
      }
    } else {
      if (payPlanInfos.length) {
        // 单收款  收款信息同步到支付计划
        payPlanInfos.forEach(el => {
          const payeeId = getV(dataSource, 'form.payeeId')
          el.dataLinkForm['E_system_支付计划_收款信息'] = payeeId
        })
      }
    }
    if (editable) {
      this.handlePayeeInfoChange({ multiplePayeesMode, payPlanMode, payeePayPlan }, true)
    }
    payPlanInfos.length > 0 && this.payPlanStore && this.payPlanStore.initObjs(payPlanInfos)
  }

  // 检查是否支持分摊生成支付计划
  checkPlanByApportionConfig = () => {
    const { billSpecification, dataSource } = this.props
    let configs = []
    if (billSpecification) {
      // 单据编辑
      configs = getV(billSpecification, 'configs', [])
    } else if (dataSource) {
      // 只读单据
      configs = getV(dataSource, 'form.specificationId.configs', [])
    }
    const pay = configs.find(el => el.ability === 'pay')
    this.payPlanStore.supportPlanByApportion = getV(pay, 'paymentPlanByApportion', false)
  }

  /**
   * 除按分摊生成的支付计划外，法人实体字段在保存草稿和提交单据时赋值到支付计划中
   * 单据编辑状态中，初始化时删除支付计划中的法人实体，按分摊生成支付计划时不删除
   */
  fnDeleteLegelEntityInPayPlan = payPlan => {
    if (this.props.editable && payPlan.dataLinkForm?.E_system_支付计划_legalEntity) {
      delete payPlan.dataLinkForm.E_system_支付计划_legalEntity
    }
  }

  handleSpecificationChange = ({ dataSource }) => {
    const configs = getV(dataSource, 'form.specificationId.configs', [])
    const pay = configs.find(el => el.ability === 'pay')
    this.payPlanStore.supportPlanByApportion = getV(pay, 'paymentPlanByApportion', false)
  }

  componentDidMount() {
    const { bus } = this.props
    bus && bus.on('dimension:currency:change:payPlan:change', this.handleCurrencyChange)
    bus && bus.on('payeeInfo:isMultiplePayee', this.handlePayeeInfoChange)
    bus && bus.on('init:pay:plan:columns', this.handleInitColumns)
    bus && bus.watch('bill:specification:change', this.handleSpecificationChange)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus && bus.un('dimension:currency:change:payPlan:change', this.handleCurrencyChange)
    bus && bus.un('payeeInfo:isMultiplePayee', this.handlePayeeInfoChange)
    bus && bus.un('init:pay:plan:columns', this.handleInitColumns)
    bus && bus.un('bill:specification:change', this.handleSpecificationChange)
  }

  componentWillMount() {
    const { template, dataSource, canPay, singleCanPay = false, bus, editable } = this.props
    this.fnInitColumns(template)
    if (singleCanPay) {
      this.fnInitPayMoney(dataSource, singleCanPay)
    } else {
      this.fnInitPayMoney(dataSource, canPay)
    }
  }

  componentDidUpdate(preProps: Props) {
    const { isModify } = this.props
    if (isModify !== preProps.isModify) {
      const { template } = preProps
      this.fnInitColumns(template)
    }
  }

  handleCurrencyChange = currency => {
    this.payPlanStore.changeObjsMoney(currency)
  }

  fnInitPayMoney = (dataSource: any, canPay: boolean) => {
    let payMoneyBig: any = 0
    const value = dataSource.form
    const details = getNodeValueByPath(value, 'details')
    const { payDetail } = getDetailCalculateMoney(details || [], dataSource.formType)
    if (dataSource.formType === 'expense' && details) {
      const { writtenOffRecords = [] } = this.props
      const writtenOffMoneyBig = total(writtenOffRecords.map((v: any) => v.amount || 0))
      const expenseMoneyBig = typeof payDetail.value === 'object' ? payDetail.value.standard : payDetail.value
      const companyPayMoneyBig = getDetailsMoney(details, 'companyRealPay')
      payMoneyBig = new MoneyMath(expenseMoneyBig).minus(writtenOffMoneyBig).minus(companyPayMoneyBig).value as any
    }
    if (dataSource.formType === 'loan' || (dataSource.formType === 'requisition' && canPay)) {
      payMoneyBig = value['loanMoney'] || 0
    } else if (dataSource.formType === 'payment' && canPay) {
      payMoneyBig = value.payMoney
    }
    const isNe = isNegat(payMoneyBig)
    const myValue = Math.abs(typeof payMoneyBig === 'object' ? payMoneyBig.standard : payMoneyBig)
    this.payPlanStore.isNe = isNe
    this.payPlanStore.myValue = myValue
  }

  checkLegalEntityColumn = columns => {
    const { objs, supportPlanByApportion, paymentPlanByApportion } = this.payPlanStore
    // 是否是按金额多收款
    const { multiplePayeesMode, payPlanMode } = this.props
    const isPayPlanMode = multiplePayeesMode && payPlanMode
    // 法人实体字段是否有值：
    const haveLegalEntity = objs.some(el => el.E_system_支付计划_legalEntity)
    // 是否该展示法人实体列：
    // 场景1：勾选paymentPlanByApportion，且法人实体有值时，需要展示
    // 场景2：没有勾选时，切到了按金额多收款，如果法人实体有值，需要展示
    if (supportPlanByApportion && haveLegalEntity && paymentPlanByApportion) {
      const moneyIndex = columns.findIndex(el => el.dataIndex === 'money')
      columns.splice(moneyIndex, 0, {
        label: i18n.get('法人实体'),
        title: i18n.get('法人实体'),
        dataIndex: 'E_system_支付计划_legalEntity',
        field: 'E_system_支付计划_legalEntity',
        type: 'legalEntity',
        editable: true
      })
    } else {
      const index = columns.findIndex(el => el.dataIndex === 'E_system_支付计划_legalEntity')
      if (index >= 0) columns.splice(index, 1)
    }
  }

  fnInitColumns(template: any) {
    const { editable, baseDataPropertiesMap, flowId, isModify, dataSource, billSpecification, repay } = this.props
    let columns = formatColumn(template, { editable, baseDataPropertiesMap })
    // 检查是否该展示法人实体列
    this.checkLegalEntityColumn(columns)
    if (repay) {
      columns = columns.filter((el: any) => el.field !== 'receiptIds' && el.field !== 'receiptActions')
    }
    const displayColumns = formatCoumnForDisplay({
      editable,
      isModify,
      columns,
      flowId,
      dataSource,
      payPlanStore: this.payPlanStore,
      billSpecification,
      onDelete: this.handleDelete
    })
    this.payPlanStore.updateColumns(columns)
    this.payPlanStore.updateDisplayColumns(displayColumns)
  }

  handleDelete = (record: ObjFormIF) => {
    this.payPlanStore.deleteObjs([record])
  }

  handleAdd = () => {
    const { dimentionCurrencyInfo } = this.props
    this.payPlanStore.addOneObj(dimentionCurrencyInfo)
  }

  handleBatchAdd = async () => {
    const { flowId, editable, dimentionCurrencyInfo, billSpecification } = this.props
    const templateid = billSpecification.id
    const result = await api.open(
      getBoolVariation('new_version_of_payment_account', false) ? '@bills:SelectPayeePopup' : '@bills:SelectPayeeModal',
      {
        templateid,
        flowId,
        isModify: editable,
        multiple: true
      }
    )
    this.payPlanStore.updateObjs(result as ObjFormIF[], dimentionCurrencyInfo)
  }

  handleBatchImport = () => {
    const { billSpecification } = this.props
    const templateid = billSpecification.id
    api
      .open('@bills:ImportDetailByExcel', {
        flag: { templateid },
        type: 'planPay',
        noticeMsg: i18n.get(
          '提示：仅支持匹配系统中已录入的收款账户。如果需要导入新的收款账户，请先在「档案设置-收款账户」中添加！'
        )
      })
      .then((data: any) => {
        const { dimentionCurrencyInfo } = this.props
        this.payPlanStore.updateObjsWithAmount(data as ObjFormIF[], dimentionCurrencyInfo)
      })
  }

  handleSelect = (selectedRowKeys: string[], selectedRows: ObjFormIF[]) => {
    this.payPlanStore.updateSelectObjs(selectedRows)
    const { handleSelect } = this.props
    handleSelect?.(selectedRows)
  }

  // 支付计划批量下载
  handleDowload = (rowSelection: any) => {
    const { dataSource } = this.props
    if (rowSelection.selectedRowKeys.length > 0) {
      let receiptIdArr = []
      let receiptIdList = []
      rowSelection.selectedRowKeys.forEach(el => {
        const new_PayPlan = dataSource?.form?.payPlan.filter(item => item.dataLinkId === el)
        if (new_PayPlan[0].receiptId !== undefined) {
          if (new_PayPlan[0].receiptId.length > 0) {
            receiptIdArr.push(new_PayPlan)
            new_PayPlan[0].receiptId.forEach(element => {
              receiptIdList.push(element)
            })
          }
        }
      })
      // 如果拿到的回单数组长度大于0，则有回单文件
      if (receiptIdArr.length > 0) {
        downloadReceipt(receiptIdList)
      } else {
        message.warning(i18n.get('所选择支付记录/计划无回单文件'))
      }
    } else {
      message.info(i18n.get('请选择支付记录'))
    }
  }

  handleChangeApportionCheckbox = checked => {
    this.payPlanStore.paymentPlanByApportion = checked
  }

  handleCheckboxOnChange = checked => {
    // 更新checkbox状态
    this.handleChangeApportionCheckbox(checked)
    // 更新支付计划数据
    this.payPlanStore.splitPayPlanWithApportion(this.payPlanStore.writtenOffObjs)
    // 更新支付计划表格列数据
    this.fnInitColumns(this.props.template)
  }

  handleInitColumns = () => {
    this.fnInitColumns(this.props.template)
  }

  handlePayeeInfoChange = (mltiplePayeeObj, isInit) => {
    const { editable } = this.props
    // 多收款人状态判断
    const { multiplePayeesMode, payPlanMode, payeePayPlan } = mltiplePayeeObj
    const payPlanByDetail = editable && multiplePayeesMode && !payPlanMode && !payeePayPlan
    if (multiplePayeesMode && payPlanMode) {
      this.handleCheckboxOnChange(false)
    } else {
      const checked = isInit ? this.payPlanStore.paymentPlanByApportion : false
      this.handleCheckboxOnChange(checked)
    }
    // if (multiplePayeesMode && (payPlanByDetail || payeePayPlan)) this.handleCheckboxOnChange(false)
    // 复选框赋值
    this.payPlanStore.showApportionCheckbox = payPlanByDetail
  }

  render() {
    const {
      errorMsg,
      editable,
      repay,
      isModify,
      dataSource,
      isHaveRecipt,
      dimentionCurrencyInfo,
      multiplePayeesMode
    } = this.props
    const symbol = getV(dimentionCurrencyInfo, 'currency.symbol')
    const planSymbol = getV(this.payPlanStore, 'objs[0].money.standardSymbol')
    const components = {
      body: {
        row: EditableFormRow,
        cell: EditableCell
      }
    }
    const {
      selectKeys,
      isNe,
      myValue,
      totalMoney,
      objs,
      displayColumns,
      showApportionCheckbox,
      supportPlanByApportion
    } = this.payPlanStore
    const rowSelection = {
      selectedRowKeys: selectKeys,
      getCheckboxProps: record => {
        return {
          disabled: repay && record.payPlanState !== 'REEXCHANGE',
          name: record.name
        }
      },
      onChange: this.handleSelect
    }
    const unplannedAmount = Number(myValue || 0) - Number(totalMoney) + ''
    const isReady = isHaveRecipt || dataSource?.state === 'paid' || dataSource?.state === 'archived'
    const tableData = toJS(objs)
    // 单收款只读态金额展示
    const flag = !multiplePayeesMode && !editable && tableData?.length === 1
    const all_amount = flag ? tableData?.[0]?.money?.standard : myValue
    const unplanned_amount = flag ? 0 : unplannedAmount
    return (
      <div className="table_wrapper">
        {!repay && (
          <div className="table_header-title">
            {isReady ? i18n.get('支付计划/记录') : i18n.get('支付计划')}
            {objs.length ? `(${objs.length})` : null}
            {isReady && (
              <span onClick={() => this.handleDowload(rowSelection)} className="file-download">
                {i18n.get('批量下载')}
              </span>
            )}
          </div>
        )}
        {!repay && (
          <div
            className={classnames('summary title', {
              warning: errorMsg,
              'option-line': !isIE(),
              'jc-sb': !isIE(),
              'pos-r': isIE()
            })}
          >
            <div className="option-line">
              <span className="title">{i18n.get('支付金额总计：')}</span>
              <Money
                isNegative={isNe || false}
                valueSize={14}
                currencySymbol={symbol || planSymbol}
                value={all_amount || 0}
              />
            </div>
            <div className={classnames('option-line', { 'ie-layout-wrap pos-a ': isIE() })}>
              <span className="title">{i18n.get('未计划金额：')}</span>
              <Money value={unplanned_amount} currencySymbol={symbol || planSymbol} valueSize={14} />
            </div>
          </div>
        )}
        {errorMsg && (
          <div className="summary_error">{errorMsg || i18n.get('已计划支付金额 > 总支付金额 , 请修改')}</div>
        )}
        <Table
          dataSource={toJS(objs)}
          columns={toJS(displayColumns)}
          components={components}
          pagination={{ pageSize: getTablePageSize(editable), simple: true, hideOnSinglePage: true }}
          rowSelection={repay || editable || isReady ? rowSelection : null} // 有回单的部分完成  全部完成需要多选框
          scroll={{ x: true }}
        />
        {isModify && editable && (
          <TableAction
            hideOnSinglePage={objs.length <= getTablePageSize(editable)}
            payPlanStore={this.payPlanStore}
            dimentionCurrencyInfo={dimentionCurrencyInfo}
          />
        )}
        {isModify && editable && (
          <div className="footer">
            <div className="add" onClick={this.handleAdd}>
              <EKBIcon name="#EDico-plus-default" className="icon" />
              <span>{i18n.get('添加支付计划')}</span>
            </div>
            <div className="batch_btns">
              <div className="batch_add" onClick={this.handleBatchAdd}>
                {i18n.get('批量添加支付计划')}
              </div>
              <div className="batch_import" onClick={this.handleBatchImport}>
                {i18n.get('批量导入支付计划')}
              </div>
            </div>
          </div>
        )}
        {showApportionCheckbox && supportPlanByApportion && (
          <Checkbox
            className="mt-8"
            onChange={e => this.handleCheckboxOnChange(e.target.checked)}
            checked={this.payPlanStore.paymentPlanByApportion}
          >
            {i18n.get('包含分摊明细')}
          </Checkbox>
        )}
      </div>
    )
  }
}

function getDetailsMoney(details: any, key: string) {
  return total(details.map((v: any) => v.feeTypeForm[key]).filter((o: any) => o))
}
