/*
 * @Author: Onein
 * @Date: 2019-09-23 11:27:25
 * @Last Modified by: Onein
 * @Last Modified time: 2019-10-09 17:47:25
 */

import { app as api } from '@ekuaibao/whispered'
import { getBoolVariation } from '../../../lib/featbit'

export function getColumnType(type: string) {
  return type.toUpperCase()
}

const openDimension = params => {
  const data = params.data ? { selectedNode: params.data } : undefined
  const name = 'basedata.Dimension.法人实体'
  return api.invokeService('@common:get:staff:dimension', { name }).then(res => {
    return api.open('@layout:SelectTreeModal', {
      dataset: res.items,
      dataIndex: '法人实体',
      title: '法人实体',
      data,
      searchType: 'select',
      selectRange: 'all',
      entityId: name
    })
  })
}

export function openPayeeInfo(params) {
  return api.open(
    getBoolVariation('new_version_of_payment_account', false) ? '@bills:SelectPayeePopup' : '@bills:SelectPayeeModal',
    params
  )
}

export function getTablePageSize(editable: boolean) {
  return editable ? 10 : 100
}

export const funcMap = {
  PAYEEINFO: openPayeeInfo, // 收款信息
  LEGALENTITY: openDimension // 法人实体
}

export const fixedColumnCount = 4
