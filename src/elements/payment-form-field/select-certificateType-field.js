import { Select } from '@hose/eui'
import React from 'react'
import { T } from '@ekuaibao/i18n'

export default class SelectCertificateTypeField extends React.PureComponent {
  handleCertificateTypeChange = val => {
    this.props.onChange?.(val)
  }

  render() {
    const { placeholder, dataSource, value, disabled } = this.props
    return (
      <div className="dis-f ai-c select-bank">
        <Select
          id="certificateType"
          className="w-100b"
          onChange={this.handleCertificateTypeChange}
          showSearch
          disabled={disabled}
          placeholder={placeholder}
          filterOption={(input, option) => {
            return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }}
          value={value}
          dropdownClassName="dropdown_certificateType"
          getPopupContainer={node => node.parentNode}
        >
          {dataSource?.items?.map(value => {
            const { code, name } = value
            return (
              <Select.Option key={name} value={code}>
                {i18n.get(name)}
              </Select.Option>
            )
          })}
        </Select>
      </div>
    )
  }
}

export class SelectBranchTypeField extends React.PureComponent {
  onChange = val => {
    this.props.onChange?.(val)
  }

  render() {
    const { dataSource, value, disabled, onSearchBankList, isForbiddenModify, handleAdvancedSearch } = this.props
    return (
      <span id="branch">
        <Select
          className="w-100b"
          getPopupContainer={triggerNode => triggerNode.parentNode}
          disabled={disabled}
          showSearch
          value={value}
          onChange={this.onChange}
          placeholder={i18n.get('请输入至少6个汉字的银行网点')}
          defaultActiveFirstOption={false}
          filterOption={false}
          notFoundContent={null}
          showArrow={true}
          onSearch={onSearchBankList}
        >
          {dataSource.map(d => (
            <Option key={d.id}>{d.name}</Option>
          ))}
        </Select>
        {!isForbiddenModify && (
          <span className="dis-f">
            <T name="找不到所需网点? 试试" />
            <span className="filter-text" onClick={disabled ? () => {} : handleAdvancedSearch}>
              <T name="条件搜索" />
            </span>
          </span>
        )}
      </span>
    )
  }
}
