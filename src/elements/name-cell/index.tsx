import React from "react"
import WWOpenDataCom from './wx-name'

interface NameCellProps {
  type: 'user' | 'department'
  alias?: boolean
  id: string
  name: React.ReactNode
}

export const NameCell = ({ type, alias = false, id, name }: NameCellProps) => {
  if (window.isInWeComISV) {
    try {
      const componentType = `${type}${alias ? 'Alias' : 'Name'}`
      return <WWOpenDataCom type={componentType} openid={id} />
    } catch {
      return <>{name}</>
    }
  }
  return <>{name}</>
}