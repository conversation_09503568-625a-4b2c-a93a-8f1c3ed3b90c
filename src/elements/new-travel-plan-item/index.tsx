import React from 'react'
import TravelItemOld from './TravelItemOld'
import TravelItemNew from './TravelItemNew'
import { getBoolVariation } from '../../lib/featbit'

interface TravelItemProps {
  line: any
  onlyOne?: boolean
  hiddenLine?: boolean
  readOnly?: boolean
  showButton?: boolean
  isNewGroup?: boolean
  showOrderButton?: boolean
  tripType?: string
  onOpen?: () => void
  onDeleteTrip?: () => void
  onShowDetail?: () => void
  onOrderClick?: () => {}
  isFirstGroup?: boolean
  isLastGroup?: boolean
  isSingleGroup?: boolean
  isOriginalTravelPlainning?: boolean
  editable?: boolean
  hiddleLeft?: boolean
  sceneTypeField?: string
  referenceStartTime?: string
  referenceEndTime?: string
  referenceStartDateSimple?: string
}

const TravelItem: React.FC<TravelItemProps> = (props) => {
  // 根据开关决定渲染哪个版本
  if (getBoolVariation('fkrd-5600_trip_UTC')) {
    return <TravelItemNew {...props} />
  }
  return <TravelItemOld {...props} />
}

export default TravelItem
