import React, { useState, useEffect } from 'react'
import { Fetch } from '@ekuaibao/fetch'
import styles from './TravelItem.module.less'
import { app as api } from '@ekuaibao/whispered'
import { Button } from 'antd'
import { Popover, Tag, Tooltip } from '@hose/eui'
import moment from 'moment'
const EKBIcon = api.require('@elements/ekbIcon')
const TRAIN = require('./images/train_new.svg')
const FLIGHT = require('./images/flight_new.svg')
const TAXI = require('./images/car_new.svg')
const HOTEL = require('./images/hotel_new.svg')
const COMMON = require('./images/common_new.svg')
const FOOD = require('./images/food_new.svg')
const DELETE = require('./images/delete.svg')
const LINE = require('./images/line_icon.svg')
import { getWeek } from '../../lib/lib-util'
import {
  FilledTipsNode,
  OutlinedDirectionDownRound,
  OutlinedEditDeleteTrash,
  OutlinedTipsInfo,
  TwoToneGeneralAirplane,
  TwoToneGeneralFood,
  TwoToneGeneralHotel,
  TwoToneGeneralInternationalBusiness,
  TwoToneGeneralTaxi,
  TwoToneGeneralTrain
} from '@hose/eui-icons'
import { getBoolVariation } from '../../lib/featbit'
import { Resource } from '@ekuaibao/fetch'
const v2CityGroup = new Resource('/api/v2/basedata/cityGroup/')
const cityFetch = new Resource('/api/v2/basedata/city/')
const cityIanaFetch = new Resource('/api/v2/basedata/city/iana/')

const TYPE_MAP = {
  用车: TAXI,
  飞机: FLIGHT,
  火车: TRAIN,
  酒店: HOTEL,
  餐饮: FOOD,
  通用: COMMON
}

const typeIconMap: Record<string, React.ReactNode> = {
  飞机: <TwoToneGeneralAirplane />,
  火车: <TwoToneGeneralTrain />,
  酒店: <TwoToneGeneralHotel />,
  餐饮: <TwoToneGeneralFood />,
  用车: <TwoToneGeneralTaxi />,
  通用: <TwoToneGeneralInternationalBusiness />
}

interface TravelItemProps {
  line: any
  onlyOne?: boolean
  hiddenLine?: boolean
  readOnly?: boolean
  showButton?: boolean
  isNewGroup?: boolean
  showOrderButton?: boolean
  tripType?: string
  onOpen?: () => void
  onDeleteTrip?: () => void
  onShowDetail?: () => void
  onOrderClick?: () => {}
}

const TravelItem: React.FC<TravelItemProps> = ({
  line,
  tripType,
  onOpen,
  onDeleteTrip,
  onShowDetail,
  readOnly,
  showButton,
  isNewGroup,
  isFirstGroup,
  isLastGroup,
  isSingleGroup,
  isOriginalTravelPlainning,
  onOrderClick,
  showOrderButton,
  hiddenLine,
  onlyOne,
  editable = true,
  hiddleLeft,
  sceneTypeField,
  referenceStartTime,
  referenceEndTime,
  referenceStartDateSimple
}) => {
  const [startCityGroupDisplay, setStartCityGroupDisplay] = useState('')
  const [endCityGroupDisplay, setEndCityGroupDisplay] = useState('')
  const [localSt, setLocalSt] = useState<any[]>([])
  const [localEt, setLocalEt] = useState<any[]>([])
  const [allCityIsChina, setAllCityIsChina] = useState(false)
  const [startDateUTCs, setStartDateUTCs] = useState<string[]>([])
  const [endDateUTCs, setEndDateUTCs] = useState<string[]>([])
  const [allStartCityWithTimezone, setAllStartCityWithTimezone] = useState('')
  const [allEndCityWithTimezone, setAllEndCityWithTimezone] = useState('')
  const [localUTC, setLocalUTC] = useState('')
  const [localIana, setLocalIana] = useState('')

  // 计算UTC偏移量的函数
  const getUtcOffset = (timestamp: number, timeZone: string) => {
    if (!timeZone) return ''
    if (allCityIsChina && localUTC === 'UTC+8') return ''
    try {
      const dtf = new Intl.DateTimeFormat('en-US', {
        timeZone,
        timeZoneName: 'shortOffset'
      })
      const parts = dtf.formatToParts(new Date(timestamp))
      const offsetPart = parts.find(p => p.type === 'timeZoneName')
      return offsetPart ? offsetPart.value.replace('GMT', 'UTC') : null
    } catch (error) {
      console.warn('该浏览器不支持设备时区详细信息获取，可能导致UTC显示异常，建议更换浏览器使用', error)
      return ''
    }
  }

  // 为日期增加时区后缀
  const calculateTimeZoneOffsets = (cities: any[], timestamp: number) => {
    const utcOffsets = cities.map(city => {
        if (city?.iana) {
          const utcOffset = getUtcOffset(timestamp, city.iana)
          return utcOffset
        }
        return null
      })
      .filter(Boolean)

    // utcOffsets中是否所有的utc都等于localUTC返回null
    if (utcOffsets.length > 0 && utcOffsets.every(utc => utc === localUTC)) {
        return null
    }
  
    return utcOffsets
  }

  // 为城市增加时区后缀
  const generateCityWithTimezone = (cities: any[], startTimestamp?: number, endTimestamp?: number, allCityData?: any[]) => {
    if (!Array.isArray(cities) || cities.length === 0) {
      return ''
    }
    //检查是否需要utc信息
    if(allCityData?.some(city => city?.type === 'cityGroup')) {
      if(localUTC === 'UTC+8') {
        // 如果本地时区是UTC+8，那么只检查非城市组数据
        const nonCityGroupData = allCityData?.filter(city => city?.type !== 'cityGroup')
        if(nonCityGroupData?.length > 0 && nonCityGroupData?.every(city => city?.iana === localIana)) {
          return ''
        }
      }
    } else {
      if(allCityData?.length > 0 && allCityData?.every(city => city?.iana === localIana)) {
        return ''
      }
    }

    return cities
      .map(city => {
        let cityLabel = city?.label || ''

        if (city?.iana && startTimestamp || endTimestamp) {
          const startUTC = getUtcOffset(startTimestamp, city.iana)
          const endUTC = endTimestamp ? getUtcOffset(endTimestamp, city.iana) : ''

          if (startUTC || endUTC) {
            const filteredStartUTC = startUTC
            const filteredEndUTC = endUTC

            if (filteredStartUTC && filteredEndUTC) {
              if (filteredStartUTC === filteredEndUTC) {
              // 起止时间时区相同显示一个
                cityLabel = `${cityLabel}<${filteredStartUTC}>`
              } else {
                cityLabel = `${cityLabel}<${filteredStartUTC},${filteredEndUTC}>`
              }
            } else if (filteredStartUTC) {
              cityLabel = `${cityLabel}<${filteredStartUTC}>`
            } else if (filteredEndUTC) {
              cityLabel = `${cityLabel}<${filteredEndUTC}>`
            }
          }
        }

        return cityLabel
      })
      .join('、')
  }

  const fetchAndProcessCityDetails = async (travelFromCity: string, travelToCity: string) => {
    const fromCityData = (travelFromCity && JSON.parse(travelFromCity)) || []
    const toCityData = (travelToCity && JSON.parse(travelToCity)) || []

    const foreignCityKeys = []
    if (fromCityData && fromCityData.length > 0) {
      for (const city of fromCityData) {
        if (city?.enLabel && !city.enLabel.includes('China')) {
          foreignCityKeys.push(city.key)
        }
      }
    }
    if (toCityData && toCityData.length > 0) {
      for (const city of toCityData) {
        if (city?.enLabel && !city.enLabel.includes('China')) {
          foreignCityKeys.push(city.key)
        }
      }
    }

    if (foreignCityKeys.length > 0) {
      try {
        const res = await cityFetch.GET('[ids]', { ids: foreignCityKeys.join(',') })

        if (res && res.items && Array.isArray(res.items)) {
          const processCityDetails = (cityData: any[], cityDetails: any[]) => {
            if (!cityData || !cityDetails || !Array.isArray(cityData) || !Array.isArray(cityDetails)) {
              return cityData
            }

            const newCityData = cityData.map(city => ({ ...city }))

            cityDetails.forEach(cityDetail => {
              if (cityDetail.fullName) {
                const countryName = cityDetail.fullName.split(',')[0]
                newCityData.forEach(city => {
                  if (city.key === cityDetail.id && !city.label.includes(`(${countryName})`)) {
                    city.label = `${city.label}(${countryName})`
                  }
                })
              }
            })

            return newCityData
          }

          const enhanceFromCityData = processCityDetails(fromCityData, res.items)
          const enhanceToCityData = processCityDetails(toCityData, res.items)

          return { enhanceFromCityData, enhanceToCityData }
        }
      } catch (error) {
        console.error('请求城市详情失败:', error)
      }
    }

    return { enhanceFromCityData: fromCityData, enhanceToCityData: toCityData }
  }

  const fetchAndProcessCityIana = async (travelFromCity: string, travelToCity: string) => {
    const fromCityData = (travelFromCity && JSON.parse(travelFromCity)) || []
    const toCityData = (travelToCity && JSON.parse(travelToCity)) || []

    // 收集所有城市的 ID
    const allCityKeys = []
    if (fromCityData && fromCityData.length > 0) {
      for (const city of fromCityData) {
        if (city?.key) {
          allCityKeys.push(city.key)
        }
      }
    }
    if (toCityData && toCityData.length > 0) {
      for (const city of toCityData) {
        if (city?.key) {
          allCityKeys.push(city.key)
        }
      }
    }

    if (allCityKeys.length > 0) {
      try {
        const res = await cityIanaFetch.POST('', { ids: allCityKeys })

        if (res && res.items && Array.isArray(res.items)) {
          const processCityIana = (cityData: any[], ianaDetails: any[]) => {
            if (!cityData || !ianaDetails || !Array.isArray(cityData) || !Array.isArray(ianaDetails)) {
              return cityData
            }

            const newCityData = cityData.map(city => ({ ...city }))

            ianaDetails.forEach(ianaDetail => {
              if (ianaDetail.iana) {
                newCityData.forEach(city => {
                  if (city.key === ianaDetail.id) {
                    city.iana = ianaDetail.iana
                  }
                })
              }
            })

            return newCityData
          }

          const enhanceFromCityData = processCityIana(fromCityData, res.items)
          const enhanceToCityData = processCityIana(toCityData, res.items)

          return { enhanceFromCityData, enhanceToCityData }
        }
      } catch (error) {
        console.error('请求城市IANA信息失败:', error)
      }
    }

    return { enhanceFromCityData: fromCityData, enhanceToCityData: toCityData }
  }

  const handleGetPrice = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e && e.stopPropagation && e.stopPropagation()
    e && e.preventDefault && e.preventDefault()
    onOpen && onOpen(e)
  }
  const handleDeleteTrip = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e && e.stopPropagation && e.stopPropagation()
    e && e.preventDefault && e.preventDefault()
    editable && onDeleteTrip && onDeleteTrip(e)
  }
  const handleShowDetail = () => {
    onShowDetail && onShowDetail()
  }

  const handleOrderClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e && e.stopPropagation && e.stopPropagation()
    e && e.preventDefault && e.preventDefault()
    onOrderClick && onOrderClick()
  }

  let { type, travelToCity, travelFromCity, startDate, endDate, referencePrice, scene, sceneList } = line
  let dateString
  type = tripType ? tripType : type
  const startDateStr = moment(startDate).format(i18n.get('MM月DD日'))
  if (startDate && endDate) {
    const endDateStr = moment(endDate).format(i18n.get('MM月DD日'))
    const daysNumber = moment(endDate).diff(startDate, 'days')
    const desc =
      type === '酒店'
        ? i18n.get('共 {days} 天 {nights} 晚', { days: daysNumber + 1, nights: daysNumber })
        : i18n.get('共 {days} 天', { days: daysNumber + 1 })
    dateString = (
      <>
        <span>
          {`${referenceStartTime ? referenceStartTime : startDateStr}`}
          {isOriginalTravelPlainning && startDateUTCs?.length > 0 && (
            <Tag className="ml-4" color="neu" size="small">
              {startDateUTCs?.length > 1 ? i18n.get('多时区') : startDateUTCs?.length > 0 ? startDateUTCs?.[0] : ''}
            </Tag>
          )}
        </span>
        <span>
          {` - ${referenceEndTime ? referenceEndTime : endDateStr}`}
          {isOriginalTravelPlainning && endDateUTCs?.length > 0 && (
            <Tag className="ml-4" color="neu" size="small">
              {endDateUTCs?.length > 1 ? i18n.get('多时区') : endDateUTCs?.length > 0 ? endDateUTCs?.[0] : ''}
            </Tag>
          )}
        </span>
        <span className="date-line">&nbsp;</span>
        <span className="date-count">{desc}</span>
      </>
    )
  } else {
    dateString = (
      <>
        <span>{`${referenceStartTime ? referenceStartTime : startDateStr}`}</span>
        <span>
          {` ${getWeek(startDate)}`}
          {isOriginalTravelPlainning && startDateUTCs?.length > 0 && (
            <Tag className="ml-4" color="neu" size="small">
              {startDateUTCs?.length > 1 ? i18n.get('多时区') : startDateUTCs?.length > 0 ? startDateUTCs?.[0] : ''}
            </Tag>
          )}
        </span>
      </>
    )
  }

  // 保留原始数据同步
  useEffect(() => {
    const fromCityData = (travelFromCity && JSON.parse(travelFromCity)) || []
    const toCityData = (travelToCity && JSON.parse(travelToCity)) || []
    // 检查 fromCityData 和 toCityData 中是否有不包含 "China" 的城市
    const checkChinaInclusion = (cityData: any[]) => {
      if (!Array.isArray(cityData) || cityData.length === 0) {
        return true
      }
      return cityData.every(city => {
        return city?.enLabel && city.enLabel.includes('China')
      })
    }

    const fromCityAllChina = checkChinaInclusion(fromCityData)
    const toCityAllChina = checkChinaInclusion(toCityData)
    const allCityIsChina = fromCityAllChina && toCityAllChina

    setAllCityIsChina(allCityIsChina)

  }, [travelFromCity, travelToCity])

  // 处理城市国家信息和IANA时区信息
  useEffect(() => {
    const processCityDetails = async () => {
      const { enhanceFromCityData, enhanceToCityData } = await fetchAndProcessCityDetails(travelFromCity, travelToCity)
      setLocalSt(enhanceFromCityData)
      setLocalEt(enhanceToCityData)
      return { enhanceFromCityData, enhanceToCityData }
    }

    const processIanaData = async (cityDetailsData: any) => {
      const { enhanceFromCityData, enhanceToCityData } = await fetchAndProcessCityIana(travelFromCity, travelToCity)

      // 合并IANA时区信息到现有城市数据
      const mergeIanaData = (cityData: any[], ianaData: any[]) => {
        if (!Array.isArray(cityData) || !Array.isArray(ianaData)) {
          return cityData
        }

        return cityData.map(city => {
          const ianaCity = ianaData.find(iana => iana.key === city.key)
          return {
            ...city,
            ...(ianaCity?.iana && { iana: ianaCity.iana })
          }
        })
      }

      // 使用从processCityDetails获取的城市数据，如果没有则使用原始数据
      const currentFromCityData = cityDetailsData?.enhanceFromCityData || (travelFromCity && JSON.parse(travelFromCity)) || []
      const currentToCityData = cityDetailsData?.enhanceToCityData || (travelToCity && JSON.parse(travelToCity)) || []

      const finalFromCityData = mergeIanaData(currentFromCityData, enhanceFromCityData)
      const finalToCityData = mergeIanaData(currentToCityData, enhanceToCityData)

      setLocalSt(finalFromCityData)
      setLocalEt(finalToCityData)

      // 生成hover内时区信息
      if (startDate || endDate) {
        const startTimestamp = new Date(startDate).getTime()
        const endTimestamp = new Date(endDate).getTime()
        const allCityData = [...finalFromCityData, ...finalToCityData]
        const allStartCityWithTimezone = generateCityWithTimezone(finalFromCityData, startTimestamp, endTimestamp,allCityData)
        const allEndCityWithTimezone = generateCityWithTimezone(finalToCityData, startTimestamp, endTimestamp,allCityData)


        setAllStartCityWithTimezone(allStartCityWithTimezone)
        setAllEndCityWithTimezone(allEndCityWithTimezone)
      }
      const timeZoneTemp = Intl.DateTimeFormat().resolvedOptions().timeZone
      const localUTCTemp = getUtcOffset(new Date().getTime(), timeZoneTemp)
      // 生成日期后的时区信息
      if (startDate) {
        const startTimestamp = new Date(startDate).getTime()
        const startUTCs = calculateTimeZoneOffsets(finalFromCityData, startTimestamp)
        let uniqueStartUTCs = Array.from(new Set(startUTCs)) //去重
        if(finalFromCityData?.length > 0 && finalFromCityData?.[0]?.type === 'cityGroup' && localUTCTemp !== 'UTC+8') {
          uniqueStartUTCs = ["UTC+8"]
        }
        setStartDateUTCs(uniqueStartUTCs)
      }
      if (endDate) {
        const endTimestamp = new Date(endDate).getTime()
        //结束日期utc结合出发地来计算
        const endUTCs = calculateTimeZoneOffsets(finalFromCityData, endTimestamp)
        let uniqueEndUTCs = Array.from(new Set(endUTCs)) //去重
        if(finalToCityData?.length > 0 && finalToCityData?.[0]?.type === 'cityGroup' && localUTCTemp !== 'UTC+8') {
          uniqueEndUTCs = ["UTC+8"]
       }
        setEndDateUTCs(uniqueEndUTCs)
      }
    }

    const executeSequentially = async () => {
      const cityDetailsData = await processCityDetails()
      await processIanaData(cityDetailsData)
    }

    executeSequentially()
  }, [travelFromCity, travelToCity, line, localUTC])

  useEffect(() => {
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    setLocalIana(timeZone)
    const localUTC = getUtcOffset(new Date().getTime(), timeZone)
    setLocalUTC(localUTC || 'UTC+8')
  }, [])

  const st = localSt?.length > 0 ? localSt : (travelFromCity && JSON.parse(travelFromCity)) || []
  const et = localEt?.length > 0 ? localEt : (travelToCity && JSON.parse(travelToCity)) || []
  const sCity = st[0]
  const eCity = et[0]
  const allStartCity = st?.map(item => item?.label)?.join('、')
  const allEndCity = et?.map(item => item?.label)?.join('、')
  let startCity = i18n.currentLocale === 'en-US' ? sCity?.enLabel || sCity?.label || '' : sCity?.label || ''
  let endCity = i18n.currentLocale === 'en-US' ? eCity?.enLabel || eCity?.label || '' : eCity?.label || ''
  let tooltipStr = ''
  if (Fetch.lang === 'zh-CN') {
    startCity = startCity.split('/').pop()
    endCity = endCity.split('/').pop()
  }
  if (st.length > 1) {
    startCity += `(+${st.length - 1})`
    tooltipStr = st?.map((v: any) => v.label)?.join('、')
  }
  if (et.length > 1) {
    endCity += `(+${et.length - 1})`
  }
  let startMonthStr = ''
  let startdayStr = ''
  if (isNewGroup) {
    startMonthStr = moment(startDate).month() + 1 + ''
    const startdayNum = moment(startDate).date()
    startdayStr = startdayNum > 9 ? `${startdayNum}` : `0${startdayNum}`
  }

  useEffect(() => {
    if (st?.[0]?.type !== 'cityGroup' && et?.[0]?.type !== 'cityGroup') {
      setStartCityGroupDisplay('')
      setEndCityGroupDisplay('')
      return
    }
    const fetchData = async () => {
      const userInfo = api.getState('@common')?.userinfo?.data
      
      // 获取城市IANA时区信息的函数
      const getCityIanaInfo = async (cityIds: string[]) => {
        if (!cityIds.length) return []
        try {
          const res = await cityIanaFetch.POST('', { ids: cityIds })
          return res?.items || []
        } catch (error) {
          console.error('获取城市IANA信息失败:', error)
          return []
        }
      }

      // 为城市添组加时区信息
      const addUtcToCities = (cities: any[], ianaData: any[], startTimestamp?: number, endTimestamp?: number) => {
        return cities.map(city => {
          const ianaInfo = ianaData.find(iana => iana.id === city.id)
          let cityName = city.name
          
          if (ianaInfo?.iana && (startTimestamp || endTimestamp)) {
            const startUTC = startTimestamp ? getUtcOffset(startTimestamp, ianaInfo.iana) : null
            const endUTC = endTimestamp ? getUtcOffset(endTimestamp, ianaInfo.iana) : null
            
            if (startUTC && endUTC) {
              const filteredStartUTC = startUTC
              const filteredEndUTC = endUTC
              
              if (filteredStartUTC && filteredEndUTC) {
                if (filteredStartUTC === filteredEndUTC) {
                  if(filteredStartUTC === localUTC) {
                    cityName = `${cityName}`
                  } else {
                    cityName = `${cityName}<${filteredStartUTC}>`
                  }
                } else {
                  cityName = `${cityName}<${filteredStartUTC},${filteredEndUTC}>`
                }
              } else if (filteredStartUTC) {
                cityName = `${cityName}<${filteredStartUTC}>`
              } else if (filteredEndUTC) {
                cityName = `${cityName}<${filteredEndUTC}>`
              }
            } else if (startUTC && startUTC !== localUTC) {
              cityName = `${cityName}<${startUTC}>`
            } else if (endUTC && endUTC !== localUTC) {
              cityName = `${cityName}<${endUTC}>`
            }
          }
          
          return cityName
        })
      }
      
      if (st?.[0]?.type === 'cityGroup') {
        await v2CityGroup
          .GET('findLimitedCities', {
            cityGroupId: st?.[0]?.key,
            staffId: userInfo.staff.id,
            count: 3000
          })
          .then(async res => {
            if (res?.items?.length) {
              const cityIds = res.items.map((item: any) => item.id)
              const ianaData = await getCityIanaInfo(cityIds)
              const citiesWithUtc = addUtcToCities(res.items, ianaData, 
                startDate ? new Date(startDate).getTime() : undefined,
                endDate ? new Date(endDate).getTime() : undefined
              )
              const result = citiesWithUtc.join('、')
              setStartCityGroupDisplay(result)
            }
          })
      }
      
      if (et?.[0]?.type === 'cityGroup') {
        await v2CityGroup
          .GET('findLimitedCities', {
            cityGroupId: et?.[0]?.key,
            staffId: userInfo.staff.id,
            count: 3000
          })
          .then(async res => {
            if (res?.items?.length) {
              const cityIds = res.items.map((item: any) => item.id)
              const ianaData = await getCityIanaInfo(cityIds)
              const citiesWithUtc = addUtcToCities(res.items, ianaData,
                startDate ? new Date(startDate).getTime() : undefined,
                endDate ? new Date(endDate).getTime() : undefined
              )
              const result = citiesWithUtc.join('、')
              setEndCityGroupDisplay(result)
            }
          })
      }
    }
    fetchData()
  }, [st, et, startDate, endDate, localUTC])

  return (
    <section className={styles['travel-item']} onClick={handleShowDetail}>
      {isOriginalTravelPlainning ? (
        <>
          {!isSingleGroup && (
            <div className="left-part-new">
              {isNewGroup && !isFirstGroup && <div className="line-icon-front" />}
              {isNewGroup &&
                (!isFirstGroup && !isLastGroup ? (
                  <OutlinedDirectionDownRound style={{ margin: readOnly ? '3px 0' : '4px 0 3px' }} />
                ) : (
                  <FilledTipsNode style={{ margin: readOnly ? '3px 0' : '4px 0 3px', color: '#2555FF' }} />
                ))}
              {!isSingleGroup && !isLastGroup && <div className="line-icon" />}
            </div>
          )}
          <div className="right-part-new">
            {isNewGroup && (
              <div className="date-line" style={{ marginTop: isFirstGroup ? 0 : 16 }}>
                {referenceStartDateSimple ? referenceStartDateSimple : `${startMonthStr}.${startdayStr} ${getWeek(startDate)}`}
              </div>
            )}
            <div className="trip-card">
              <div className="title-line">
                <div className="title">
                  <div style={{ fontSize: 24, marginRight: 4 }}>{typeIconMap[type]}</div>
                  <div className="trip-type">{type}</div>
                </div>
                <div onClick={e => handleDeleteTrip(e)}>
                  {!readOnly && <OutlinedEditDeleteTrash style={{ fontSize: 16, color: 'rgba(29, 33, 41, 0.60)' }} />}
                </div>
              </div>
              <div className="info-box">
                <div className="city-line">
                  <span className="start">
                    {startCityGroupDisplay ? (
                      <>
                        {startCity}
                        <Popover content={<div style={{ maxHeight: 480, maxWidth: 400 }}>{startCityGroupDisplay}</div>}>
                          <OutlinedTipsInfo
                            style={{
                              fontSize: 16,
                              marginLeft: 4,
                              marginBottom: '-1px',
                              color: 'rgba(29, 33, 41, 0.40)'
                            }}
                          />
                        </Popover>
                      </>
                    ) : (
                      st?.length === 1 && !allStartCityWithTimezone && startCity
                    )}
                    {!startCityGroupDisplay && (allStartCityWithTimezone || st?.length > 1) && <Tooltip title={allStartCityWithTimezone || allStartCity}>{startCity}</Tooltip>}
                  </span>
                  {!endCity}
                  {endCity && <span>-</span>}
                  {endCity && (
                    <span className="end">
                      {endCityGroupDisplay ? (
                        <>
                          {endCity}
                          <Popover content={<div style={{ maxHeight: 480, maxWidth: 400 }}>{endCityGroupDisplay}</div>}>
                            <OutlinedTipsInfo
                              style={{
                                fontSize: 16,
                                marginLeft: 4,
                                marginBottom: '-1px',
                                color: 'rgba(29, 33, 41, 0.40)'
                              }}
                            />
                          </Popover>
                        </>
                      ) : (
                        et?.length === 1 && !allEndCityWithTimezone && endCity
                      )}
                      {!endCityGroupDisplay && (allEndCityWithTimezone || et?.length > 1) && <Tooltip title={allEndCityWithTimezone || allEndCity}>{endCity}</Tooltip>}
                    </span>
                  )}
                </div>
                <div className="detail-line">
                  <div>{dateString}</div>
                  {(sceneList?.length > 0 || (!readOnly && editable && sceneTypeField)) && (
                    <div style={{ height: 20 }}>
                      <div className="divider" />
                    </div>
                  )}
                  <div className="scene-line">
                    {sceneList?.length > 0
                      ? Array.isArray(sceneList)
                        ? (sceneList?.map(item => item?.sceneName) || []).join('、')
                        : scene?.sceneName
                      : !readOnly && editable
                      ? sceneTypeField === 'open_required' && <div style={{ color: '#F53F3F' }}>请选择场景(必填)</div>
                      : sceneTypeField === 'open_notRequired' && (
                          <div style={{ color: 'rgba(29, 33, 41, 0.50)' }}>请选择场景(非必填)</div>
                        )}
                  </div>
                </div>
                {(!!referencePrice || showButton) && (
                  <div className="price-line">
                    {!!referencePrice && (
                      <div className="price">
                        {i18n.get('参考报价:{money}元/', { money: referencePrice })}
                        {i18n.get(type === '酒店' ? '晚' : '张')}
                      </div>
                    )}
                    {showButton && ['飞机', '酒店', '火车'].includes(type) && !readOnly && (
                      <div className="btn-get-price" style={{ marginLeft: 8 }} onClick={handleGetPrice}>
                        {i18n.get(referencePrice > 0 ? '重新获取' : '获取参考报价')}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          {!hiddenLine && (
            <div className="left-line">
              {isNewGroup && (
                <div className="date">
                  {startMonthStr}
                  {i18n.currentLocale !== 'en-US' && i18n.get('月')}
                </div>
              )}
              {isNewGroup && <div className="month">{startdayStr}</div>}
              {!onlyOne && <div className="line-icon" />}
            </div>
          )}
          <div className="right-part">
            <div className="info-top">
              <div className="info-top-first-line">
                <img className="info-top-icon" src={TYPE_MAP[type] || COMMON} />
                <div className="info-top-city">
                  <span className="start">{startCity}</span>
                  {!endCity && (
                    <small>
                      {Array.isArray(sceneList)
                        ? (sceneList?.map(item => item?.sceneName) || []).join('、')
                        : scene?.sceneName}
                    </small>
                  )}
                  {endCity && <img className="end-line" src={LINE} />}
                  {endCity && <span className="end">{endCity}</span>}
                </div>
              </div>
              {endCity && (
                <div className="scene-line">
                  <small>
                    {Array.isArray(sceneList)
                      ? (sceneList?.map(item => item?.sceneName) || []).join('、')
                      : scene?.sceneName}
                  </small>
                </div>
              )}
              <div className="info-top-second-line">
                <div className="info-top-name">{i18n.get(type)}</div>
                <div className="info-top-date">{dateString}</div>
              </div>
              {showOrderButton && (
                <Button type="primary" className="travel-item-button" onClick={handleOrderClick}>
                  {i18n.get('订购')}
                </Button>
              )}
              {!readOnly && (
                <div onClick={e => handleDeleteTrip(e)}>
                  {!editable ? (
                    <Tooltip title="该行程已经发生订购，若想删除行程， 请先取消对应的订单" placement="topLeft">
                      <OutlinedEditDeleteTrash className="info-top-delete-icon disabled" />
                    </Tooltip>
                  ) : (
                    <OutlinedEditDeleteTrash className="info-top-delete-icon" />
                  )}
                </div>
              )}
            </div>
            <div className="price-wrapper">
              {!!referencePrice && (
                <div className="price-wrapper-money">
                  {i18n.get('参考报价:{money}元/', { money: referencePrice })}
                  {i18n.get(type === '酒店' ? '晚' : '张')}
                </div>
              )}
              {showButton && ['飞机', '酒店', '火车'].includes(type) && !readOnly && (
                <div className="price-wrapper-action" onClick={handleGetPrice}>
                  {i18n.get(referencePrice > 0 ? '重新获取' : '获取报价')}
                </div>
              )}
            </div>
            <div className="margin-part" />
          </div>
        </>
      )}
    </section>
  )
}

export default TravelItem
