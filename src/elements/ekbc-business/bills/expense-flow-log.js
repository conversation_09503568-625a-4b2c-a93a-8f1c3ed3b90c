import './expense-flow-log.less'
import React, { PureComponent } from 'react'
import { Menu, Dropdown, Icon, message } from 'antd'
import moment from 'moment'
import { fnMapLogs, flowStateMap } from './flow-log-item'
import { getV } from '@ekuaibao/lib/lib/help'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { isArray } from '@ekuaibao/helpers'
import BillsVersion from './bills-version'
import _debounce from 'lodash/debounce'
import { HistoryApi } from '../../../plugins/bills/history/api'
import { isAIAgentNode, getAIAgentObj } from '../../ai-agent-utils'

const ApprovalHistoryFilterList = [
  { label: '全部', value: 'ALL', type: 'all' },
  { label: '包含评论的历史', value: 'CONTAIN_COMMENT', type: 'comment' },
  { label: '审批事件的历史', value: 'APPROVAL_EVENT', type: 'approve' },
  { label: '人员审批的历史', value: 'STAFF_APPROVAL', type: 'staff_approval' }
]

@EnhanceConnect(state => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  KA_DETAIL_LAYOUT_CONFIG: state['@common'].powers.KA_DETAILS_LAYOUT // KA-单据详情展示
}))
export default class ExpenseFlowLogView extends PureComponent {
  menus = [
    { label: i18n.get('包含评论的历史'), type: 'comment', value: 'CONTAIN_COMMENT' },
    { label: i18n.get('审批事件的历史'), type: 'approve', value: 'APPROVAL_EVENT' },
    { label: i18n.get('人员审批的历史'), type: 'staff_approval', value: 'STAFF_APPROVAL' },
    { label: i18n.get('有效审批的历史'), type: 'effective', value: 'EFFECTIVE_APPROVAL' },
    { label: i18n.get('全部历史'), type: 'all', value: 'ALL' }
  ]

  static defaultProps = {
    flowlogType: ''
  }

  constructor(props) {
    super(props)
    const { flowlogType, doc } = props
    this.state = {
      defaultSelectedMenu: null,
      menus: [],
      selectedMenu: {},
      flowlogType,
      doc,
      formatLogs: [],
      isShowVersion: false,
      flowPlanConfig: undefined
    }
  }

  /**
   * 通过 layout config 控制默认展示的 flow doc type
   * @returns {Promise<void>}
   */
  init = async () => {
    const { doc, flowlogType, KA_DETAIL_LAYOUT_CONFIG } = this.props
    let _menus = this.menus
    let defaultSelectedMenu = this.menus.find(v => v.value === 'ALL')
    if (KA_DETAIL_LAYOUT_CONFIG) {
      const specificationId = doc?.form?.specificationId?.id
      if (!specificationId) return
      const layoutConfig = await api.invokeService('@bills:get:layoutConfig', { type: 'ARRANGE', specificationId })
      const approvalHistoryList = layoutConfig?.value?.configDetail?.approvalHistoryList
      if (approvalHistoryList && approvalHistoryList.length) {
        _menus = this.menus.filter(menuItem => !!~approvalHistoryList.indexOf(menuItem.value))
      }
      const approvalHistory = layoutConfig?.value?.configDetail?.approvalHistory
      defaultSelectedMenu = this.menus.find(v => v.value === approvalHistory)
    }
    this.setState(
      {
        menus: _menus,
        defaultSelectedMenu: defaultSelectedMenu
      },
      () => {
        const { selectedMenu, formatLogs } = this._formatData(
          flowlogType || defaultSelectedMenu?.type || 'all',
          doc,
          _menus
        )
        this.setState({
          selectedMenu,
          formatLogs
        })
      }
    )
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.flowlogType !== nextProps.flowlogType) {
      this._refreshView(nextProps.flowlogType)
    }

    if (this.props.doc !== nextProps.doc) {
      const { selectedMenu, formatLogs } = this._formatData(nextProps.flowlogType, nextProps.doc)
      this.setState({ selectedMenu, formatLogs, doc: nextProps.doc })
    }
  }

  async componentDidMount() {
    this.init()
    const { doc } = this.props
    const { flowPlanConfig } = this.state
    if (doc?.plan?.flowPlanConfigId && !flowPlanConfig) {
      const { value } = await api.invokeService('@bills:get:flow:config:by:id', {
        id: doc?.plan?.flowPlanConfigId
      })
      this.setState({ flowPlanConfig: value, isShowVersion: !!doc?.plan?.configVersion })
    }
  }

  _dropdownMenu = () => {
    return (
      <Menu onClick={item => this._logTypeChangeHandle(item)}>
        {this.state.menus.map(item => {
          return <Menu.Item key={item.type}>{i18n.get(item.label)}</Menu.Item>
        })}
      </Menu>
    )
  }

  _versionClickHandle = _debounce(async item => {
    const { versionItemAction, doc } = this.props
    item = isArray(item) ? item[0] : item
    const isHistoryVersion = await HistoryApi.getFlowVersionModifyState(item.flowVersionedId)
    if (isHistoryVersion?.value) {
      versionItemAction && versionItemAction(item)
    } else {
      api.open('@bills:BillHistoryModal', {
        isHoseEUI: true,
        flowVersionedId: item.flowVersionedId,
        specification: doc.form.specificationId
      })
    }
  }, 500)

  _logTypeChangeHandle = item => {
    const { key } = item
    this._refreshView(key)
  }

  _refreshView = type => {
    let { selectedMenu, doc } = this.state
    if (selectedMenu && selectedMenu.type === type) return
    this.setState(this._formatData(type, doc))
  }

  _formatData = (logtype, dataDoc, _menus) => {
    if (!_menus) {
      _menus = this.state?.menus || this.menus
    }
    let selectedMenu = _menus.find(item => item.type === logtype)
    if (!selectedMenu) {
      if (logtype) {
        let targetMenu = this.menus.find(item => item.type === logtype)
        message.info(i18n.get(`无查看${targetMenu.label}权限`))
      }
      if (this.state?.defaultSelectedMenu) {
        selectedMenu = this.state.defaultSelectedMenu
      } else {
        selectedMenu = this.menus.find(item => item.type === 'all')
      }
    }
    const formatLogs = this._filterLog(selectedMenu.type, dataDoc)
    return { selectedMenu, formatLogs }
  }

  _filterLog(type, dataDoc) {
    if (!dataDoc) return
    const { logs } = dataDoc
    const formatLogs = fnMapLogs(logs)
    if (type === 'comment') {
      return formatLogs.filter(item => {
        return item.action !== 'freeflow.carbonCopy' && item.attributes && item.attributes.comment
      })
    }

    if (type === 'approve') {
      return formatLogs.filter(
        item => !~['freeflow.comment', 'freeflow.select.approver', 'freeflow.modify'].indexOf(item.action)
      )
    }

    if (type === 'staff_approval') {
      return formatLogs.filter(item => {
        const isStaffApprovalAction = [
          'freeflow.submit',
          'freeflow.reject',
          'freeflow.agree',
          'freeflow.addnode',
          'freeflow.pay', //支付完成
          'freeflow.pay.by.offline', //转线下支付
          'freeflow.paying', //支付中
          'freeflow.pay.partial.paying', //部分支付中
          'freeflow.pay.partial.success', //部分支付成功
          'freeflow.pay.partial.failure', //部分支付失败
          'freeflow.repaying', //重新支付中
          'freeflow.failure' //支付失败
        ].includes(item.action)
        if (!isStaffApprovalAction) return false
        if (getV(item, 'attributes.isEbotNode')) return false
        if (getV(item, 'attributes.isInvoiceApplicationNode')) return false
        if (getV(item, 'attributes.isRecalNode')) return false
        if (item.action === 'freeflow.reject') {
          return item.attributes && !item.attributes.isAuto
        }
        return true
      })
    }

    if (type === 'effective') {
      return formatLogs.filter(item => {
        const skippedType = [
          'APPROVER_NOT_FOUND',
          'APPROVER_NOT_FOUND_BY_ROLE',
          'NO_ABILITY',
          'PAY_AMOUNT_IS_0'
        ].includes(item?.attributes?.skippedType)
        return !(item?.action === 'freeflow.skipped' && skippedType)
      })
    }

    return formatLogs
  }

  _renderLine = (key, el) => {
    const { userInfo, dynamicChannelMap = {}, staffDisplayConfigField } = this.props
    const { doc, flowPlanConfig } = this.state
    const { time, action } = el
    el.dynamicChannelMap = dynamicChannelMap
    const dt = moment(time)
    const ac = action.replace('freeflow.', '')
    const isEbotNode = getV(el, 'attributes.isEbotNode')
    const isInvoiceApplicationNode = getV(el, 'attributes.isInvoiceApplicationNode')
    const isRecalNode = getV(el, 'attributes.isRecalNode')
    if (isEbotNode) {
      el.operatorId = { name: 'EBot', avatar: 'EBotIconNode' }
    }
    if (isInvoiceApplicationNode) {
      el.operatorId = { name: '开票申请', avatar: 'EBotIconNode' }
    }
    if (isRecalNode) {
      el.operatorId = { name: i18n.get('重算节点'), avatar: 'RecalculateIconNode' }
    }

    if(isAIAgentNode(el)){
      const {agent} = getAIAgentObj(el, this.props.nodesAIAgentMap)
      el.operatorId = { name: agent?.name, avatar: agent?.icon }
    }
    // 通讯录显示配置
    flowStateMap.staffDisplayConfigField = staffDisplayConfigField
    return (
      <div key={key} className="flow-item horizontal">
        <div className="time-line" />
        <div className="mark-circle" />
        <div className="label-time vertical">
          <div>{dt.format('YYYY')}</div>
          <div style={{ color: '#333' }}>{dt.format('MM.DD')}</div>
          <div>{dt.format('HH:mm')}</div>
        </div>
        <div className="flow-desc horizontal">
          {flowStateMap[ac] && flowStateMap[ac].render(el, this._versionClickHandle, userInfo, doc)}
        </div>
      </div>
    )
  }

  fnGetVersionTxt = () => {
    const { flowPlanConfig, isShowVersion } = this.state
    const { doc } = this.props
    if (!flowPlanConfig?.version) return ''
    let versionTxt = flowPlanConfig?.name
    if (isShowVersion) {
      versionTxt = `${versionTxt}:v${Number(doc?.plan?.configVersion).toFixed(1)}`
    }
    return versionTxt
  }

  render() {
    const { formatLogs, selectedMenu, doc, flowPlanConfig, isShowVersion } = this.state
    const versionTxt = this.fnGetVersionTxt()
    const { privilegeId } = this.props
    return (
      <div className="expense-log">
        <div className="horizontal jc-sb">
          <Dropdown overlay={this._dropdownMenu()} trigger={['hover']}>
            <span className="log-title">
              {selectedMenu && i18n.get(selectedMenu.label)}
              <Icon type="down" />
            </span>
          </Dropdown>
          <BillsVersion flowId={doc?.id} logs={formatLogs} privilegeId={privilegeId} viewFlowState={doc?.state} />
        </div>
        <div className="log-content">
          <div className="expense-flow">
            <div className="pos-r ovr-h">
              {formatLogs &&
                formatLogs.map((el, i) => {
                  return this._renderLine(i, el)
                })}
            </div>
          </div>
        </div>
        <div className="flow-plan-version">{versionTxt}</div>
      </div>
    )
  }
}
