@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/eui-styles/less/token.less';

.account-create-wrapper-modal {
  background: #ffffff;
  flex: 1;
  :global {
    .modal-header {
      border: none;
      padding: 16px;
      background: rgba(255, 255, 255, 1);
      border-top-right-radius: 12px;
      border-top-left-radius: 12px;
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
      line-height: 24px;
    }
    .payee-content {
      position: absolute;
      top: 56px;
      bottom: 64px;
      left: 0;
      right: 0;
      overflow: auto;
      padding: 0 16px 12px 16px;
      .eui-form-item-explain,
      .eui-form-item-extra {
        margin-bottom: 16px !important;
      }
      .tip-info-wrapper {
        margin-bottom: 16px;
      }
      .branch {
        margin-bottom: @space-5;
        .dis-f {
          color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
          font-size: 14px;
          .filter-text {
            color: var(--eui-text-link-normal, #2555ff);
            margin-left: @space-2;
            cursor: pointer;
          }
        }
      }

      .sortSpace {
        width: 100%;

        .eui-space-item:nth-child(1) {
          flex: 1;
          margin-top: 2px;
        }
      }
    }
    .account-type {
      width: 100%;
      .eui-radio-button-wrapper {
        width: 50%;
        height: 32px;
        justify-content: center;
      }
    }

    .select-bank-wrapper {
      .line-bank {
        height: 22px;
      }
    }
    .bank-desc {
      color: @gray-8;
      margin-bottom: 10px;
    }
    .known-reason {
      color: @primary-6;
      cursor: pointer;
      padding-left: 5px;
    }
    .oter-bank-input {
      .ant-form-item-required {
        opacity: 0;
      }
    }
    .select-bank {
      position: relative;
      .tool-tips {
        position: absolute;
        top: 0;
        right: -20px;
      }
    }
    .conciseList {
      border-top: 1px solid rgba(29, 43, 61, 0.06);
      .ant-radio-group {
        margin: 24px 0 16px;
        .ant-radio-button-wrapper:first-child {
          border-radius: 4px 0 0 4px;
        }
        .ant-radio-button-wrapper:last-child {
          border-radius: 0 4px 4px 0;
        }
      }
      .concise-description {
        margin-bottom: 10px;
      }
    }
    .account-create-footer {
      height: 56px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 0 16px;
      background-color: #ffffff;
      box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2);
    }
    .modal-header {
      .change-log {
        color: @color-brand-2;
        .font-size-2;
        .font-weight-2;
      }
    }
    .payee-account-modal-footer {
      display: flex;
      flex-direction: row;
      align-items: center;
      background-color: white;
      height: 64px;
      padding: 0 16px;
      justify-content: flex-end;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.06);
      .active-wrap {
        .font-size-2;
        .font-weight-2;
        color: @color-inform-2;
        .disable {
          color: @color-error-2;
        }
      }
    }
  }
}
.account-create-wrapper-popup {
  background: #ffffff;
  flex: 1;
  :global {
    .modal-header {
      border: none;
      padding: 16px;
      background: rgba(255, 255, 255, 1);
      border-top-right-radius: 12px;
      border-top-left-radius: 12px;
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
      border-bottom: 1px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.1));
      line-height: 24px;
    }
    .payee-content {
      position: absolute;
      top: 56px;
      bottom: 64px;
      left: 0;
      right: 0;
      overflow: auto;
      padding: 16px;
      .eui-form-item-explain,
      .eui-form-item-extra {
        margin-bottom: 16px !important;
      }
      .tip-info-wrapper {
        margin-bottom: 16px;
      }
      .branch {
        margin-bottom: @space-5;
        .dis-f {
          color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
          font-size: 14px;
          .filter-text {
            color: var(--eui-text-link-normal, #2555ff);
            margin-left: @space-2;
            cursor: pointer;
          }
        }
      }

      .sortSpace {
        width: 100%;

        .eui-space-item:nth-child(1) {
          flex: 1;
          margin-top: 2px;
        }
      }
    }
    .account-type {
      width: 100%;
      .eui-radio-button-wrapper {
        width: 50%;
        height: 32px;
        justify-content: center;
      }
      .currency-select {
        width: 100%;
        border-radius: 6px;
        background: var(--eui-bg-float-base, #f2f3f5);
        padding: 8px;

        .currency-group {
          width: 100%;

          .currency-radio {
            .eui-radio-wrapper {
              color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
              width: 100%;
            }
          }
        }
      }
    }

    .select-bank-wrapper {
      .line-bank {
        height: 22px;
      }
    }
    .bank-desc {
      color: @gray-8;
      margin-bottom: 10px;
    }
    .known-reason {
      color: @primary-6;
      cursor: pointer;
      padding-left: 5px;
    }
    .oter-bank-input {
      .ant-form-item-required {
        opacity: 0;
      }
    }
    .select-bank {
      position: relative;
      .tool-tips {
        position: absolute;
        top: 0;
        right: -20px;
      }
    }
    .conciseList {
      border-top: 1px solid rgba(29, 43, 61, 0.06);
      .ant-radio-group {
        margin: 24px 0 16px;
        .ant-radio-button-wrapper:first-child {
          border-radius: 4px 0 0 4px;
        }
        .ant-radio-button-wrapper:last-child {
          border-radius: 0 4px 4px 0;
        }
      }
      .concise-description {
        margin-bottom: 10px;
      }
    }
    .account-create-footer {
      height: 56px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 0 16px;
      background-color: #ffffff;
      box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2);
    }
    .modal-header {
      .change-log {
        color: @color-brand-2;
        .font-size-2;
        .font-weight-2;
      }
    }
    .payee-account-modal-footer {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 16px;
      justify-content: flex-start;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.06);
      .active-wrap {
        .font-size-2;
        .font-weight-2;
        color: @color-inform-2;
        .disable {
          color: @color-error-2;
        }
      }
    }
  }
}
