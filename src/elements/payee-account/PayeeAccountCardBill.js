/**
 * 单据查看态的收款信息卡片新样式,目前只有单据查看态使用 2025.08.05
 */

import styles from './PayeeAccountCardBill.module.less'
import React, { PureComponent } from 'react'
import { Tooltip, Tag, Divider, Ellipsis, message } from '@hose/eui'
import { showMessage } from '@ekuaibao/show-util'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import { EnhanceConnect } from '@ekuaibao/store'
import { getDisplayName } from '../utilFn'
import { T } from '@ekuaibao/i18n'
import { concat } from 'lodash'
import { getAccountById } from '../../plugins/bills/bills.action'
import { app } from '@ekuaibao/whispered'
import { OutlinedGeneralMember, OutlinedGeneralGroup } from '@hose/eui-icons'

function fnCardNumber(cardNo) {
  if (/[^0-9]/.test(cardNo)) {
    return cardNo
  }
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1   ')
}

@EnhanceConnect(state => ({
  payeeConfig: state['@common'].payeeConfig,
  allCurrencyRates: state['@common'].getCurrencyAll,
  standardCurrency: state['@common'].standardCurrency
}))
export default class PayeeAccountCardBill extends PureComponent {
  static defaultProps = {
    name: '',
    accountNo: '',
    branch: '',
    bank: '',
    icon: '',
    owner: '',
    type: '',
    isDefault: false,
    state: '',
    active: false,
    dynamicCard: 'readonly',
    className: null,
    searchText: '',
    canShare: false,
    isSelf: false,
    multiplePayeesMode: false
  }

  handleClick = () => {
    getAccountById(this.props.id).then(res => {
      if (res?.items?.length) {
        app.open('@bills:PayeeAccountCreatePopup', {
          payee: res.items[0],
          isCreate: false,
          disableType: 'MCDISABLE',
          mcDisable: false,
          showTips: false,
          showHistory: false,
          disabled: true
        })
      } else {
        message.error(i18n.get('收款账户不存在'))
      }
    })
  }

  renderCopyBtn = value => {
    const textData = value.replace(/\s/g, '')
    return (
      <CopyToClipboard text={textData}>
        <div type="primary" onClick={this.handleCopyBtnClick} style={{ cursor: 'pointer' }}>
          {i18n.get('复制')}
        </div>
      </CopyToClipboard>
    )
  }

  handleCopyBtnClick = e => {
    e.stopPropagation()
    e.preventDefault()
    showMessage.success(i18n.get('复制成功！'))
  }

  renderBank() {
    let { ...data } = this.props
    let bankName = data?.branchId?.name || data?.branch || data.bank || data.unionBank
    const bankIcon = data.icon || data.unionIcon
    const isCorporation = data.owner === 'CORPORATION' ? i18n.get('企业') : data.staffId && data.staffId.name

    return (
      <div className="bank-card-footer">
        <div className="bank-card-icon">
          {bankName && <img className="bank-icon" src={`${bankIcon}?********`} alt={bankName} />}
          <Tooltip placement="topLeft" title={bankName} getPopupContainer={triggerNode => triggerNode.parentNode}>
            {data.sort === 'OVERSEABANK' ? (
              <div style={{ display: 'flex', alignItems: 'center' }} className="bank-branch">
                <span>{data.swiftCode}</span>
                <Divider type="vertical" style={{ height: '16px' }} />
                <span className="bank-bankName">{data.bankName}</span>
              </div>
            ) : (
              <div className="bank-branch">{bankName}</div>
            )}
          </Tooltip>
        </div>
        {data.owner && (
          <div className="bank-card-owner">
            <T name="所有者:" /> {isCorporation}
          </div>
        )}
      </div>
    )
  }

  render() {
    let { hiddenActions, payeeConfig, allCurrencyRates, standardCurrency, ...card } = this.props
    let { sort, style = {}, receivingCurrencyNumCode } = card
    if (!sort) {
      return null
    }
    let accountNo = fnCardNumber(card.accountNo || card.cardNo)
    let staffname = getDisplayName(card?.staffId)
    const _staffname = staffname?.length > 5 ? staffname?.substr(0, 5) + '...' : staffname
    let owner = card.owner === 'INDIVIDUAL' ? _staffname : i18n.get('企业')
    const remarkDisplay =
      card.type === 'PERSONAL'
        ? payeeConfig?.personalAccountConfig?.remarkDisplay
        : payeeConfig?.publicAccountConfig?.remarkDisplay
    const remark = card?.remark || ''
    const showRemark = remark && remarkDisplay
    const accountType = card.type === 'PERSONAL' ? i18n.get('个人账户') : i18n.get('对公账户')
    if (receivingCurrencyNumCode) {
      const allCurrency = concat(standardCurrency, allCurrencyRates)
      card.receivingCurrency = allCurrency.find(item => item.numCode === receivingCurrencyNumCode) || {}
    }
    return (
      <div
        className={`${styles['payee-account-card']} ${card.className || ''}`}
        style={style}
        onClick={this.handleClick}
      >
        <div className="title-line-style">
          <Tooltip key={card.id} placement="topLeft" title={card.accountName || card.name}>
            <span
              className={card.active ? 'title-line-text fs-14 color-gray-9' : 'title-line-text fs-14 color-gray-7 mr-8'}
            >
              {card.accountName || card.name}
            </span>
          </Tooltip>
          <Tag size="small" fill="outline" style={{ width: 'max-content', marginLeft: '8px', marginRight: '8px' }}>
            {card.type === 'PERSONAL' ? (
              <OutlinedGeneralMember fontSize={12} className="mr-2" />
            ) : (
              <OutlinedGeneralGroup className="mr-2" fontSize={12} />
            )}
            {accountType}
          </Tag>
          {receivingCurrencyNumCode && (
            <span className="receiving-currency">
              {card.receivingCurrency.strCode}
              {i18n.get(card.receivingCurrency.name)}
            </span>
          )}
        </div>
        <div className="card-number-wrapper">
          <Tooltip placement="topLeft" title={this.renderCopyBtn.bind(this, accountNo)}>
            <div className="card-number">{accountNo}</div>
          </Tooltip>
          {showRemark && (
            <Tooltip title={remark}>
              <div className="account-remark">{`${i18n.get('备注：')}${remark}`}</div>
            </Tooltip>
          )}
        </div>

        {this.renderBank()}
      </div>
    )
  }
}
