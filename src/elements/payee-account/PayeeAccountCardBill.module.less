@import "~@ekuaibao/web-theme-variables/styles/colors";
@import "~@ekuaibao/eui-styles/less/token.less";

.payee-account-card {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 16px;
  border-radius: 8px;
  width: 100%;
  cursor: pointer;
  background: var(--eui-bg-body-overlay, #f7f8fa);
  overflow: hidden;
  gap: 4px;
  border: 1px solid var(--eui-bg-body-overlay, #f7f8fa);
  flex-shrink: 0;
  &:hover {
    border: 1px solid var(--eui-line-divider-default) !important;
    background: var(--eui-bg-base);
  }

  :global {
    .title-line-style {
      display: flex;
      flex-direction: row;
      align-items: center;
      .title-line-text {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
        font: var(--eui-font-body-b1);
      }
      .receiving-currency {
        flex-shrink: 0;
        margin-left: auto;
        font: var(--eui-font-note-b2);
        color: var(--eui-text-title);
      }
    }
    .card-number-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .card-number {
        color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
        font: var(--eui-font-head-b1);
        flex-shrink: 0;
      }
      .account-remark {
        margin-left: 16px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
        font: var(--eui-font-note-r2);
      }
    }

    .bank-card-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;

      .bank-card-icon {
        flex: 1;
        display: flex;
        overflow: hidden;
        align-items: center;
        margin-right: 12px;
        .bank-icon {
          width: 20px;
          height: 20px;
          flex-shrink: 0;
          margin-right: 4px;
        }

        .bank-branch {
          color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
          font: var(--eui-font-body-r1);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          .bank-bankName {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .bank-card-owner {
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
        font: var(--eui-font-body-r1);
      }
    }
    .card-content {
      display: flex;
      justify-content: space-between;

      .card-left-wrapper {
        flex: 1;
        overflow: hidden;

        .account-number-line-style {
          margin: 8px 0 14px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          .account-number-style {
            font-weight: 600;
            font-size: 16px;
            //line-height : 2.0;
            //color       : @gray-9;
          }
        }
        .account-adr-line-style {
          display: flex;
          max-width: 550px;
          flex-direction: row;
          line-height: 20px;
          .bank-icon-wrapper {
            flex-shrink: 0;
            height: 20px;
            width: 20px;
          }
          .bank-info-style {
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            white-space: nowrap;
            font-size: 12px;
            cursor: pointer;
            color: @gray-8;
          }
          .international-bank-wrap {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: row;
            color: @color-black-2;
            &.international-bank-inactive {
              color: @color-black-4;
              .bank-info-style {
                color: @color-black-4;
              }
            }
            .bank-info-style {
              flex-shrink: 0;
              font-size: 14px;
              color: @color-black-2;
            }
            .international-bank-code {
              flex-shrink: 0;
              font-size: 14px;
              max-width: 130px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding-right: @space-2;
              margin-right: @space-2;
              border-right: 1px solid @color-black-4;
            }
          }
        }
      }
      .card-right-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex-shrink: 0;
        height: 65px;
        .op-wrapper {
          display: flex;
          justify-content: flex-end;
          margin-top: 24px;
          .complement-info {
            color: #ea1005;
            .warning-icon {
              margin-right: 8px;
              fill: #ea1005;
            }
          }
          .share-actions-wrapper {
            display: flex;
            margin-right: 10px;
          }
          .ops-row-wrap {
            display: flex;
            .op-line-wrapper {
              &:last-child {
                & .segment-line-vertical {
                  display: none;
                }
              }
            }
          }
          .action-wrapper {
            line-height: 19px;
            height: 19px;
            width: 75px;
            text-align: right;
          }
          .enable-switch {
            min-width: 32px;
            line-height: 19px;
            height: 19px;
            border-radius: 16px;
            margin-left: 5px;
            &::after {
              width: 16px;
              height: 16px;
              left: 0;
              top: 0;
              border-radius: 16px;
            }
          }
          .disable-switch {
            .enable-switch;
            display: none;
          }

          .ant-switch-checked {
            &::after {
              left: 33px !important;
            }
          }
        }
        .owner-line {
          margin-left: 15px;
          display: flex;
          flex-direction: row;
          bottom: 16px;
          align-items: flex-end;
          justify-content: flex-end;
          .owner-text {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.25);
          }
        }
      }
    }

    .segment-line-vertical {
      height: 20px;
      border-right: 1px solid #e9e9e9;
      margin-right: 10px;
      padding-right: 10px;
      //&:last-child {
      //  display : none;
      //}
    }

    .default-wrapper {
      text-align: center;
      height: 18px;
      border-radius: 2px;
      background-color: #eeeeee;
      border: solid 1px #dcdcdc;
      .default-text-style {
        font-size: 11px;
        color: rgba(0, 0, 0, 0.65);
      }
    }

    .highlight {
      color: var(--brand-base);
    }
  }
}
