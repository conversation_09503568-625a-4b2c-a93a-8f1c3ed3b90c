import excelImportTracker from './ExcelImportMemoryTracker'

/**
 * ImportStepView内存监控补丁
 * 用于在不大幅修改原代码的情况下添加内存监控
 */

/**
 * 包装handleChange方法，添加内存监控
 * @param {Function} originalHandleChange - 原始的handleChange方法
 * @returns {Function} 包装后的方法
 */
export function wrapHandleChange(originalHandleChange) {
  return function(info) {
    const file = info[0]
    
    // 开始追踪
    if (file && file.status !== 'error') {
      excelImportTracker.startTracking({
        name: file.name,
        size: file.size,
        type: file.type
      })
    }
    
    // 如果是上传完成
    if (file && file.status === 'done') {
      excelImportTracker.trackUploadComplete(file.response)
    }
    
    // 如果是错误
    if (file && file.status === 'error') {
      excelImportTracker.trackError(new Error('Upload failed'), 'file upload')
    }
    
    // 调用原始方法
    const result = originalHandleChange.call(this, info)
    
    return result
  }
}

/**
 * 包装ArrayBuffer转换操作
 * @param {ArrayBuffer} response - 响应数据
 * @returns {string} 转换后的字符串
 */
export function monitoredArrayBufferToString(response) {
  excelImportTracker.trackArrayBufferConversionStart(response)
  
  try {
    // 原始的转换逻辑
    const uint8 = Array.from(new Uint8Array(response))
      .map(item => String.fromCharCode(item))
      .join('')
    
    excelImportTracker.trackArrayBufferConversionEnd(uint8)
    return uint8
  } catch (error) {
    excelImportTracker.trackError(error, 'ArrayBuffer conversion')
    throw error
  }
}

/**
 * 包装JSON解析操作
 * @param {string} jsonString - JSON字符串
 * @returns {Object} 解析后的对象
 */
export function monitoredJSONParse(jsonString) {
  excelImportTracker.trackJSONParseStart(jsonString)
  
  try {
    const result = JSON.parse(jsonString)
    excelImportTracker.trackJSONParseEnd(result)
    return result
  } catch (error) {
    excelImportTracker.trackError(error, 'JSON parsing')
    throw error
  }
}

/**
 * 包装fnHandleChange方法
 * @param {Function} originalFnHandleChange - 原始的fnHandleChange方法
 * @returns {Function} 包装后的方法
 */
export function wrapFnHandleChange(originalFnHandleChange) {
  return function(obj) {
    excelImportTracker.trackDataProcessingStart(obj)
    
    try {
      const result = originalFnHandleChange.call(this, obj)
      excelImportTracker.trackDataProcessingEnd(result)
      return result
    } catch (error) {
      excelImportTracker.trackError(error, 'data processing')
      throw error
    }
  }
}

/**
 * 包装setState方法，监控状态变化
 * @param {Function} originalSetState - 原始的setState方法
 * @returns {Function} 包装后的方法
 */
export function wrapSetState(originalSetState) {
  return function(newState, callback) {
    // 记录状态更新前的内存
    if (newState && typeof newState === 'object') {
      excelImportTracker.trackStateUpdate(newState)
    }
    
    // 调用原始setState
    return originalSetState.call(this, newState, callback)
  }
}

/**
 * 监控数组操作
 * @param {string} operation - 操作类型
 * @param {Array} sourceArray - 源数组
 * @param {Array} newArray - 新数组
 * @returns {Array} 新数组
 */
export function monitorArrayOperation(operation, sourceArray, newArray) {
  excelImportTracker.trackArrayOperation(operation, sourceArray, newArray)
  return newArray
}

/**
 * 快速集成方法 - 在组件中调用此方法即可启用监控
 * @param {Object} component - React组件实例
 */
export function enableMemoryMonitoring(component) {
  console.log('🔧 启用Excel导入内存监控')
  
  // 包装handleChange方法
  if (component.handleChange) {
    const originalHandleChange = component.handleChange
    component.handleChange = wrapHandleChange(originalHandleChange)
  }
  
  // 包装fnHandleChange方法
  if (component.fnHandleChange) {
    const originalFnHandleChange = component.fnHandleChange
    component.fnHandleChange = wrapFnHandleChange(originalFnHandleChange)
  }
  
  // 包装setState方法
  if (component.setState) {
    const originalSetState = component.setState
    component.setState = wrapSetState(originalSetState)
  }
  
  // 添加组件卸载时的清理
  const originalComponentWillUnmount = component.componentWillUnmount
  component.componentWillUnmount = function() {
    if (excelImportTracker.isTracking) {
      excelImportTracker.endTracking()
    }
    
    if (originalComponentWillUnmount) {
      originalComponentWillUnmount.call(this)
    }
  }
  
  console.log('✅ 内存监控已启用')
}

/**
 * 手动结束监控
 */
export function endMemoryMonitoring() {
  return excelImportTracker.endTracking()
}

/**
 * 获取监控状态
 */
export function getMonitoringStatus() {
  return excelImportTracker.getTrackingStatus()
}

/**
 * 导出监控数据
 */
export function exportMonitoringData() {
  return window.memoryMonitor ? window.memoryMonitor.exportToCSV() : null
}

/**
 * 强制垃圾回收（开发环境）
 */
export function forceGarbageCollection() {
  excelImportTracker.forceGC()
}

// 添加全局快捷方式
if (typeof window !== 'undefined') {
  window.enableExcelMemoryMonitoring = enableMemoryMonitoring
  window.endExcelMemoryMonitoring = endMemoryMonitoring
  window.getExcelMonitoringStatus = getMonitoringStatus
  window.exportExcelMonitoringData = exportMonitoringData
  window.forceGC = forceGarbageCollection
}
