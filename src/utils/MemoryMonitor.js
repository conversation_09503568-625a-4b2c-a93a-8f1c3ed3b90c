/**
 * 内存监控工具类
 * 用于实时监控Excel导入过程中的内存使用情况
 */
class MemoryMonitor {
  constructor() {
    this.logs = []
    this.isMonitoring = false
    this.monitorInterval = null
    this.startTime = null
    this.callbacks = []
  }

  /**
   * 开始监控
   * @param {number} interval - 监控间隔(ms)，默认100ms
   */
  startMonitoring(interval = 100) {
    if (this.isMonitoring) {
      console.warn('内存监控已在运行中')
      return
    }

    this.isMonitoring = true
    this.startTime = Date.now()
    this.logs = []
    
    console.log('🚀 开始内存监控...')
    
    // 记录初始状态
    this.logMemory('监控开始')
    
    // 定时监控
    this.monitorInterval = setInterval(() => {
      this.logMemory('定时监控')
    }, interval)
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return
    }

    this.isMonitoring = false
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = null
    }
    
    this.logMemory('监控结束')
    console.log('⏹️ 内存监控已停止')
    
    // 生成报告
    this.generateReport()
  }

  /**
   * 记录特定时刻的内存使用情况
   * @param {string} label - 标签描述
   * @param {Object} extra - 额外信息
   */
  logMemory(label, extra = {}) {
    const timestamp = Date.now()
    const relativeTime = this.startTime ? timestamp - this.startTime : 0
    
    let memoryInfo = {
      timestamp,
      relativeTime,
      label,
      ...extra
    }

    // 获取内存信息
    if (performance.memory) {
      const { usedJSHeapSize, totalJSHeapSize, jsHeapSizeLimit } = performance.memory
      
      memoryInfo = {
        ...memoryInfo,
        usedJSHeapSize: Math.round(usedJSHeapSize / 1024 / 1024 * 100) / 100, // MB
        totalJSHeapSize: Math.round(totalJSHeapSize / 1024 / 1024 * 100) / 100, // MB
        jsHeapSizeLimit: Math.round(jsHeapSizeLimit / 1024 / 1024 * 100) / 100, // MB
        usagePercent: Math.round((usedJSHeapSize / jsHeapSizeLimit) * 10000) / 100 // 百分比
      }
    }

    // 获取DOM节点数量
    memoryInfo.domNodes = document.querySelectorAll('*').length

    this.logs.push(memoryInfo)
    
    // 实时输出
    console.log(`📊 [${relativeTime}ms] ${label}:`, {
      内存使用: `${memoryInfo.usedJSHeapSize}MB`,
      使用率: `${memoryInfo.usagePercent}%`,
      DOM节点: memoryInfo.domNodes,
      ...extra
    })

    // 触发回调
    this.callbacks.forEach(callback => {
      try {
        callback(memoryInfo)
      } catch (error) {
        console.error('内存监控回调执行失败:', error)
      }
    })

    // 内存使用率过高警告
    if (memoryInfo.usagePercent > 80) {
      console.warn(`⚠️ 内存使用率过高: ${memoryInfo.usagePercent}%`)
    }
  }

  /**
   * 添加监控回调
   * @param {Function} callback - 回调函数
   */
  addCallback(callback) {
    this.callbacks.push(callback)
  }

  /**
   * 移除监控回调
   * @param {Function} callback - 回调函数
   */
  removeCallback(callback) {
    const index = this.callbacks.indexOf(callback)
    if (index > -1) {
      this.callbacks.splice(index, 1)
    }
  }

  /**
   * 生成内存使用报告
   */
  generateReport() {
    if (this.logs.length === 0) {
      console.log('📋 没有监控数据')
      return
    }

    const startLog = this.logs[0]
    const endLog = this.logs[this.logs.length - 1]
    
    const memoryDiff = endLog.usedJSHeapSize - startLog.usedJSHeapSize
    const maxMemory = Math.max(...this.logs.map(log => log.usedJSHeapSize))
    const maxUsagePercent = Math.max(...this.logs.map(log => log.usagePercent))
    
    console.group('📋 内存使用报告')
    console.log('总监控时长:', `${endLog.relativeTime}ms`)
    console.log('起始内存:', `${startLog.usedJSHeapSize}MB`)
    console.log('结束内存:', `${endLog.usedJSHeapSize}MB`)
    console.log('内存变化:', `${memoryDiff > 0 ? '+' : ''}${memoryDiff}MB`)
    console.log('峰值内存:', `${maxMemory}MB`)
    console.log('最高使用率:', `${maxUsagePercent}%`)
    console.log('监控点数量:', this.logs.length)
    
    // 找出内存增长最大的时间段
    let maxIncrease = 0
    let maxIncreaseSegment = null
    
    for (let i = 1; i < this.logs.length; i++) {
      const increase = this.logs[i].usedJSHeapSize - this.logs[i-1].usedJSHeapSize
      if (increase > maxIncrease) {
        maxIncrease = increase
        maxIncreaseSegment = {
          from: this.logs[i-1],
          to: this.logs[i]
        }
      }
    }
    
    if (maxIncreaseSegment) {
      console.log('最大内存增长:', `+${maxIncrease}MB`)
      console.log('增长时间段:', `${maxIncreaseSegment.from.label} → ${maxIncreaseSegment.to.label}`)
    }
    
    console.groupEnd()
    
    // 返回详细数据供进一步分析
    return {
      logs: this.logs,
      summary: {
        duration: endLog.relativeTime,
        startMemory: startLog.usedJSHeapSize,
        endMemory: endLog.usedJSHeapSize,
        memoryDiff,
        maxMemory,
        maxUsagePercent,
        maxIncrease,
        maxIncreaseSegment
      }
    }
  }

  /**
   * 导出监控数据为CSV
   */
  exportToCSV() {
    if (this.logs.length === 0) {
      console.log('没有数据可导出')
      return
    }

    const headers = ['时间戳', '相对时间(ms)', '标签', '使用内存(MB)', '总内存(MB)', '内存限制(MB)', '使用率(%)', 'DOM节点数']
    const rows = this.logs.map(log => [
      log.timestamp,
      log.relativeTime,
      log.label,
      log.usedJSHeapSize,
      log.totalJSHeapSize,
      log.jsHeapSizeLimit,
      log.usagePercent,
      log.domNodes
    ])

    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `memory-monitor-${Date.now()}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    console.log('📁 内存监控数据已导出为CSV文件')
  }

  /**
   * 清空监控数据
   */
  clear() {
    this.logs = []
    console.log('🗑️ 监控数据已清空')
  }
}

// 创建全局实例
const memoryMonitor = new MemoryMonitor()

// 挂载到window对象，方便调试
if (typeof window !== 'undefined') {
  window.memoryMonitor = memoryMonitor
}

export default memoryMonitor
