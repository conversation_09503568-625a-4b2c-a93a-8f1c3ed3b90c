import memoryMonitor from './MemoryMonitor'

/**
 * Excel导入内存追踪器
 * 专门用于追踪Excel导入过程中的内存变化
 */
class ExcelImportMemoryTracker {
  constructor() {
    this.isTracking = false
    this.importStartTime = null
    this.fileInfo = null
  }

  /**
   * 开始追踪Excel导入
   * @param {Object} fileInfo - 文件信息
   */
  startTracking(fileInfo = {}) {
    this.isTracking = true
    this.importStartTime = Date.now()
    this.fileInfo = fileInfo
    
    console.group('🔍 Excel导入内存追踪开始')
    console.log('文件信息:', fileInfo)
    
    // 开始内存监控，更频繁的监控间隔
    memoryMonitor.startMonitoring(50)
    
    // 记录导入开始
    memoryMonitor.logMemory('Excel导入开始', {
      fileName: fileInfo.name,
      fileSize: fileInfo.size,
      fileType: fileInfo.type
    })
  }

  /**
   * 追踪文件上传完成
   * @param {Object} response - 响应数据
   */
  trackUploadComplete(response) {
    if (!this.isTracking) return
    
    const responseSize = response ? response.byteLength || JSON.stringify(response).length : 0
    
    memoryMonitor.logMemory('文件上传完成', {
      responseSize: Math.round(responseSize / 1024) + 'KB',
      responseType: typeof response
    })
  }

  /**
   * 追踪ArrayBuffer转换开始
   * @param {ArrayBuffer} buffer - 原始buffer
   */
  trackArrayBufferConversionStart(buffer) {
    if (!this.isTracking) return
    
    memoryMonitor.logMemory('ArrayBuffer转换开始', {
      bufferSize: Math.round(buffer.byteLength / 1024) + 'KB'
    })
  }

  /**
   * 追踪ArrayBuffer转换完成
   * @param {string} result - 转换结果
   */
  trackArrayBufferConversionEnd(result) {
    if (!this.isTracking) return
    
    memoryMonitor.logMemory('ArrayBuffer转换完成', {
      resultSize: Math.round(result.length / 1024) + 'KB',
      resultLength: result.length
    })
  }

  /**
   * 追踪JSON解析开始
   * @param {string} jsonString - JSON字符串
   */
  trackJSONParseStart(jsonString) {
    if (!this.isTracking) return
    
    memoryMonitor.logMemory('JSON解析开始', {
      jsonSize: Math.round(jsonString.length / 1024) + 'KB'
    })
  }

  /**
   * 追踪JSON解析完成
   * @param {Object} parsedData - 解析后的数据
   */
  trackJSONParseEnd(parsedData) {
    if (!this.isTracking) return
    
    let dataInfo = {}
    
    if (parsedData && typeof parsedData === 'object') {
      // 尝试获取数据详情
      if (parsedData.details && Array.isArray(parsedData.details)) {
        dataInfo.detailsCount = parsedData.details.length
      }
      
      // 估算对象大小
      try {
        const jsonStr = JSON.stringify(parsedData)
        dataInfo.estimatedSize = Math.round(jsonStr.length / 1024) + 'KB'
      } catch (e) {
        dataInfo.estimatedSize = '无法计算'
      }
    }
    
    memoryMonitor.logMemory('JSON解析完成', dataInfo)
  }

  /**
   * 追踪数据处理开始
   * @param {Object} data - 要处理的数据
   */
  trackDataProcessingStart(data) {
    if (!this.isTracking) return
    
    let dataInfo = {}
    
    if (data && data.details && Array.isArray(data.details)) {
      dataInfo.recordCount = data.details.length
      dataInfo.dataType = 'details'
    }
    
    memoryMonitor.logMemory('数据处理开始', dataInfo)
  }

  /**
   * 追踪数组操作
   * @param {string} operation - 操作类型 (slice, concat, spread等)
   * @param {Array} sourceArray - 源数组
   * @param {Array} targetArray - 目标数组
   */
  trackArrayOperation(operation, sourceArray, targetArray) {
    if (!this.isTracking) return
    
    memoryMonitor.logMemory(`数组操作: ${operation}`, {
      sourceLength: sourceArray ? sourceArray.length : 0,
      targetLength: targetArray ? targetArray.length : 0,
      operation
    })
  }

  /**
   * 追踪数据处理完成
   * @param {Object} result - 处理结果
   */
  trackDataProcessingEnd(result) {
    if (!this.isTracking) return
    
    memoryMonitor.logMemory('数据处理完成', {
      resultType: typeof result,
      hasDetails: result && result.details ? true : false,
      detailsCount: result && result.details ? result.details.length : 0
    })
  }

  /**
   * 追踪状态更新
   * @param {Object} newState - 新状态
   */
  trackStateUpdate(newState) {
    if (!this.isTracking) return
    
    let stateInfo = {
      step: newState.step
    }
    
    if (newState.details && Array.isArray(newState.details)) {
      stateInfo.detailsInState = newState.details.length
    }
    
    memoryMonitor.logMemory('状态更新', stateInfo)
  }

  /**
   * 追踪错误处理
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   */
  trackError(error, context) {
    if (!this.isTracking) return
    
    memoryMonitor.logMemory('错误处理', {
      errorType: error.name,
      errorMessage: error.message,
      context
    })
  }

  /**
   * 结束追踪
   */
  endTracking() {
    if (!this.isTracking) return
    
    const totalTime = Date.now() - this.importStartTime
    
    memoryMonitor.logMemory('Excel导入结束', {
      totalImportTime: totalTime + 'ms'
    })
    
    // 停止内存监控并生成报告
    const report = memoryMonitor.stopMonitoring()
    
    console.log('📊 Excel导入完成，总耗时:', totalTime + 'ms')
    console.groupEnd()
    
    this.isTracking = false
    this.importStartTime = null
    this.fileInfo = null
    
    return report
  }

  /**
   * 强制垃圾回收（仅在开发环境下有效）
   */
  forceGC() {
    if (window.gc && typeof window.gc === 'function') {
      memoryMonitor.logMemory('强制垃圾回收前')
      window.gc()
      setTimeout(() => {
        memoryMonitor.logMemory('强制垃圾回收后')
      }, 100)
    } else {
      console.log('⚠️ 垃圾回收功能不可用（需要在Chrome中使用 --js-flags="--expose-gc" 启动）')
    }
  }

  /**
   * 获取当前追踪状态
   */
  getTrackingStatus() {
    return {
      isTracking: this.isTracking,
      startTime: this.importStartTime,
      fileInfo: this.fileInfo,
      currentMemory: performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024 * 100) / 100,
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024 * 100) / 100
      } : null
    }
  }
}

// 创建全局实例
const excelImportTracker = new ExcelImportMemoryTracker()

// 挂载到window对象，方便调试
if (typeof window !== 'undefined') {
  window.excelImportTracker = excelImportTracker
}

export default excelImportTracker
