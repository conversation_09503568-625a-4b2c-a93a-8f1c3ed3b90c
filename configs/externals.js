//
module.exports = (config) => {
  if (config && config.patch && config.patch.vendors) {
    config.patch.vendors([
      'dist/*/eui-icons.js',
      ['@hose/eui-icons']
    ])

    config.patch.vendors([
      'dist/*/eui-icons-menu.js',
      ['@hose/eui-icons']
    ])

    config.patch.vendors([
      'umd/lib_ekuaibao_uploader.*.js',
      ['@ekuaibao/uploader']
    ])

    config.patch.vendors([
      'umd/lib_ekuaibao_uploader.*.css',
      ['@ekuaibao/uploader']
    ])

  }

  config.externals({
    '@hose/eui-icons': 'lib_eui_icons',
    '@ekuaibao/uploader': 'lib_ekuaibao_uploader',
  })
}
